neondb	pg_catalog	pg_statistic	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_type	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_foreign_table	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_authid	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_shadow	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_roles	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_statistic_ext_data	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_hba_file_rules	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_settings	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_file_settings	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_backend_memory_contexts	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_ident_file_mappings	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_config	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_shmem_allocations	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_user_mapping	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_statio_all_sequences	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_replication_origin_status	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_subscription	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_attribute	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_proc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_class	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_attrdef	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_statio_sys_sequences	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_statio_user_sequences	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_constraint	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_inherits	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_index	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_operator	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_opfamily	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_opclass	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_am	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_amop	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_amproc	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_language	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_largeobject_metadata	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_aggregate	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_statistic_ext	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_rewrite	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_trigger	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_event_trigger	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_description	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_cast	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_enum	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_namespace	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_conversion	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_depend	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_database	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_db_role_setting	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_tablespace	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_auth_members	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_shdepend	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_shdescription	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_ts_config	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_ts_config_map	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_ts_dict	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_ts_parser	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_ts_template	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_extension	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_foreign_data_wrapper	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_foreign_server	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_policy	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_replication_origin	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_default_acl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_init_privs	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_seclabel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_shseclabel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_collation	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_parameter_acl	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_partitioned_table	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_range	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_transform	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_sequence	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_publication	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_publication_namespace	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_publication_rel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_subscription_rel	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_group	VIEW	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_user	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_policies	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_rules	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_views	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_matviews	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_indexes	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_sequences	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stats	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stats_ext	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stats_ext_exprs	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_publication_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_locks	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_cursors	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_available_extensions	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_available_extension_versions	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_prepared_xacts	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_prepared_statements	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_seclabels	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_timezone_abbrevs	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_timezone_names	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_all_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_xact_all_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_xact_user_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_sys_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_xact_sys_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_user_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_statio_all_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_statio_sys_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_statio_user_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_all_indexes	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_sys_indexes	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_user_indexes	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_statio_all_indexes	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_statio_sys_indexes	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_statio_user_indexes	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_activity	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_replication	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_slru	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_wal_receiver	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_recovery_prefetch	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_subscription	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_ssl	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_gssapi	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_replication_slots	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_replication_slots	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_database	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_database_conflicts	VIEW	\N	\N	\N	\N	\N	YES	NO	\N
neondb	pg_catalog	pg_stat_user_functions	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_xact_user_functions	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_archiver	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_bgwriter	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_checkpointer	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_io	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_wal	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_progress_analyze	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_progress_vacuum	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_progress_cluster	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_progress_create_index	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_progress_basebackup	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_progress_copy	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_user_mappings	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_stat_subscription_stats	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_wait_events	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	pg_catalog	pg_largeobject	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	information_schema	column_column_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	information_schema_catalog_name	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	check_constraints	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	applicable_roles	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	administrable_role_authorizations	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	attributes	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	collations	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	character_sets	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	check_constraint_routine_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	column_privileges	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	collation_character_set_applicability	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	column_domain_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	column_udt_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	columns	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	constraint_column_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	constraint_table_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	domain_constraints	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	routine_table_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	domain_udt_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	domains	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	enabled_roles	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	routines	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	key_column_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	parameters	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	referential_constraints	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	schemata	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	role_column_grants	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	routine_column_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	sql_parts	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	information_schema	routine_privileges	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	sequences	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	role_routine_grants	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	routine_routine_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	routine_sequence_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	sql_features	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	information_schema	sql_implementation_info	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	information_schema	role_table_grants	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	sql_sizing	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	information_schema	table_privileges	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	table_constraints	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	transforms	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	triggered_update_columns	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	triggers	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	udt_privileges	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	_pg_foreign_data_wrappers	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	role_udt_grants	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	usage_privileges	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	foreign_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	role_usage_grants	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	foreign_data_wrapper_options	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	user_defined_types	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	view_column_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	view_routine_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	foreign_data_wrappers	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	view_table_usage	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	views	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	_pg_foreign_servers	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	data_type_privileges	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	element_types	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	_pg_foreign_table_columns	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	_pg_user_mappings	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	column_options	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	foreign_server_options	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	foreign_servers	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	_pg_foreign_tables	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	foreign_table_options	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	user_mapping_options	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	information_schema	user_mappings	VIEW	\N	\N	\N	\N	\N	NO	NO	\N
neondb	public	_prisma_migrations	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	VerificationToken	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Account	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Session	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	MonthlyPlanning	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Content	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	WeeklyActivity	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	User	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Feedback	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	ResultsReport	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Notification	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	LooseClient	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	ContentStep	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	GeneralDemand	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Calendar	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Event	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Address	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	HolidayEvent	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Owner	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Result	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
neondb	public	Client	BASE TABLE	\N	\N	\N	\N	\N	YES	NO	\N
