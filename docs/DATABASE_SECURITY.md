# 🛡️ Guia de segurança do banco de dados

## 🚨 **REGRAS FUNDAMENTAIS**

### ❌ **NUNCA FAÇA ISSO:**
- `pnpm exec prisma migrate reset` em produção
- Edite `.env` diretamente para mudar ambiente
- Execute comandos destrutivos sem backup
- Use comandos de desenvolvimento em produção

### ✅ **SEMPRE FAÇA ISSO:**
- Use os scripts seguros (`pnpm run db:*`)
- Faça backup antes de mudanças críticas
- Confirme o ambiente antes de executar comandos
- Use ambientes separados para dev/prod

---

## 📁 **Estrutura de arquivos de ambiente**

```
.env                 # ⚠️  Ambiente ativo (não edite diretamente!)
.env.development     # 🟢 Configuração para desenvolvimento
.env.production      # 🔴 Configuração para produção
```

---

## 🔧 **Comandos seguros**

### **Configuração de ambiente:**
```bash
# Para desenvolvimento
pnpm run setup:dev

# Para produção (cuidado!)
pnpm run setup:prod
```

### **Operações de banco (Desenvolvimento):**
```bash
pnpm run db:migrate    # Executar migração
pnpm run db:reset      # Reset (apenas dev)
pnpm run db:studio     # Abrir Prisma Studio
pnpm run db:status     # Ver status das migrações
pnpm run db:seed       # Executar seed
pnpm run db:generate   # Gerar cliente Prisma
```

### **Backup e restauração:**
```bash
pnpm run backup:create    # Criar backup do banco de produção
pnpm run backup:restore   # Restaurar backup (apenas dev)
```

### **Produção (COM CUIDADO):**
```bash
pnpm run migrate:prod     # Migração em produção (pede confirmação)
pnpm run reset:prod       # ❌ BLOQUEADO (previne acidentes)
```

---

## 🔄 **Fluxo de trabalho seguro**

### **1. Desenvolvimento local:**
```bash
# 1. Configurar ambiente de desenvolvimento
pnpm run setup:dev

# 2. Verificar status
pnpm run db:status

# 3. Executar migrações
pnpm run db:migrate

# 4. Testar mudanças
pnpm run dev
```

### **2. Deploy para produção:**
```bash
# 1. Fazer backup antes de qualquer mudança
pnpm run backup:create

# 2. Configurar ambiente de produção
pnpm run setup:prod

# 3. Executar migração (com confirmação)
pnpm run migrate:prod

# 4. Verificar resultado
pnpm run db:status
```

---

## 🚨 **Sistema de proteção**

### **Script de proteção (`scripts/safe-db.sh`):**
- ✅ Verifica se você está em desenvolvimento
- ❌ Bloqueia comandos perigosos em produção
- 🔍 Analisa o `.env` antes de executar

### **Detecção de ambiente:**
- **Desenvolvimento**: `localhost:5433`
- **Produção**: `neon.tech`
- **Erro**: Qualquer outro ambiente

### **Confirmações obrigatórias:**
- Migração em produção: requer digitar "CONFIRMO"
- Reset em produção: BLOQUEADO
- Restauração: requer seleção manual do backup

---

## 💾 **Sistema de backup**

### **Backup automático:**
- 📁 Local: `./backups/`
- 🕐 Retenção: 30 dias
- 📝 Formato: `b4desk_backup_YYYYMMDD_HHMMSS.sql`

### **Como usar:**
```bash
# Criar backup
pnpm run backup:create

# Listar e restaurar backup
pnpm run backup:restore
```

---

## 🔧 **Recuperação de emergência**

### **Se perder dados em produção:**

1. **Verificar backups no Neon.tech:**
   - Acesse: https://console.neon.tech/
   - Procure por "Backups" ou "Point in Time Recovery"

2. **Contatar suporte:**
   - Email: <EMAIL>
   - Discord: https://discord.gg/neon

3. **Usar backup local:**
   ```bash
   pnpm run backup:restore
   ```

---

## 📝 **Checklist antes de migrações**

### **Desenvolvimento:**
- [ ] Estou usando `.env.development`?
- [ ] O Docker está rodando?
- [ ] Testei localmente?

### **Produção:**
- [ ] Fiz backup recente?
- [ ] Testei a migração em desenvolvimento?
- [ ] Tenho plano de rollback?
- [ ] Equipe está ciente?
- [ ] Estou usando `pnpm run migrate:prod`?

---

## 🚨 **Em caso de emergência**

### **Contatos:**
- **Suporte Neon.tech**: <EMAIL>
- **Discord Neon**: https://discord.gg/neon

### **Arquivos importantes:**
- `backups/` - Backups locais
- `.env.production` - Config de produção
- `scripts/backup.sh` - Script de backup

### **Logs importantes:**
- Console do Neon.tech
- Logs da aplicação
- Historico de migrações: `_prisma_migrations`

---

## **Boas práticas**

1. **Sempre use os scripts `pnpm run`**
2. **Backup antes de mudanças críticas**
3. **Teste tudo em desenvolvimento primeiro**
4. **Mantenha ambientes separados**
5. **Documente mudanças importantes**
6. **Configure monitoramento de banco**
7. **Revise migrações antes de aplicar**
8. **Tenha plano de rollback**

---

## **Suporte**

Se encontrar problemas ou dúvidas sobre este sistema de proteção, verifique:

1. Logs dos scripts em `scripts/`
2. Estado do banco com `pnpm run db:status`
3. Arquivos de backup em `backups/`
4. Esta documentação

**Lembre-se: É melhor ser cauteloso demais do que perder dados!**
