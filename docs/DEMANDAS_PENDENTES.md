# PADRONIZAÇÃO DE CRITÉRIOS PARA DEMANDAS PENDENTES

Este documento explica as alterações feitas para padronizar a forma como as demandas pendentes são identificadas em todo o sistema.

## Problema

Havia uma discrepância na contagem de demandas pendentes entre diferentes componentes do sistema:

1. <PERSON> página principal (`UserDemandsStats`), a contagem incluía todas as demandas não concluídas
2. Na página de Minhas Demandas (`MyDemandsPage`), a exibição era filtrada por status específicos
3. Na API de contagem de demandas (`/api/demands/pending-count`), apenas as demandas não marcadas como "concluído" eram contadas

## Solução

Criamos uma função utilitária centralizada para padronizar a verificação de demandas pendentes:

```typescript
// Em src/lib/utils.ts
export function isPendingDemand(demand: { status?: string; archived?: boolean }) {
  const pendingStatuses = [
    "pendente",
    "em andamento",
    "estruturação de feed",
    "feed estruturado",
    "repassado",
    "alteração",
  ];
  return pendingStatuses.includes(demand.status || '') && !demand.archived;
}
```

E atualizamos todos os componentes para usar esta função, garantindo consistência:

1. `MyDemandsPage` - Filtro de status pendentes
2. `UserDemandsStats` - Contagem de demandas pendentes
3. `UserLatestDemands` - Exibição de demandas pendentes recentes
4. `API /demands/pending-count` - Contagem total de demandas pendentes

## Status considerados "Pendentes"

Os seguintes status são considerados como demandas pendentes:
- pendente
- em andamento
- estruturação de feed
- feed estruturado
- repassado
- alteração

## Status considerados "Concluídos"

Os seguintes status são considerados como demandas concluídas:
- concluído
- captado
- pend. captação
- em revisão
- anúncio concluído

## Benefícios

- Consistência na contagem e exibição de demandas pendentes em todo o sistema
- Manutenção facilitada através da centralização da lógica de negócio
- Experiência do usuário melhorada com dados consistentes entre páginas
