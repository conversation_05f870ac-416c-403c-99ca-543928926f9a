# Como usar o AddFeedUrlModal atualizado

## Mudanças principais

O componente `AddFeedUrlModal` foi atualizado para suportar múltiplas URLs. As principais mudanças são:

### Props

- `currentUrl` agora aceita `string | string[]` (antes era apenas `string`)
- `onUrlUpdated` agora recebe `(contentId: string, newUrls: string[])` (antes era `(contentId: string, newUrl: string)`)

### Interface

```typescript
interface AddFeedUrlModalProps {
  contentId: string;
  currentUrl?: string | string[];
  onUrlUpdated: (contentId: string, newUrls: string[]) => void;
  type?: 'content' | 'general';
}
```

## Exemplo de uso

```tsx
import { AddFeedUrlModal } from '@/app/components/add-feed-url-modal';

// Estado que pode conter múltiplas URLs
const [content, setContent] = useState({
  id: 'content-1',
  urlStructuringFeed: ['https://drive.google.com/file/d/abc123', 'https://drive.google.com/file/d/def456']
});

// Função para atualizar URLs
const handleUrlUpdate = (contentId: string, newUrls: string[]) => {
  setContent(prevContent => ({
    ...prevContent,
    urlStructuringFeed: newUrls.length > 0 ? newUrls : undefined
  }));
};

// Uso do componente
<AddFeedUrlModal
  contentId={content.id}
  currentUrl={content.urlStructuringFeed}
  onUrlUpdated={handleUrlUpdate}
  type="content"
/>
```

## Funcionalidades

### 1. Adicionar URLs
- Clique no botão "Adicionar URL" para adicionar uma nova URL
- Cada URL é validada para garantir que seja do Google Drive

### 2. Remover URLs
- Clique no ícone de lixeira ao lado de cada URL para removê-la
- Sempre deve haver pelo menos uma URL (mesmo que vazia)

### 3. Visualização
- A URL selecionada (destacada em azul) é exibida na área de visualização
- Clique em uma URL para selecioná-la e ver sua visualização

### 4. Navegação entre URLs
- Use o foco nos campos de input para alternar entre URLs
- A visualização é atualizada automaticamente conforme você navega

## Compatibilidade com código existente

O componente mantém compatibilidade retroativa:
- Se `currentUrl` for uma string, ela será tratada como uma única URL
- O backend deve ser atualizado para aceitar arrays de URLs no campo `urlStructuringFeed`

## Modelo de dados atualizado

```typescript
interface Content {
  id: string;
  // ... outros campos
  urlStructuringFeed?: string | string[]; // Agora aceita arrays
}
```
