# Configuração da API do Google Drive

Este documento descreve como configurar a API do Google Drive para o projeto B4Desk.

## Pré-requisitos

- Ter um projeto no Google Cloud Console
- Ter uma conta de serviço configurada no projeto

## Passos para configuração

### 1. Acessar o Google Cloud Console

1. Acesse [console.cloud.google.com](https://console.cloud.google.com)
2. Selecione o projeto que você já criou ou crie um novo projeto

### 2. Habilitar a API do Google Drive

1. No menu lateral, vá para "APIs e Serviços" > "Biblioteca"
2. Pesquise por "Google Drive API"
3. Clique na API do Google Drive e depois em "Habilitar"

### 3. Criar uma conta de serviço

1. No menu lateral, vá para "APIs e Serviços" > "Credenciais"
2. Clique em "Criar credenciais" e selecione "Conta de serviço"
3. Preencha os detalhes da conta de serviço:
   - Nome da conta de serviço: `b4desk-drive-service`
   - ID da conta de serviço: será gerado automaticamente
   - Descrição: `Conta de serviço para acesso à API do Google Drive`
4. Clique em "Concluir"

### 4. Criar uma chave para a conta de serviço

1. Na lista de contas de serviço, clique na conta que você acabou de criar
2. Vá para a aba "Chaves"
3. Clique em "Adicionar chave" > "Criar nova chave"
4. Selecione o formato "JSON" e clique em "Criar"
5. Um arquivo JSON será baixado automaticamente - guarde-o em um local seguro

### 5. Configurar as variáveis de ambiente

Adicione as seguintes variáveis ao arquivo `.env`:

```
GOOGLE_SERVICE_ACCOUNT_EMAIL="<EMAIL>"
GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nSua chave privada aqui\n-----END PRIVATE KEY-----\n"
```

Onde:
- `GOOGLE_SERVICE_ACCOUNT_EMAIL`: é o email da conta de serviço (encontrado no arquivo JSON baixado)
- `GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY`: é a chave privada da conta de serviço (encontrada no arquivo JSON baixado)

### 6. Compartilhar arquivos do Google Drive com a conta de serviço

Para que a API possa acessar os arquivos do Google Drive, é necessário compartilhar os arquivos ou pastas com o email da conta de serviço:

1. No Google Drive, clique com o botão direito no arquivo ou pasta que deseja compartilhar
2. Clique em "Compartilhar"
3. Adicione o email da conta de serviço e dê permissão de "Leitor"
4. Clique em "Compartilhar"

## Testando a integração

Após configurar a API, você pode testar a integração acessando uma URL de imagem do Google Drive através do endpoint `/api/drive-proxy`:

```
/api/drive-proxy?id=ID_DO_ARQUIVO&quality=high&size=large
```

Onde:
- `ID_DO_ARQUIVO`: é o ID do arquivo no Google Drive
- `quality`: pode ser `low`, `medium` ou `high` (padrão: `high`)
- `size`: pode ser `small`, `medium`, `large` ou `xlarge` (padrão: `large`)

## Solução de problemas

Se você encontrar problemas ao acessar os arquivos do Google Drive, verifique:

1. Se a API do Google Drive está habilitada no projeto
2. Se as credenciais da conta de serviço estão corretas no arquivo `.env`
3. Se os arquivos foram compartilhados com a conta de serviço
4. Se os arquivos são imagens (o endpoint `/api/drive-proxy` só funciona com imagens)

Em caso de falha na API oficial, o sistema tentará usar o método alternativo de thumbnail público.
