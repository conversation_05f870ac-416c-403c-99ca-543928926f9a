import withPWA from 'next-pwa';
import type { NextConfig } from 'next';

const pwa = withPWA({
  dest: 'public',
  register: true,
  skipWaiting: true,
  fallbacks: { document: '/offline.html' },
  disable: process.env.NODE_ENV === 'development',
  cacheOnFrontEndNav: true,
  publicExcludes: ['!icon-b4desk-*.png'],
});

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'drive.google.com',
        pathname: '/**',
      },
    ],
  },
};

export default pwa(nextConfig);
