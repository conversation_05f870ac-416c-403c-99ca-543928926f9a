-- CreateTable
CREATE TABLE "MonthlyPlanning" (
    "id" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,

    CONSTRAINT "MonthlyPlanning_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WeeklyActivity" (
    "id" TEXT NOT NULL,
    "monthlyPlanningId" TEXT NOT NULL,
    "week" INTEGER NOT NULL,
    "activityDate" TIMESTAMP(3) NOT NULL,
    "content" TEXT NOT NULL,
    "contentType" TEXT NOT NULL,
    "channel" TEXT NOT NULL,
    "description" TEXT NOT NULL,

    CONSTRAINT "WeeklyActivity_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "MonthlyPlanning_clientId_month_year_key" ON "MonthlyPlanning"("clientId", "month", "year");

-- AddForeignKey
ALTER TABLE "MonthlyPlanning" ADD CONSTRAINT "MonthlyPlanning_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WeeklyActivity" ADD CONSTRAINT "WeeklyActivity_monthlyPlanningId_fkey" FOREIGN KEY ("monthlyPlanningId") REFERENCES "MonthlyPlanning"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
