/*
  Warnings:

  - You are about to drop the column `activityDate` on the `WeeklyActivity` table. All the data in the column will be lost.
  - You are about to drop the column `channel` on the `WeeklyActivity` table. All the data in the column will be lost.
  - You are about to drop the column `content` on the `WeeklyActivity` table. All the data in the column will be lost.
  - You are about to drop the column `contentType` on the `WeeklyActivity` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[monthlyPlanningId,week]` on the table `WeeklyActivity` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "WeeklyActivity" DROP COLUMN "activityDate",
DROP COLUMN "channel",
DROP COLUMN "content",
DROP COLUMN "contentType";

-- CreateTable
CREATE TABLE "Content" (
    "id" TEXT NOT NULL,
    "weeklyActivityId" TEXT NOT NULL,
    "activityDate" TIMESTAMP(3) NOT NULL,
    "contentType" TEXT NOT NULL,
    "channel" TEXT NOT NULL,
    "details" TEXT NOT NULL,

    CONSTRAINT "Content_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "WeeklyActivity_monthlyPlanningId_week_key" ON "WeeklyActivity"("monthlyPlanningId", "week");

-- AddForeignKey
ALTER TABLE "Content" ADD CONSTRAINT "Content_weeklyActivityId_fkey" FOREIGN KEY ("weeklyActivityId") REFERENCES "WeeklyActivity"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
