/*
  Warnings:

  - You are about to drop the column `email` on the `Client` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[instagramUsername]` on the table `Client` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `instagramUsername` to the `Client` table without a default value. This is not possible if the table is not empty.
  - Added the required column `phone` to the `Client` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "Client" DROP COLUMN "email",
ADD COLUMN     "instagramUsername" TEXT NOT NULL,
ADD COLUMN     "phone" TEXT NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "Client_instagramUsername_key" ON "Client"("instagramUsername");
