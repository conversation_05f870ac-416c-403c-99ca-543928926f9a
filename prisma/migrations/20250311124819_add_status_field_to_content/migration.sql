/*
  Warnings:

  - Added the required column `updatedAt` to the `Content` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Content" DROP CONSTRAINT "Content_weeklyActivityId_fkey";

-- AlterTable
ALTER TABLE "Content" ADD COLUMN "status" TEXT DEFAULT 'pendente';
ALTER TABLE "Content" ADD COLUMN "updatedAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "Content" ALTER COLUMN "updatedAt" SET NOT NULL;
ALTER TABLE "Content" ALTER COLUMN "channel" SET DEFAULT 'Feed';

-- CreateIndex
CREATE INDEX "Content_weeklyActivityId_idx" ON "Content"("weeklyActivityId");

-- AddForeignKey
ALTER TABLE "Content" ADD CONSTRAINT "Content_weeklyActivityId_fkey" FOREIGN KEY ("weeklyActivityId") REFERENCES "WeeklyActivity"("id") ON DELETE CASCADE ON UPDATE CASCADE;
