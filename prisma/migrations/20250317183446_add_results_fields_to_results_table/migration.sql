-- CreateTable
CREATE TABLE "ResultsReport" (
    "id" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "month" INTEGER NOT NULL,
    "year" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ResultsReport_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Result" (
    "id" TEXT NOT NULL,
    "resultsReportId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "totalFollowers" INTEGER,
    "totalPosts" INTEGER,
    "totalInteractions" INTEGER,
    "totalStories" INTEGER,
    "followersGrowthFacebook" INTEGER,
    "followersGrowthInstagram" INTEGER,
    "interactionsDistribution" JSONB,
    "averageInteractionsPerPost" INTEGER,
    "totalReach" INTEGER,
    "totalImpressions" INTEGER,
    "reachByPostType" JSONB,
    "averageStoryViews" INTEGER,
    "storyCompletionRate" DOUBLE PRECISION,
    "feedCompletionRate" DOUBLE PRECISION,
    "staticStories" INTEGER,
    "animatedStories" INTEGER,
    "staticFeed" INTEGER,
    "manualFeed" INTEGER,
    "extraMaterials" INTEGER,

    CONSTRAINT "Result_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Result_resultsReportId_idx" ON "Result"("resultsReportId");

-- AddForeignKey
ALTER TABLE "ResultsReport" ADD CONSTRAINT "ResultsReport_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Result" ADD CONSTRAINT "Result_resultsReportId_fkey" FOREIGN KEY ("resultsReportId") REFERENCES "ResultsReport"("id") ON DELETE CASCADE ON UPDATE CASCADE;
