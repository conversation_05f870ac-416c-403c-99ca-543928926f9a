-- CreateTable
CREATE TABLE "GeneralDemand" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "dueDate" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'pendente',
    "priority" TEXT NOT NULL DEFAULT 'normal',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "clientId" TEXT NOT NULL,
    "assignedToId" TEXT,

    CONSTRAINT "GeneralDemand_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "GeneralDemand_clientId_idx" ON "GeneralDemand"("clientId");

-- CreateIndex
CREATE INDEX "GeneralDemand_assignedToId_idx" ON "GeneralDemand"("assignedToId");

-- AddForeignKey
ALTER TABLE "GeneralDemand" ADD CONSTRAINT "GeneralDemand_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GeneralDemand" ADD CONSTRAINT "GeneralDemand_assignedToId_fkey" FOREIGN KEY ("assignedToId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
