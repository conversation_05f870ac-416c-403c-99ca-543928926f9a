-- DropFore<PERSON><PERSON>ey
ALTER TABLE "GeneralDemand" DROP CONSTRAINT "GeneralDemand_clientId_fkey";

-- AlterTable
ALTER TABLE "GeneralDemand" ADD COLUMN     "looseClientId" TEXT,
ALTER COLUMN "clientId" DROP NOT NULL;

-- CreateTable
CREATE TABLE "LooseClient" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "LooseClient_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "GeneralDemand_looseClientId_idx" ON "GeneralDemand"("looseClientId");

-- AddForeignKey
ALTER TABLE "GeneralDemand" ADD CONSTRAINT "GeneralDemand_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "GeneralDemand" ADD CONSTRAINT "GeneralDemand_looseClientId_fkey" FOREIGN KEY ("looseClientId") REFERENCES "LooseClient"("id") ON DELETE SET NULL ON UPDATE CASCADE;
