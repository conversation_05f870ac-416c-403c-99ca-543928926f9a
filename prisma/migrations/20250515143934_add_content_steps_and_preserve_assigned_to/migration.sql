-- CreateEnum
CREATE TYPE "StepType" AS ENUM ('CAP<PERSON>CAO', 'DESIG<PERSON>', 'EDICAO', 'TRAFEGO');

-- CreateTable
CREATE TABLE "ContentStep" (
    "id" TEXT NOT NULL,
    "contentId" TEXT NOT NULL,
    "type" "StepType" NOT NULL,
    "assignedToId" TEXT NOT NULL,

    CONSTRAINT "ContentStep_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ContentStep_contentId_idx" ON "ContentStep"("contentId");

-- CreateIndex
CREATE INDEX "ContentStep_assignedToId_idx" ON "ContentStep"("assignedToId");

-- CreateIndex
CREATE UNIQUE INDEX "ContentStep_contentId_type_key" ON "ContentStep"("contentId", "type");

-- AddForeignKey
ALTER TABLE "ContentStep" ADD CONSTRAINT "ContentStep_assignedToId_fkey" FOREIGN KEY ("assignedToId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ContentStep" ADD CONSTRAINT "ContentStep_contentId_fkey" FOREIGN KEY ("contentId") REFERENCES "Content"("id") ON DELETE CASCADE ON UPDATE CASCADE;
