/*
  Warnings:

  - You are about to drop the column `factoryAddress` on the `Client` table. All the data in the column will be lost.
  - You are about to drop the column `storeAddress` on the `Client` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[storeAddressId]` on the table `Client` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[factoryAddressId]` on the table `Client` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Client" DROP COLUMN "factoryAddress",
DROP COLUMN "storeAddress",
ADD COLUMN     "factoryAddressId" TEXT,
ADD COLUMN     "storeAddressId" TEXT;

-- CreateTable
CREATE TABLE "Address" (
    "id" TEXT NOT NULL,
    "zipCode" TEXT,
    "street" TEXT,
    "number" TEXT,
    "neighborhood" TEXT,
    "city" TEXT,
    "state" TEXT,
    "country" TEXT DEFAULT 'Brasil',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Address_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Client_storeAddressId_key" ON "Client"("storeAddressId");

-- CreateIndex
CREATE UNIQUE INDEX "Client_factoryAddressId_key" ON "Client"("factoryAddressId");

-- AddForeignKey
ALTER TABLE "Client" ADD CONSTRAINT "Client_storeAddressId_fkey" FOREIGN KEY ("storeAddressId") REFERENCES "Address"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Client" ADD CONSTRAINT "Client_factoryAddressId_fkey" FOREIGN KEY ("factoryAddressId") REFERENCES "Address"("id") ON DELETE SET NULL ON UPDATE CASCADE;
