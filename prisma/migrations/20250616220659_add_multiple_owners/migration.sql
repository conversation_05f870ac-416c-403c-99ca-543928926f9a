/*
  Warnings:

  - You are about to drop the column `cpf` on the `Client` table. All the data in the column will be lost.
  - You are about to drop the column `ownerBirthday` on the `Client` table. All the data in the column will be lost.
  - You are about to drop the column `ownerName` on the `Client` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "Client" DROP COLUMN "cpf",
DROP COLUMN "ownerBirthday",
DROP COLUMN "ownerName";

-- CreateTable
CREATE TABLE "Owner" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "cpf" TEXT,
    "birthDate" TIMESTAMP(3),
    "rg" TEXT,
    "issuingAgency" TEXT,
    "maritalStatus" TEXT,
    "nationality" TEXT DEFAULT 'Brazilian',
    "profession" TEXT,
    "clientId" TEXT NOT NULL,
    "addressId" TEXT,
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Owner_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Owner_addressId_key" ON "Owner"("addressId");

-- CreateIndex
CREATE INDEX "Owner_clientId_idx" ON "Owner"("clientId");

-- AddForeignKey
ALTER TABLE "Owner" ADD CONSTRAINT "Owner_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Owner" ADD CONSTRAINT "Owner_addressId_fkey" FOREIGN KEY ("addressId") REFERENCES "Address"("id") ON DELETE SET NULL ON UPDATE CASCADE;
