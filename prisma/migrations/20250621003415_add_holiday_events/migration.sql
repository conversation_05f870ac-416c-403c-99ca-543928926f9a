-- CreateTable
CREATE TABLE "HolidayEvent" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "month" INTEGER NOT NULL,
    "day" INTEGER NOT NULL,
    "color" TEXT DEFAULT '#FF0000',
    "allDay" BOOLEAN NOT NULL DEFAULT true,
    "isRecurring" BOOLEAN NOT NULL DEFAULT true,
    "calendarId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "HolidayEvent_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "HolidayEvent_calendarId_idx" ON "HolidayEvent"("calendarId");

-- CreateIndex
CREATE INDEX "HolidayEvent_month_day_idx" ON "HolidayEvent"("month", "day");

-- AddForeignKey
ALTER TABLE "HolidayEvent" ADD CONSTRAINT "HolidayEvent_calendarId_fkey" FOREIGN KEY ("calendarId") REFERENCES "Calendar"("id") ON DELETE CASCADE ON UPDATE CASCADE;
