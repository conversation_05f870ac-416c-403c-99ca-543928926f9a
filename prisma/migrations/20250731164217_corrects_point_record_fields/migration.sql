/*
  Warnings:

  - You are about to drop the column `points` on the `PointRecord` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId,date]` on the table `PointRecord` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `date` to the `PointRecord` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `PointRecord` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "PointRecord" DROP COLUMN "points",
ADD COLUMN     "clockIn" TIMESTAMP(3),
ADD COLUMN     "clockOut" TIMESTAMP(3),
ADD COLUMN     "date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "totalHours" DOUBLE PRECISION,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL;

-- CreateIndex
CREATE INDEX "PointRecord_date_idx" ON "PointRecord"("date");

-- CreateIndex
CREATE UNIQUE INDEX "PointRecord_userId_date_key" ON "PointRecord"("userId", "date");
