-- CreateTable
CREATE TABLE "PointRecordAudit" (
    "id" TEXT NOT NULL,
    "pointRecordId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "field" TEXT,
    "oldValue" TEXT,
    "newValue" TEXT,
    "editedBy" TEXT NOT NULL,
    "editedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PointRecordAudit_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "PointRecordAudit_pointRecordId_idx" ON "PointRecordAudit"("pointRecordId");

-- CreateIndex
CREATE INDEX "PointRecordAudit_editedBy_idx" ON "PointRecordAudit"("editedBy");

-- AddForeignKey
ALTER TABLE "PointRecordAudit" ADD CONSTRAINT "PointRecordAudit_pointRecordId_fkey" FOREIGN KEY ("pointRecordId") REFERENCES "PointRecord"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PointRecordAudit" ADD CONSTRAINT "PointRecordAudit_editedBy_fkey" FOREIGN KEY ("editedBy") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
