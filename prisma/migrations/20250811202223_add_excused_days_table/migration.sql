-- CreateTable
CREATE TABLE "ExcusedDay" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "date" TIMESTAMP(3) NOT NULL,
    "reason" TEXT NOT NULL,
    "createdBy" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ExcusedDay_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ExcusedDay_userId_idx" ON "ExcusedDay"("userId");

-- CreateIndex
CREATE INDEX "ExcusedDay_date_idx" ON "ExcusedDay"("date");

-- CreateIndex
CREATE UNIQUE INDEX "ExcusedDay_userId_date_key" ON "ExcusedDay"("userId", "date");

-- AddForeignKey
ALTER TABLE "ExcusedDay" ADD CONSTRAINT "ExcusedDay_userId_fkey" FOREIG<PERSON> KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add<PERSON><PERSON>ignKey
ALTER TABLE "ExcusedDay" ADD CONSTRAINT "ExcusedDay_createdBy_fkey" FOREIGN KEY ("createdBy") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;
