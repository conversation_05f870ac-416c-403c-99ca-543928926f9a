-- CreateTable
CREATE TABLE "Quote" (
    "id" TEXT NOT NULL,
    "client" TEXT NOT NULL,
    "clientPhoneNumber" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Quote_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Service" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,

    CONSTRAINT "Service_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "QuoteItem" (
    "id" TEXT NOT NULL,
    "quoteId" TEXT NOT NULL,
    "serviceId" TEXT,
    "budgetItemId" TEXT,
    "include" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "customLabel" TEXT,
    "customDescription" TEXT,
    "price" DECIMAL(12,2),
    "quantity" INTEGER DEFAULT 1,
    "discount" DECIMAL(12,2),
    "notes" TEXT,

    CONSTRAINT "QuoteItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MarketingManagementBudget" (
    "id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "planningMonthlyQuantity" INTEGER DEFAULT 1,
    "planningMonthlyPrepHoursMin" DOUBLE PRECISION,
    "planningMonthlyPrepHoursMax" DOUBLE PRECISION,
    "planningMonthlyMeetingHours" DOUBLE PRECISION,
    "planningMonthlyDelivery" TEXT,
    "calendarQuantity" INTEGER,
    "calendarHoursMin" DOUBLE PRECISION,
    "calendarHoursMax" DOUBLE PRECISION,
    "calendarDelivery" TEXT,
    "captureSessionsMin" INTEGER,
    "captureSessionsMax" INTEGER,
    "captureSuggestedSessions" INTEGER,
    "captureHoursMin" DOUBLE PRECISION,
    "captureHoursHalfDay" DOUBLE PRECISION,
    "captureHoursFullDay" DOUBLE PRECISION,
    "captureDelivery" TEXT,
    "campaignsAlignmentQuantity" INTEGER,
    "campaignsAlignmentHoursMin" DOUBLE PRECISION,
    "campaignsAlignmentHoursMax" DOUBLE PRECISION,
    "campaignsAlignmentDelivery" TEXT,
    "socialFeedMin" INTEGER,
    "socialFeedMax" INTEGER,
    "socialStoriesMin" INTEGER,
    "socialStoriesMax" INTEGER,
    "socialReelsMin" INTEGER,
    "socialReelsMax" INTEGER,
    "socialHoursPerWeekMin" DOUBLE PRECISION,
    "socialHoursPerWeekMax" DOUBLE PRECISION,
    "socialDelivery" TEXT,
    "extraMaterialsMin" INTEGER,
    "extraMaterialsMax" INTEGER,
    "extraMaterialsHoursPerPieceMin" DOUBLE PRECISION,
    "extraMaterialsHoursPerPieceMax" DOUBLE PRECISION,
    "extraMaterialsDelivery" TEXT,
    "paidCampaignsMin" INTEGER,
    "paidCampaignsMax" INTEGER,
    "paidSetupHoursMin" DOUBLE PRECISION,
    "paidSetupHoursMax" DOUBLE PRECISION,
    "paidOptimizationHoursPerWeekMin" DOUBLE PRECISION,
    "paidOptimizationHoursPerWeekMax" DOUBLE PRECISION,
    "paidDelivery" TEXT,
    "tiktokVideosMin" INTEGER,
    "tiktokVideosMax" INTEGER,
    "tiktokEditHoursPerVideoMin" DOUBLE PRECISION,
    "tiktokEditHoursPerVideoMax" DOUBLE PRECISION,
    "tiktokDelivery" TEXT,
    "ecommerceBannersMin" INTEGER,
    "ecommerceBannersMax" INTEGER,
    "ecommerceProductsMin" INTEGER,
    "ecommerceProductsMax" INTEGER,
    "ecommerceHoursPerBanner" DOUBLE PRECISION,
    "ecommerceMinutesPerProduct" DOUBLE PRECISION,
    "ecommerceDelivery" TEXT,
    "endomarketingActionsMin" INTEGER,
    "endomarketingActionsMax" INTEGER,
    "endomarketingHoursPerActionMin" DOUBLE PRECISION,
    "endomarketingHoursPerActionMax" DOUBLE PRECISION,
    "endomarketingDelivery" TEXT,
    "dashboardQuantity" INTEGER,
    "dashboardHoursMin" DOUBLE PRECISION,
    "dashboardHoursMax" DOUBLE PRECISION,
    "dashboardDelivery" TEXT,
    "alignmentMeetingsQuantity" INTEGER,
    "alignmentMeetingsHoursMin" DOUBLE PRECISION,
    "alignmentMeetingsHoursMax" DOUBLE PRECISION,
    "alignmentMeetingsDelivery" TEXT,
    "competitorAnalysisQuantityMin" INTEGER,
    "competitorAnalysisQuantityMax" INTEGER,
    "competitorAnalysisHoursMin" DOUBLE PRECISION,
    "competitorAnalysisHoursMax" DOUBLE PRECISION,
    "competitorAnalysisDelivery" TEXT,
    "multichannelContinuous" BOOLEAN DEFAULT true,
    "multichannelHoursPerWeekMin" DOUBLE PRECISION,
    "multichannelHoursPerWeekMax" DOUBLE PRECISION,
    "multichannelDelivery" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MarketingManagementBudget_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "BudgetItem" (
    "id" TEXT NOT NULL,
    "marketingBudgetId" TEXT NOT NULL,
    "code" TEXT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "defaultPrice" DECIMAL(12,2),
    "include" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BudgetItem_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Quote_createdAt_idx" ON "Quote"("createdAt");

-- CreateIndex
CREATE UNIQUE INDEX "Service_code_key" ON "Service"("code");

-- CreateIndex
CREATE INDEX "QuoteItem_budgetItemId_idx" ON "QuoteItem"("budgetItemId");

-- CreateIndex
CREATE UNIQUE INDEX "QuoteItem_quoteId_serviceId_key" ON "QuoteItem"("quoteId", "serviceId");

-- CreateIndex
CREATE UNIQUE INDEX "QuoteItem_quoteId_budgetItemId_key" ON "QuoteItem"("quoteId", "budgetItemId");

-- CreateIndex
CREATE UNIQUE INDEX "MarketingManagementBudget_code_key" ON "MarketingManagementBudget"("code");

-- CreateIndex
CREATE INDEX "MarketingManagementBudget_code_idx" ON "MarketingManagementBudget"("code");

-- CreateIndex
CREATE UNIQUE INDEX "BudgetItem_code_key" ON "BudgetItem"("code");

-- CreateIndex
CREATE INDEX "BudgetItem_marketingBudgetId_idx" ON "BudgetItem"("marketingBudgetId");

-- AddForeignKey
ALTER TABLE "QuoteItem" ADD CONSTRAINT "QuoteItem_serviceId_fkey" FOREIGN KEY ("serviceId") REFERENCES "Service"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QuoteItem" ADD CONSTRAINT "QuoteItem_budgetItemId_fkey" FOREIGN KEY ("budgetItemId") REFERENCES "BudgetItem"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "QuoteItem" ADD CONSTRAINT "QuoteItem_quoteId_fkey" FOREIGN KEY ("quoteId") REFERENCES "Quote"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BudgetItem" ADD CONSTRAINT "BudgetItem_marketingBudgetId_fkey" FOREIGN KEY ("marketingBudgetId") REFERENCES "MarketingManagementBudget"("id") ON DELETE CASCADE ON UPDATE CASCADE;
