/*
  Warnings:

  - You are about to drop the column `defaultPrice` on the `BudgetItem` table. All the data in the column will be lost.
  - You are about to drop the column `marketingBudgetId` on the `BudgetItem` table. All the data in the column will be lost.
  - You are about to drop the `MarketingManagementBudget` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "TemplateServiceType" AS ENUM ('STRING', 'TEXT', 'NUMBER', 'DECIMAL', 'BOOLEAN', 'SELECT', 'MULTISELECT', 'DATE', 'TIME');

-- DropForeignKey
ALTER TABLE "BudgetItem" DROP CONSTRAINT "BudgetItem_marketingBudgetId_fkey";

-- DropIndex
DROP INDEX "BudgetItem_marketingBudgetId_idx";

-- AlterTable
ALTER TABLE "BudgetItem" DROP COLUMN "defaultPrice",
DROP COLUMN "marketingBudgetId",
ADD COLUMN     "budgetTemplateId" TEXT;

-- AlterTable
ALTER TABLE "QuoteItem" ADD COLUMN     "budgetTemplateId" TEXT,
ADD COLUMN     "itemTotal" DECIMAL(12,2),
ADD COLUMN     "templateSnapshot" JSONB,
ADD COLUMN     "unitPrice" DECIMAL(12,2);

-- DropTable
DROP TABLE "MarketingManagementBudget";

-- CreateTable
CREATE TABLE "BudgetTemplate" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "BudgetTemplate_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TemplateServices" (
    "id" TEXT NOT NULL,
    "budgetTemplateId" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "label" TEXT NOT NULL,
    "type" "TemplateServiceType" NOT NULL,
    "required" BOOLEAN NOT NULL DEFAULT false,
    "options" JSONB,
    "defaultValue" TEXT,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TemplateServices_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "TemplateServices_budgetTemplateId_idx" ON "TemplateServices"("budgetTemplateId");

-- CreateIndex
CREATE UNIQUE INDEX "TemplateServices_budgetTemplateId_key_key" ON "TemplateServices"("budgetTemplateId", "key");

-- CreateIndex
CREATE INDEX "BudgetItem_budgetTemplateId_idx" ON "BudgetItem"("budgetTemplateId");

-- CreateIndex
CREATE INDEX "QuoteItem_budgetTemplateId_idx" ON "QuoteItem"("budgetTemplateId");

-- AddForeignKey
ALTER TABLE "BudgetItem" ADD CONSTRAINT "BudgetItem_budgetTemplateId_fkey" FOREIGN KEY ("budgetTemplateId") REFERENCES "BudgetTemplate"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TemplateServices" ADD CONSTRAINT "TemplateServices_budgetTemplateId_fkey" FOREIGN KEY ("budgetTemplateId") REFERENCES "BudgetTemplate"("id") ON DELETE CASCADE ON UPDATE CASCADE;
