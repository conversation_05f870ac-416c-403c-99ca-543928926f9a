-- CreateTable
CREATE TABLE "ImageComment" (
    "id" TEXT NOT NULL,
    "contentId" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "x" DOUBLE PRECISION NOT NULL,
    "y" DOUBLE PRECISION NOT NULL,
    "text" TEXT NOT NULL,
    "authorId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ImageComment_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ImageComment_contentId_idx" ON "ImageComment"("contentId");

-- CreateIndex
CREATE INDEX "ImageComment_authorId_idx" ON "ImageComment"("authorId");

-- AddForeignKey
ALTER TABLE "ImageComment" ADD CONSTRAINT "ImageComment_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "ImageComment" ADD CONSTRAINT "ImageComment_contentId_fkey" FOREIGN KEY ("contentId") REFERENCES "Content"("id") ON DELETE CASCADE ON UPDATE CASCADE;
