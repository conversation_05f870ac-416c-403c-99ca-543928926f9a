import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seed() {
    try {
        const clients = [
            {
                name: '<PERSON>',
                phone: '123456789',
                instagramUsername: 'jose_silva',
                monthlyPostsLimit: 10,
                additionalContent: 2,
                monthlyPlannings: [
                    {
                        month: 2,
                        year: 2025,
                        activities: [
                            {
                                week: 1,
                                description: 'Educar o público sobre serviços especiais e cuidados automotivos no verão.',
                                contents: [
                                    {
                                        activityDate: new Date('2025-02-01'),
                                        contentType: 'arte',
                                        channel: 'Story e Feed',
                                        details: 'Dia de agendar sua lavagem (link para contato).'
                                    },
                                    {
                                        activityDate: new Date('2025-02-15'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    },
                                    {
                                        activityDate: new Date('2025-02-22'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    },
                                    {
                                        activityDate: new Date('2025-02-27'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    }
                                ]
                            },
                            {
                                week: 2,
                                description: 'Prova social, promoções e foco em serviços de alta necessidade.',
                                contents: [
                                    {
                                        activityDate: new Date('2025-02-08'),
                                        contentType: 'arte',
                                        channel: 'Story',
                                        details: 'Mensagem de "Feliz Ano Novo" com chamada para ação: "Comece o ano com o carro impecável! Agende já sua lavagem."'
                                    },
                                    {
                                        activityDate: new Date('2025-02-22'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    },
                                    {
                                        activityDate: new Date('2025-02-22'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    }
                                ]
                            },
                            {
                                week: 3,
                                description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
                                contents: [
                                    {
                                        activityDate: new Date('2025-02-22'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    },
                                    {
                                        activityDate: new Date('2025-02-22'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    },
                                    {
                                        activityDate: new Date('2025-02-22'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    }
                                ]
                            },
                            {
                                week: 4,
                                description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
                                contents: [
                                    {
                                        activityDate: new Date('2025-02-22'),
                                        contentType: 'arte',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    },
                                    {
                                        activityDate: new Date('2025-02-22'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    },
                                    {
                                        activityDate: new Date('2025-02-22'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    },
                                    {
                                        activityDate: new Date('2025-02-22'),
                                        contentType: 'vídeo',
                                        channel: 'Story',
                                        details: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.'
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
        ];

        for (const client of clients) {
            const createdClient = await prisma.client.create({
                data: {
                    name: client.name,
                    phone: client.phone,
                    instagramUsername: client.instagramUsername,
                    monthlyPostsLimit: client.monthlyPostsLimit,
                    additionalContent: client.additionalContent,
                    monthlyPlannings: {
                        create: client.monthlyPlannings.map((planning) => ({
                            month: planning.month,
                            year: planning.year,
                            activities: {
                                create: planning.activities.map((activity) => ({
                                    week: activity.week,
                                    description: activity.description,
                                    contents: {
                                        create: activity.contents.map((content) => ({
                                            activityDate: content.activityDate,
                                            contentType: content.contentType,
                                            channel: content.channel,
                                            details: content.details,
                                            destination: content.channel === "Story" ? "Story" : "Feed"
                                        }))
                                    }
                                }))
                            }
                        }))
                    }
                },
                include: {
                    monthlyPlannings: {
                        include: {
                            activities: {
                                include: {
                                    contents: true
                                }
                            }
                        }
                    }
                }
            });

            console.log(`Client ${createdClient.name} with planning data has been created.`);
        }
    } catch (error) {
        console.error('Error populating data:', error);
    } finally {
        await prisma.$disconnect();
    }
}

seed();
