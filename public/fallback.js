self.fallback = async (request) => {
  // Inicial<PERSON>e, tenta buscar recurso da rede
  try {
    return await fetch(request);
  } catch (err) {
    // Se falhar, busca no cache
    const cache = await caches.open('offline-fallback');
    
    // Tenta buscar qualquer versão em cache
    try {
      const cachedResponse = await cache.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    } catch (err) {
      console.error('Erro ao buscar no cache:', err);
    }
    
    // Para conteúdo HTML, retorna a página offline
    const url = new URL(request.url);
    if (request.headers.get('Accept')?.includes('text/html')) {
      return await cache.match('/offline.html');
    }
    
    // Para ícones, tenta carregar um ícone de fallback
    if (url.pathname.match(/\.(png|jpg|jpeg|svg|gif)$/i)) {
      if (url.pathname.includes('icon-b4desk')) {
        return await cache.match('/icon-b4desk.png');
      }
      return new Response('Not found', { status: 404 });
    }
    
    // Default fallback
    return new Response('Network error', { status: 408 });
  }
};
