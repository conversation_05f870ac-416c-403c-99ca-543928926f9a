#!/bin/bash

# =================================================================
# SISTEMA DE BACKUP AUTOMÁTICO - B4DESK
# =================================================================

set -e

# Configurações
BACKUP_DIR="./backups"
DATE=$(date +"%Y%m%d_%H%M%S")

# Detectar ambiente atual
if [ ! -f ".env" ]; then
    print_error "Arquivo .env não encontrado!"
    print_error "Execute 'pnpm run setup:prod' para configurar produção"
    print_error "ou 'pnpm run setup:dev' para desenvolvimento"
    exit 1
fi

# Ler a DATABASE_URL do arquivo .env atual
DATABASE_URL=$(grep -v '^#' .env | grep DATABASE_URL | cut -d'=' -f2- | tr -d '"')

if [ -z "$DATABASE_URL" ]; then
    print_error "DATABASE_URL não encontrada no .env!"
    exit 1
fi

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${BLUE}[BACKUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ [BACKUP]${NC} $1"
}

print_error() {
    echo -e "${RED}❌ [BACKUP]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️  [BACKUP]${NC} $1"
}

# Criar diretório de backup se não existir
mkdir -p "$BACKUP_DIR"

# Detectar tipo de ambiente
if echo "$DATABASE_URL" | grep -q "neon.tech"; then
    AMBIENTE="PRODUÇÃO"
    print_message "Fazendo backup do banco de PRODUÇÃO (Neon.tech)..."
    print_message "⚠️  Conectando ao banco de produção..."
elif echo "$DATABASE_URL" | grep -q "localhost:5433"; then
    AMBIENTE="DESENVOLVIMENTO"
    print_message "Fazendo backup do banco de DESENVOLVIMENTO (local)..."
else
    print_error "Ambiente não reconhecido!"
    print_error "DATABASE_URL: $DATABASE_URL"
    exit 1
fi

# Nome do arquivo de backup
BACKUP_FILE="$BACKUP_DIR/b4desk_backup_${AMBIENTE,,}_$DATE.sql"

# Fazer backup usando pg_dump
print_message "Criando backup em: $BACKUP_FILE"

# Verificar se pg_dump está disponível
if ! command -v pg_dump &> /dev/null; then
    print_error "pg_dump não encontrado!"
    print_error "Instale o PostgreSQL client:"
    print_error "  sudo apt-get install postgresql-client"
    exit 1
fi

# Extrair componentes da URL
if echo "$DATABASE_URL" | grep -q "neon.tech"; then
    # Para URLs do Neon.tech: ************************************* (sem porta)
    # Remover aspas e prefixo
    CLEAN_URL=$(echo "$DATABASE_URL" | tr -d '"' | sed 's/postgresql:\/\///')
    
    # Extrair user:pass@host/db
    USER_PASS_HOST=$(echo "$CLEAN_URL" | cut -d'/' -f1)
    DB_NAME=$(echo "$CLEAN_URL" | cut -d'/' -f2 | cut -d'?' -f1)
    
    # Separar user:pass e host
    USER_PASS=$(echo "$USER_PASS_HOST" | cut -d'@' -f1)
    DB_HOST=$(echo "$USER_PASS_HOST" | cut -d'@' -f2)
    
    # Extrair componentes individuais
    DB_USER=$(echo "$USER_PASS" | cut -d':' -f1)
    DB_PASS=$(echo "$USER_PASS" | cut -d':' -f2)
    DB_PORT="5432"  # Porta padrão do PostgreSQL para Neon.tech
    
    # Adicionar parâmetros SSL para Neon.tech
    SSL_OPTS="--no-password"
else
    # Para URLs localhost
    DB_HOST="localhost"
    DB_PORT="5433"
    DB_NAME="b4_desk"
    DB_USER="postgres"
    DB_PASS="postgres"
    SSL_OPTS="--no-password"
fi

print_message "Conectando em: $DB_HOST:$DB_PORT/$DB_NAME como $DB_USER"

# Configurar variáveis de ambiente para pg_dump
export PGPASSWORD="$DB_PASS"

# Executar backup
print_message "Tentando backup com pg_dump..."
if pg_dump -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" $SSL_OPTS --verbose > "$BACKUP_FILE" 2>/dev/null; then
    print_success "Backup criado com sucesso!"
    print_message "Arquivo: $BACKUP_FILE"
    print_message "Tamanho: $(du -h "$BACKUP_FILE" | cut -f1)"
    print_message "Ambiente: $AMBIENTE"
else
    print_warning "pg_dump falhou, tentando com psql..."
    
    # Alternativa: usar psql para fazer dump
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\copy (SELECT * FROM information_schema.tables) TO '$BACKUP_FILE.info'" > /dev/null 2>&1; then
        print_message "Conexão OK, tentando backup manual..."
        
        # Criar backup usando psql
        if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -c "\d" > "$BACKUP_FILE.schema" 2>/dev/null; then
            print_success "Backup de schema criado!"
            print_message "Arquivo: $BACKUP_FILE.schema"
            print_message "Use o console do Neon.tech para backup completo"
        else
            print_error "Falha ao criar backup!"
        fi
    else
        print_error "Falha ao conectar ao banco!"
        print_error "Detalhes da conexão:"
        print_error "  Host: $DB_HOST"
        print_error "  Port: $DB_PORT"
        print_error "  Database: $DB_NAME"
        print_error "  User: $DB_USER"
        print_error ""
        print_error "Sugestões:"
        print_error "1. Verifique se o Neon.tech está online"
        print_error "2. Use o console do Neon.tech para fazer backup"
        print_error "3. Instale uma versão mais recente do postgresql-client"
        exit 1
    fi
fi

# Limpar backups antigos (manter apenas os últimos 30)
print_message "Limpando backups antigos..."
find "$BACKUP_DIR" -name "b4desk_backup_*.sql" -type f -mtime +30 -delete 2>/dev/null || true

# Listar backups existentes
print_message "Backups disponíveis:"
ls -la "$BACKUP_DIR"/b4desk_backup_*.sql 2>/dev/null | tail -10 || echo "Nenhum backup encontrado"

print_success "Processo de backup concluído!"
print_message "Ambiente: $AMBIENTE"
print_message "Para voltar ao desenvolvimento: pnpm run setup:dev"

# Desconfigurar variável de ambiente
unset PGPASSWORD
