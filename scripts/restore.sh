#!/bin/bash

# =================================================================
# SCRIPT DE RESTAURAÇÃO DE BACKUP - B4DESK
# =================================================================

set -e

# Configurações
BACKUP_DIR="./backups"

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${BLUE}[RESTORE]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ [RESTORE]${NC} $1"
}

print_error() {
    echo -e "${RED}❌ [RESTORE]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️  [RESTORE]${NC} $1"
}

# Verificar se o diretório de backup existe
if [ ! -d "$BACKUP_DIR" ]; then
    print_error "Diretório de backup não encontrado: $BACKUP_DIR"
    exit 1
fi

# Listar backups disponíveis
print_message "Backups disponíveis:"
backups=($(ls -t "$BACKUP_DIR"/b4desk_backup_*.sql 2>/dev/null || echo ""))

if [ ${#backups[@]} -eq 0 ]; then
    print_error "Nenhum backup encontrado!"
    exit 1
fi

# Mostrar lista numerada de backups
for i in "${!backups[@]}"; do
    filename=$(basename "${backups[$i]}")
    size=$(du -h "${backups[$i]}" | cut -f1)
    date_created=$(stat -c %y "${backups[$i]}" | cut -d' ' -f1,2 | cut -d'.' -f1)
    echo "$((i+1)). $filename ($size) - $date_created"
done

# Solicitar seleção do backup
echo ""
read -p "Selecione o número do backup para restaurar (1-${#backups[@]}): " selection

# Validar seleção
if ! [[ "$selection" =~ ^[0-9]+$ ]] || [ "$selection" -lt 1 ] || [ "$selection" -gt ${#backups[@]} ]; then
    print_error "Seleção inválida!"
    exit 1
fi

# Obter arquivo selecionado
selected_backup="${backups[$((selection-1))]}"
print_message "Backup selecionado: $(basename "$selected_backup")"

# Verificar ambiente atual
print_warning "ATENÇÃO: Esta operação irá SOBRESCREVER o banco atual!"
print_warning "Certifique-se de estar no ambiente correto."

if grep -q "neon.tech" .env 2>/dev/null; then
    print_error "BANCO DE PRODUÇÃO DETECTADO!"
    print_error "Não é recomendado restaurar backup em produção via script."
    print_error "Use o console do Neon.tech para restaurações em produção."
    exit 1
fi

# Confirmação final
print_warning "Você está prestes a restaurar o backup:"
print_warning "  Arquivo: $(basename "$selected_backup")"
print_warning "  Banco: $(grep DATABASE_URL .env | cut -d'=' -f2)"
echo ""
read -p "Digite 'CONFIRMO' para continuar: " confirmation

if [ "$confirmation" != "CONFIRMO" ]; then
    print_message "Operação cancelada."
    exit 0
fi

# Executar restauração
print_message "Iniciando restauração..."

# Verificar se é banco local
if grep -q "localhost:5433" .env; then
    print_message "Restaurando no banco local..."
    
    # Configurações do banco local
    DB_HOST="localhost"
    DB_PORT="5433"
    DB_NAME="b4_desk"
    DB_USER="postgres"
    export PGPASSWORD="postgres"
    
    # Dropar e recriar banco
    print_message "Recriando banco..."
    dropdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" --if-exists "$DB_NAME" 2>/dev/null || true
    createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME"
    
    # Restaurar backup
    print_message "Restaurando dados..."
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" < "$selected_backup"; then
        print_success "Restauração concluída com sucesso!"
    else
        print_error "Falha na restauração!"
        exit 1
    fi
    
    # Limpar variável de ambiente
    unset PGPASSWORD
    
else
    print_error "Ambiente não suportado para restauração automática."
    print_error "Use o console do provedor do banco para restaurar."
    exit 1
fi

print_success "Backup restaurado com sucesso!"
print_message "Execute 'pnpm run db:status' para verificar o estado do banco."
