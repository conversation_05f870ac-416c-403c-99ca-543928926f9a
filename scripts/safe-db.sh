#!/bin/bash

# =================================================================
# SCRIPT DE PROTEÇÃO DE BANCO DE DADOS - B4DESK
# =================================================================

set -e  # Parar execução em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para exibir mensagens coloridas
print_message() {
    echo -e "${BLUE}[B4DESK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️  [AVISO]${NC} $1"
}

print_error() {
    echo -e "${RED}❌ [ERRO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ [SUCESSO]${NC} $1"
}

# Verificar se existe .env
if [ ! -f ".env" ]; then
    print_error ".env não encontrado!"
    exit 1
fi

# Verificar qual ambiente está sendo usado (apenas linhas ativas)
if grep -v '^#' .env | grep -q "neon.tech"; then
    print_error "BANCO DE PRODUÇÃO DETECTADO NO .env!"
    print_error "Operação cancelada por segurança."
    print_warning "Para usar produção, use:"
    echo "   pnpm run setup:prod"
    echo "   pnpm run migrate:prod"
    exit 1
fi

# Verificar se é ambiente de desenvolvimento (apenas linhas ativas)
if ! grep -v '^#' .env | grep -q "localhost:5433"; then
    print_error "Ambiente não reconhecido!"
    print_warning "Use pnpm run setup:dev para desenvolvimento local"
    exit 1
fi

print_success "Ambiente de desenvolvimento confirmado ✅"
print_message "Executando comando: $@"

# Executar o comando passado como parâmetro
exec "$@"
