#!/bin/bash

# =================================================================
# SETUP INICIAL DO PROJETO B4DESK COM PNPM
# =================================================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_message() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}✅ [SETUP]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠️  [SETUP]${NC} $1"
}

print_message "Configurando projeto B4DESK..."

# Verificar se pnpm está instalado
if ! command -v pnpm &> /dev/null; then
    print_warning "PNPM não está instalado!"
    echo "Instale com: npm install -g pnpm"
    exit 1
fi

# Verificar se Docker está rodando
if ! docker info &> /dev/null; then
    print_warning "Docker não está rodando!"
    echo "Inicie o Docker e tente novamente."
    exit 1
fi

# Instalar dependências
print_message "Instalando dependências..."
pnpm install

# Configurar ambiente de desenvolvimento
print_message "Configurando ambiente de desenvolvimento..."
pnpm run setup:dev

# Verificar/iniciar banco de dados
print_message "Verificando banco de dados..."
if ! docker-compose ps | grep -q "postgres.*Up"; then
    print_message "Iniciando banco de dados..."
    sudo docker-compose up -d postgres || docker-compose up -d postgres
fi

# Aguardar banco estar pronto
print_message "Aguardando banco de dados estar pronto..."
sleep 5

# Executar migrações
print_message "Executando migrações..."
pnpm run db:migrate

# Gerar cliente Prisma
print_message "Gerando cliente Prisma..."
pnpm run db:generate

print_success "Setup concluído!"
print_message ""
print_message "📋 Comandos disponíveis:"
print_message ""
print_message "🔧 Desenvolvimento:"
print_message "  pnpm run dev          # Iniciar servidor de desenvolvimento"
print_message "  pnpm run db:studio    # Abrir Prisma Studio"
print_message "  pnpm run db:migrate   # Executar nova migração"
print_message "  pnpm run db:status    # Ver status do banco"
print_message ""
print_message "🛡️  Segurança:"
print_message "  pnpm run setup:dev    # Ambiente de desenvolvimento"
print_message "  pnpm run setup:prod   # Ambiente de produção"
print_message "  pnpm run backup:create # Criar backup"
print_message ""
print_message "🚀 Para começar a desenvolver:"
print_message "  pnpm run dev"
