"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState, useCallback, useMemo } from "react";
import { Button } from "@/app/components/ui/button";
import { Card, CardContent } from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { ListTodo, MoveLeft, GripVertical, Table2, Search } from "lucide-react";
import { toast } from "sonner";
import { Badge } from "@/app/components/ui/badge";
import { Checkbox } from "@/app/components/ui/checkbox";
import { AssignBulkDemands } from "@/app/components/assign-bulk-demands";
import { RemoveBulkDemands } from "@/app/components/remove-bulk-demands";
import { GeneralDemandForm } from "@/app/components/general-demand-form";
import { EditGeneralDemand } from "@/app/components/edit-general-demand";
import { EditPlanActivity } from "@/app/components/edit-plan-activity";
import { SortableDemandsList } from "@/app/components/sortable-demands-list";
import { ArchiveDemand } from "@/app/components/archive-demand";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import { Header } from "@/app/components/header";
import Loading from "@/app/components/ui/loading";
import { NotAllowed } from "@/app/components/not-allowed";
import { Footer } from "@/app/components/footer";
import { ActivityInfo } from "@/app/components/activity-info";
import { ContentAssignmentView } from "@/app/components/content-assignment-view";
import { AddContentReview } from "@/app/components/add-content-review";

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  role: string;
}

interface Client {
  id: string;
  name: string;
}

interface Content {
  reviewedBy: { id?: string | undefined; name?: string | undefined; email?: string | undefined; image?: string | undefined; } | undefined;
  urlFolder: string;
  assignedToId: string | null;
  id: string;
  type: 'content' | 'general';
  activityDate: string;
  contentType?: string;
  channel?: string | null;
  destination?: string;
  status?: string | null;
  details?: string | null;
  title?: string;
  description?: string | null;
  priority?: string;
  position?: number;
  client: Client;
  assignedTo: User | null;
  steps?: {
    id: string;
    type: string;
    assignedTo: {
      id: string;
      name: string | null;
      email: string;
      image?: string | null;
    }
  }[];
  planningStatus?: string;
  copywriting?: string | null;
  reference?: string | null;
  caption?: string | null;
  urlStructuringFeed?: string[] | null;
  weeklyActivityId?: string;
  isLooseClient?: boolean;
  looseClientName?: string | null;
  archived?: boolean;
  review?: string | null;
}

export default function AdminDemandsPage() {
  const { data: session, status } = useSession();
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [clients, setClients] = useState<Client[]>([]);
  const [contents, setContents] = useState<Content[]>([]);
  const [filteredContents, setFilteredContents] = useState<Content[]>([]);
  const [selectedContentIds, setSelectedContentIds] = useState<string[]>([]);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [selectedDemandType, setSelectedDemandType] = useState<string>("all");
  const [selectedPriority, setSelectedPriority] = useState<string>("all");
  const [selectedMonth, setSelectedMonth] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("não-concluído");
  const [viewByPriority, setViewByPriority] = useState<boolean>(false);
  const [showSortableList, setShowSortableList] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [showArchived, setShowArchived] = useState<boolean>(false);
  const router = useRouter();

  useEffect(() => {
    const checkUserRole = async () => {
      if (session?.user?.email) {
        try {
          const response = await fetch(`/api/users/${session.user.email}`);
          if (response.ok) {
            const user = await response.json();
            setIsAdmin(["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"].includes(user?.role || ""));
          }
        } catch (error) {
          console.error("Erro ao verificar função do usuário:", error);
        }
      }
    };

    if (status === "authenticated") {
      checkUserRole();
    } else if (status === "unauthenticated") {
      router.push("/");
    }
  }, [status, session, router]);

  const monthNames = useMemo(() => [
    "Janeiro", "Fevereiro", "Março", "Abril",
    "Maio", "Junho", "Julho", "Agosto",
    "Setembro", "Outubro", "Novembro", "Dezembro",
  ], []);

  const monthName = useCallback((month: number) => monthNames[month - 1], [monthNames]);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const usersResponse = await fetch("/api/users");
      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        const filteredUsers = usersData.filter(
          (user: User) => user.role !== "DEVELOPER"
        );
        setUsers(filteredUsers);
      }

      const clientsResponse = await fetch("/api/clients");
      if (clientsResponse.ok) {
        const clientsData = await clientsResponse.json();
        setClients(clientsData);
      }

      const contentsResponse = await fetch(
        `/api/admin/demands?type=${selectedDemandType}&showArchived=${showArchived}`
      );
      if (contentsResponse.ok) {
        const contentsData = await contentsResponse.json();
        setContents(contentsData);
      }
    } catch (error) {
      console.error("Erro ao buscar dados:", error);
      toast.error("Erro ao carregar dados");
    } finally {
      setIsLoading(false);
    }
  }, [selectedDemandType, showArchived]);

  useEffect(() => {
    if (isAdmin) {
      fetchData();
    }
  }, [isAdmin, fetchData]);

  useEffect(() => {
    let filtered = [...contents];

    if (selectedClientId) {
      filtered = filtered.filter(
        (content) => content.client?.id === selectedClientId
      );
    }

    if (selectedUserId) {
      filtered = filtered.filter(
        (content) => content.assignedTo?.id === selectedUserId ||
          content.steps?.some(step => step.assignedTo?.id === selectedUserId)
      );
    }

    if (selectedDemandType !== 'all') {
      filtered = filtered.filter(
        (content) => content.type === selectedDemandType
      );
    }

    if (selectedPriority !== 'all') {
      filtered = filtered.filter(
        (content) => content.priority?.toLowerCase() === selectedPriority.toLowerCase()
      );
    }

    if (selectedMonth !== 'all') {
      filtered = filtered.filter(content => {
        const contentDate = new Date(content.activityDate);
        const contentMonth = contentDate.getMonth() + 1;
        return monthName(contentMonth).toLowerCase() === selectedMonth;
      });
    }

    if (selectedStatus !== 'all') {
      if (selectedStatus === 'não-concluído') {
        filtered = filtered.filter(content =>
          content.status?.toLowerCase() !== 'concluído'
        );
      } else if (selectedStatus === 'concluído') {
        filtered = filtered.filter(content =>
          content.status?.toLowerCase() === 'concluído'
        );
      } else {
        filtered = filtered.filter(content =>
          content.status?.toLowerCase() === selectedStatus.toLowerCase()
        );
      }
    }

    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase().trim();
      filtered = filtered.filter((content) => {
        const contentId = content.id.toLowerCase();
        if (contentId.includes(term)) {
          return true;
        }

        if (content.type === 'general') {
          return (
            (content.title && content.title.toLowerCase().includes(term)) ||
            (content.description && content.description.toLowerCase().includes(term)) ||
            (content.client?.name && content.client.name.toLowerCase().includes(term)) ||
            (content.looseClientName && content.looseClientName.toLowerCase().includes(term)) ||
            (content.status && content.status.toLowerCase().includes(term)) ||
            (content.priority && content.priority.toLowerCase().includes(term)) ||
            (content.assignedTo?.name && content.assignedTo.name.toLowerCase().includes(term)) ||
            (content.assignedTo?.email && content.assignedTo.email.toLowerCase().includes(term))
          );
        } else {
          return (
            (content.contentType && content.contentType.toLowerCase().includes(term)) ||
            (content.details && content.details.toLowerCase().includes(term)) ||
            (content.destination && content.destination.toLowerCase().includes(term)) ||
            (content.client?.name && content.client.name.toLowerCase().includes(term)) ||
            (content.status && content.status.toLowerCase().includes(term)) ||
            (content.assignedTo?.name && content.assignedTo.name.toLowerCase().includes(term)) ||
            (content.assignedTo?.email && content.assignedTo.email.toLowerCase().includes(term))
          );
        }
      });
    }

    if (viewByPriority) {
      filtered.sort((a, b) => {
        const priorityOrder = {
          'urgente': 1,
          'alta': 2,
          'normal': 3,
          'baixa': 4
        };

        const priorityA = a.priority?.toLowerCase() || 'normal';
        const priorityB = b.priority?.toLowerCase() || 'normal';

        if (priorityA !== priorityB) {
          return (priorityOrder[priorityA as keyof typeof priorityOrder] || 5) -
            (priorityOrder[priorityB as keyof typeof priorityOrder] || 5);
        }

        const dateA = a.activityDate ? new Date(a.activityDate).getTime() : 0;
        const dateB = b.activityDate ? new Date(b.activityDate).getTime() : 0;
        return dateA - dateB;
      });
    } else {
      filtered.sort((a, b) => {
        const dateA = a.activityDate ? new Date(a.activityDate).getTime() : 0;
        const dateB = b.activityDate ? new Date(b.activityDate).getTime() : 0;
        return dateA - dateB;
      });
    }

    setFilteredContents(filtered);
  }, [contents, selectedClientId, selectedUserId, selectedDemandType, selectedPriority, selectedMonth, selectedStatus, searchTerm, viewByPriority, showArchived, monthName]);

  const toggleContentSelection = (contentId: string) => {
    setSelectedContentIds((prev) => {
      if (prev.includes(contentId)) {
        return prev.filter((id) => id !== contentId);
      } else {
        return [...prev, contentId];
      }
    });
  };

  const selectAllContents = () => {
    if (selectedContentIds.length === filteredContents.length) {
      setSelectedContentIds([]);
    } else {
      setSelectedContentIds(filteredContents.map((content) => content.id));
    }
  };

  const handleBulkAssignSuccess = () => {
    fetchData();
    setSelectedContentIds([]);
  };

  const handleLocalBulkAssign = (
    contentIds: string[],
    generalDemandIds: string[],
    _userId: string | null,
    user: { id: string; name: string | null; email: string; image: string | null; role?: string } | null
  ) => {
    setContents(prevContents =>
      prevContents.map(content => {
        if (
          (content.type === 'content' && contentIds.includes(content.id)) ||
          (content.type === 'general' && generalDemandIds.includes(content.id))
        ) {
          return {
            ...content,
            assignedTo: user as User | null,
            status: user ? "repassado" : content.status
          };
        }
        return content;
      })
    );
  };

  const handleLocalBulkRemove = (removedIds: string[]) => {
    setContents(prevContents =>
      prevContents.filter(content => !removedIds.includes(content.id))
    );
    setSelectedContentIds(prev => prev.filter(id => !removedIds.includes(id)));
  };

  const handleLocalArchive = (demandId: string, isArchiving: boolean) => {
    setContents(prevContents =>
      prevContents.map(content => {
        if (content.id === demandId) {
          return {
            ...content,
            archived: isArchiving
          };
        }
        return content;
      })
    );

    if ((isArchiving && !showArchived) || (!isArchiving && showArchived)) {
      setFilteredContents(prev => prev.filter(content => content.id !== demandId));
      setSelectedContentIds(prev => prev.filter(id => id !== demandId));
    }
  };

  const handleReviewUpdate = (id: string, newReview: string) => {
    setContents(prev => prev.map(c => c.id === id ? { ...c, review: newReview } : c));
  };

  const handleStatusUpdate = (id: string, newStatus: string) => {
    setContents(prev => prev.map(c => c.id === id ? { ...c, status: newStatus } : c));
  };

  const getStatusColor = (status: string | null | undefined) => {
    if (!status) return "bg-gray-200 text-gray-800";

    switch (status.toLowerCase()) {
      case "pendente":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-950 dark:text-yellow-300";
      case "em andamento":
        return "bg-blue-100 text-blue-800 dark:bg-blue-950 dark:text-blue-300";
      case "alteração":
        return "bg-rose-100 text-rose-800 dark:bg-rose-950 dark:text-rose-300";
      case "repassado":
        return "bg-purple-100 text-purple-800 dark:bg-purple-950 dark:text-purple-300";
      case "estruturação de feed":
        return "bg-gray-100 text-gray-800 dark:bg-gray-950 dark:text-gray-300";
      case "em revisão":
        return "bg-orange-100 text-orange-800 dark:bg-orange-950 dark:text-orange-300";
      case "aprovado":
        return "bg-teal-100 text-teal-800 dark:bg-teal-950 dark:text-teal-300";
      case "anúncio concluído":
        return "bg-lime-100 text-lime-800 dark:bg-lime-950 dark:text-lime-300";
      case "concluído":
        return "bg-green-100 text-green-800 dark:bg-green-950 dark:text-green-300";
      case "captado":
        return "bg-blue-100 text-blue-800 dark:bg-blue-950 dark:text-blue-300";
      case "pend. captação":
        return "bg-purple-100 text-purple-800 dark:bg-purple-950 dark:text-purple-300";
      case "feed estruturado":
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-950 dark:text-indigo-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-950 dark:text-gray-300";
    }
  };

  const getPriorityColor = (priority: string | null | undefined) => {
    if (!priority) return "bg-gray-200 text-gray-800 dark:bg-gray-950 dark:text-gray-300";

    switch (priority.toLowerCase()) {
      case "baixa":
        return "bg-green-100 text-green-800 dark:bg-green-950 dark:text-green-300";
      case "normal":
        return "bg-blue-100 text-blue-800 dark:bg-blue-950 dark:text-blue-300";
      case "alta":
        return "bg-orange-100 text-orange-800 dark:bg-orange-950 dark:text-orange-300";
      case "urgente":
        return "bg-red-100 text-red-800 dark:bg-red-950 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-950 dark:text-gray-300";
    }
  };

  const renderContentCell = (content: Content) => {
    if (content.type === 'general') {
      return (
        <div className="max-w-[200px] truncate" title={content.title}>
          {content.title}
        </div>
      );
    } else {
      return (
        <div className="max-w-[200px] truncate" title={content.details || ''}>
          {content.details}
        </div>
      );
    }
  };

  const renderTypeCell = (content: Content) => {
    return (
      <div className="flex flex-col gap-1">
        {content.type === 'content' && content.contentType && (
          <div className="text-sm">{content.contentType}</div>
        )}
        <Badge variant="outline" className={`${getPriorityColor(content.priority)} px-2 text-center inline-flex justify-center min-w-[80px]`}>
          {content.priority || "Normal"}
        </Badge>
      </div>
    );
  };

  const renderDestinationCell = (content: Content) => {
    if (content.type === 'general') {
      return (
        <div className="max-w-[200px] truncate" title={content.description || '-'}>
          {content.description || "-"}
        </div>
      );
    } else {
      return (
        <div className="max-w-[200px] truncate" title={content.destination || ''}>
          {content.destination}
        </div>
      );
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <div className="p-4 xs:p-8 flex-grow">
        {isLoading ? (
          <div className="min-h-[70vh] flex justify-center items-center">
            <Loading />
          </div>
        ) : isAdmin ? (
          <div>
            <div className="flex flex-col xs:flex-row items-start xs:items-center justify-between gap-4 mb-4">
              <Button variant="outline" onClick={() => router.push("/panel")}>
                <MoveLeft className="h-4 w-4" /> Voltar
              </Button>
              <div className="flex flex-col xs:items-end">
                <div className="flex items-center gap-2 group">
                  <h1 className="text-xl uppercase font-geistMono font-semibold tracking-tight">
                    Gerenciar demandas
                  </h1>
                  <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                    <ListTodo size={24} color="#db5743" />
                  </div>
                </div>
                {showArchived && (
                  <div className="text-amber-600 text-xs font-medium mt-1">
                    ⚠️ Exibindo apenas demandas arquivadas
                  </div>
                )}
              </div>
            </div>

            <div className="mb-6">
              <Card>
                <CardContent className="p-4 space-y-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2">
                      <div>
                        <Select
                          value={selectedDemandType}
                          onValueChange={(value) => {
                            setSelectedDemandType(value);
                            setSelectedContentIds([]);
                            if (value === 'content') {
                              setSelectedPriority('all');
                            }
                            fetchData();
                          }}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Tipo de demanda" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">Todas as demandas</SelectItem>
                            <SelectItem value="content">Demandas de conteúdo</SelectItem>
                            <SelectItem value="general">Demandas pontuais</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Select
                          value={selectedClientId || "all"}
                          onValueChange={(value) =>
                            setSelectedClientId(value === "all" ? null : value)
                          }
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Filtrar por cliente" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">Todos os clientes</SelectItem>
                            {clients
                              .sort((a, b) => a.name.localeCompare(b.name))
                              .map((client) => (
                                <SelectItem key={client.id} value={client.id}>
                                  {client.name}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Select
                          value={selectedUserId || "all"}
                          onValueChange={(value) => {
                            if (showSortableList) {
                              setShowSortableList(false);
                            }
                            setSelectedUserId(value === "all" ? null : value);
                          }}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Filtrar por colaborador" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">Todos os colaboradores</SelectItem>
                            {users
                              .sort((a, b) => {
                                const nameA = (a.name || a.email).toLowerCase();
                                const nameB = (b.name || b.email).toLowerCase();
                                return nameA.localeCompare(nameB);
                              })
                              .map((user) => (
                                <SelectItem key={user.id} value={user.id}>
                                  {(user.name)?.split(' ')[0] || user.email}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Select
                          value={selectedStatus}
                          onValueChange={(value) => setSelectedStatus(value)}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Filtrar por status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">Todos os status</SelectItem>
                            <SelectItem value="não-concluído">Não concluídos</SelectItem>
                            <SelectItem value="pendente">Pendente</SelectItem>
                            <SelectItem value="estruturação de feed">Estruturação de feed</SelectItem>
                            <SelectItem value="feed estruturado">Feed estruturado</SelectItem>
                            <SelectItem value="repassado">Repassado</SelectItem>
                            <SelectItem value="em andamento">Em andamento</SelectItem>
                            <SelectItem value="em revisão">Em revisão</SelectItem>
                            <SelectItem value="alteração">Alteração</SelectItem>
                            <SelectItem value="pend. captação">Pend. captação</SelectItem>
                            <SelectItem value="aprovado">Aprovado</SelectItem>
                            <SelectItem value="captado">Captado</SelectItem>
                            <SelectItem value="anúncio concluído">Anúncio concluído</SelectItem>
                            <SelectItem value="concluído">Concluídos</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {!showSortableList && (
                        <div className="flex flex-col gap-2">
                          <Select
                            value={selectedPriority}
                            onValueChange={(value) => {
                              setSelectedPriority(value);
                              if (value !== 'all') {
                                setViewByPriority(true);
                              } else {
                                setViewByPriority(false);
                              }
                            }}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Filtrar por prioridade" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">Todas as prioridades</SelectItem>
                              <SelectItem value="baixa">Baixa</SelectItem>
                              <SelectItem value="normal">Normal</SelectItem>
                              <SelectItem value="alta">Alta</SelectItem>
                              <SelectItem value="urgente">Urgente</SelectItem>
                            </SelectContent>
                          </Select>

                          <Select
                            value={selectedMonth}
                            onValueChange={(value) => setSelectedMonth(value)}
                          >
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Filtrar por mês" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">Todos os meses</SelectItem>
                              {monthNames.map((name: string, index: number) => (
                                <SelectItem key={index} value={name.toLowerCase()}>
                                  {name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>

                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs"
                            onClick={() => {
                              setViewByPriority(prev => !prev);
                            }}
                          >
                            {viewByPriority
                              ? "Visualizar por data"
                              : "Visualizar por prioridade"}
                          </Button>
                        </div>
                      )}

                      <div className="flex flex-col gap-2">
                        <Button
                          variant={showArchived ? "default" : "outline"}
                          size="sm"
                          className={`text-xs ${showArchived ? 'bg-amber-500 hover:bg-amber-600' : ''}`}
                          onClick={() => {
                            setShowArchived(!showArchived);
                            setSelectedContentIds([]);
                          }}
                        >
                          {showArchived
                            ? "Voltar para demandas ativas"
                            : "Mostrar apenas demandas arquivadas"}
                        </Button>
                      </div>

                      <div className="lg:col-span-4">
                        <div className="relative">
                          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Pesquisar demandas..."
                            className="pl-8 placeholder:text-xs"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2 justify-between">
                      <div className="text-sm text-muted-foreground">
                        {filteredContents.length} demandas encontradas
                      </div>

                      <div className="flex flex-wrap gap-2">
                        {selectedDemandType === 'general' && (
                          <GeneralDemandForm
                            clients={clients}
                            onSuccess={fetchData}
                            buttonLabel="Nova demanda pontual"
                          />
                        )}

                        {selectedUserId && selectedDemandType === 'all' && (
                          <Button
                            variant="outline"
                            className="gap-1 whitespace-nowrap"
                            onClick={() => {
                              setShowSortableList(!showSortableList);
                              if (!showSortableList) {
                                setSelectedContentIds([]);
                              }
                            }}
                          >
                            {showSortableList ? (
                              <>
                                <Table2 className="h-4 w-4" />
                                Visualizar em tabela
                              </>
                            ) : (
                              <>
                                <GripVertical className="h-4 w-4" />
                                Ordenar todas as demandas do colaborador
                              </>
                            )}
                          </Button>
                        )}

                        {!showSortableList && (
                          <div className="flex flex-col sm:flex-row gap-2">
                            <Button
                              variant="outline"
                              className={`gap-1 whitespace-nowrap ${showArchived ? "border-amber-500 text-amber-700" : ""}`}
                              onClick={selectAllContents}
                            >
                              {selectedContentIds.length === filteredContents.length
                                ? `Desmarcar ${showArchived ? "arquivados" : "todos"}`
                                : `Selecionar ${showArchived ? "arquivados" : "todos"}`}
                            </Button>

                            <AssignBulkDemands
                              contentIds={selectedContentIds.filter(id =>
                                contents.find(content => content.id === id && content.type === 'content')
                              )}
                              generalDemandIds={selectedContentIds.filter(id =>
                                contents.find(content => content.id === id && content.type === 'general')
                              )}
                              onSuccess={handleBulkAssignSuccess}
                              onLocalUpdate={handleLocalBulkAssign}
                            />

                            <div className="flex flex-col gap-2 items-end">
                              <RemoveBulkDemands
                                demands={selectedContentIds.map(id => {
                                  const content = contents.find(c => c.id === id);
                                  return {
                                    id,
                                    type: content?.type as 'general' | 'content'
                                  };
                                }).filter(Boolean)}
                                onSuccess={handleBulkAssignSuccess}
                                onLocalUpdate={handleLocalBulkRemove}
                              />
                              <p className="text-xs text-muted-foreground text-end max-w-[350px]">
                                Cuidado ao remover demandas! Esta ação não pode ser desfeita.
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {showSortableList ? (
              <SortableDemandsList
                demands={filteredContents.filter(content =>
                  selectedDemandType === 'all'
                    ? true
                    : content.type === selectedDemandType
                )}
                userId={selectedUserId!}
                userName={users.find(user => user.id === selectedUserId)?.name || users.find(user => user.id === selectedUserId)?.email}
                demandType={selectedDemandType as 'content' | 'general' | 'all'}
                onDemandsUpdate={(updatedDemands) => {
                  const updatedContents = contents.map(content => {
                    const updatedDemand = updatedDemands.find(d => d.id === content.id);
                    if (updatedDemand) {
                      return {
                        ...content,
                        priority: updatedDemand.priority,
                        position: updatedDemand.position
                      };
                    }
                    return content;
                  });
                  setContents(updatedContents);
                }}
              />
            ) : (
              <Card>
                <CardContent className="p-0 overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[40px]"></TableHead>
                        <TableHead className="whitespace-nowrap">Data</TableHead>
                        <TableHead className="whitespace-nowrap">Cliente</TableHead>
                        <TableHead className="whitespace-nowrap">Conteúdo/Título</TableHead>
                        <TableHead className="whitespace-nowrap">Tipo/Prioridade</TableHead>
                        <TableHead className="whitespace-nowrap">Destino/Descrição</TableHead>
                        <TableHead className="whitespace-nowrap">Status</TableHead>
                        <TableHead className="whitespace-nowrap">Responsável</TableHead>
                        <TableHead className="w-[80px] whitespace-nowrap">Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredContents.length > 0 ? (
                        filteredContents.map((content) => (
                          <TableRow key={content.id} className={content.archived ? "opacity-70 bg-amber-50 dark:bg-amber-950/30 relative" : ""}>
                            <TableCell className="flex items-start flex-col gap-2">
                              <span className="text-xs text-muted-foreground">
                                {content.id.substring(0, 8).toUpperCase()}
                                {content.archived && <Badge variant="secondary" className="border border-amber-500 text-zinc-700 dark:text-zinc-300">Arquivada</Badge>}
                              </span>
                              <Checkbox
                                checked={selectedContentIds.includes(content.id)}
                                onCheckedChange={() =>
                                  toggleContentSelection(content.id)
                                }
                              />
                            </TableCell>
                            <TableCell>
                              {content.activityDate
                                ? new Date(content.activityDate).toLocaleDateString("pt-BR", { day: "2-digit", month: "2-digit" })
                                : "-"}
                            </TableCell>
                            <TableCell>
                              <div className="max-w-[150px] truncate" title={(content.isLooseClient ? content.looseClientName : content.client?.name) || 'Cliente desconhecido'}>
                                {content.type === 'content' ? content.client.name : (content.isLooseClient ? content.looseClientName : content.client?.name) || 'Cliente desconhecido'} {content.isLooseClient && <span className="italic text-muted-foreground">⦁ Não fixo</span>}
                              </div>
                            </TableCell>
                            <TableCell>{renderContentCell(content)}</TableCell>
                            <TableCell>{renderTypeCell(content)}</TableCell>
                            <TableCell>{renderDestinationCell(content)}</TableCell>
                            <TableCell>
                              <Badge
                                variant="outline"
                                className={`${getStatusColor(content.status)} px-2 text-center inline-flex justify-center min-w-[100px]`}
                              >
                                {content.status || "Pendente"}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <ContentAssignmentView
                                assignedTo={content.assignedTo}
                                steps={content.steps}
                              />
                            </TableCell>
                            <TableCell>
                              <div className="flex justify-center gap-2">
                                <div className="flex-shrink-0">
                                  <ActivityInfo
                                    activity={{
                                      copywriting: content.copywriting || '',
                                      reference: content.reference || '',
                                      caption: content.type === 'content' ? content.caption || '' : '',
                                      url: Array.isArray(content.urlStructuringFeed) ? content.urlStructuringFeed : content.urlStructuringFeed ? [content.urlStructuringFeed] : undefined,
                                      urlFolder: content.urlFolder || ''
                                    }}
                                  />
                                </div>

                                <div className="flex-shrink-0">
                                  <AddContentReview
                                    size="icon"
                                    witdh="w-9"
                                    contentId={content.id}
                                    currentReview={content.review || ''}
                                    reviewedBy={content.reviewedBy}
                                    onReviewUpdate={(newReview) => handleReviewUpdate(content.id, newReview)}
                                    type={content.type}
                                    onStatusUpdate={(newStatus) => handleStatusUpdate(content.id, newStatus)}
                                  />
                                </div>

                                <div className="flex-shrink-0">
                                  <ArchiveDemand
                                    id={content.id}
                                    type={content.type}
                                    title={content.type === 'general' ? content.title || '' : content.details || ''}
                                    isArchived={content.archived || false}
                                    onSuccess={fetchData}
                                    onLocalUpdate={handleLocalArchive}
                                  />
                                </div>

                                {!content.archived && (
                                  <>
                                    {content.type === 'general' ? (
                                      <div className="flex-shrink-0">
                                        <EditGeneralDemand
                                          demand={{
                                            id: content.id,
                                            title: content.title || '',
                                            description: content.description,
                                            dueDate: content.activityDate,
                                            status: content.status || 'pendente',
                                            priority: content.priority || 'normal',
                                            clientId: content.client?.id || "",
                                            isLooseClient: content.isLooseClient,
                                            looseClientName: content.looseClientName,
                                            assignedToId: content.assignedTo?.id || null,
                                            urlStructuringFeed: content.urlStructuringFeed || []
                                          }}
                                          clients={clients}
                                          onSuccess={fetchData}
                                        />
                                      </div>
                                    ) : content.type === 'content' && (
                                      <div className="flex-shrink-0">
                                        <EditPlanActivity
                                          content={{
                                            clientId: content.client.id,
                                            id: content.id,
                                            activityDate: content.activityDate,
                                            contentType: content.contentType || '',
                                            channel: content.channel || '',
                                            details: content.details || '',
                                            destination: content.destination || '',
                                            copywriting: content.copywriting || '',
                                            reference: content.reference || '',
                                            caption: content.caption || '',
                                            priority: content.priority || 'normal',
                                            status: content.status || 'pendente',
                                            weeklyActivityId: content.weeklyActivityId || '',
                                            urlStructuringFeed: content.urlStructuringFeed || [],
                                            assignedToId: content.assignedTo?.id || null,
                                            assignedTo: content.assignedTo || null
                                          }}
                                          onSuccess={fetchData}
                                        />
                                      </div>
                                    )}
                                  </>
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell
                            colSpan={9}
                            className="h-24 text-center"
                          >
                            {showArchived ? (
                              <div>
                                <p className="text-amber-600 font-medium">Nenhuma demanda arquivada encontrada</p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  Não há demandas arquivadas que correspondam aos filtros atuais
                                </p>
                              </div>
                            ) : (
                              <p className="text-muted-foreground">Nenhuma demanda encontrada</p>
                            )}
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}
          </div>
        ) : (
          <NotAllowed page="/" />
        )}
      </div>
      <Footer />
    </div>
  );
}
