"use client"

import { useState, useEffect, ChangeEvent } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Textarea } from "@/app/components/ui/textarea";
import { Badge } from "@/app/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { MultiSelect } from "@/app/components/ui/multi-select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";
import { Ellipsis, Edit, Trash2, Plus } from "lucide-react";
import { toast } from "sonner";
import { formatPhoneOnInput, formatCurrency, parseSalary } from "@/lib/formatters";

type QuoteRecord = {
    id: string;
    client: string;
    socialMedia?: string[];
    clientPhoneNumber?: string | null;
    budgetType?: string | null;
    services?: ServiceRef[];
    totalPrice?: number | null;
    discount?: number | null;
};

type ServiceRef = {
    Service?: { code?: string };
    BudgetItem?: { code?: string };
    customLabel?: string;
};

type QuoteItemRecord = ServiceRef & {
    quantity?: string | null;
    customDescription?: string | null;
    notes?: string | null;
    include?: boolean | null;
};

export default function EditQuoteDialog({
    quote,
    onUpdated,
    servicesOptions,
    marketingServices,
    budgetTypes,
    templateMaps,
    servicesMeta
}: {
    quote: QuoteRecord;
    onUpdated: () => void;
    servicesOptions: { value: string; label: string }[];
    marketingServices: { value: string; label: string }[];
    budgetTypes: { value: string; label: string }[];
    templateMaps?: Record<string, Record<string, string>>;
    servicesMeta?: { value: string; label: string; defaultValue: string }[];
}) {

    const [servicesForTypeState, setServicesForTypeState] = useState<{ value: string; label: string }[]>([]);

    const dedupeOptions = (arr: { value: string; label: string }[]) => {
        const m = new Map<string, string>();
        const labels = new Set<string>();
        for (const o of arr) {
            if (!o || !o.value) continue;
            const v = String(o.value).trim();
            const lbl = String(o.label ?? '').trim();
            const lblKey = lbl.toLowerCase();
            if (labels.has(lblKey)) continue;
            if (!m.has(v)) {
                m.set(v, lbl);
                labels.add(lblKey);
            }
        }
        return Array.from(m.entries()).map(([value, label]) => ({ value, label }));
    };
    const servicesForType = servicesForTypeState;

    const resolveLabel = (key: string) => {
        const tmplMap = templateMaps && quote.budgetType ? (templateMaps[quote.budgetType] ?? {}) : {};
        return tmplMap[key] ?? servicesForType.find(o => o.value === key)?.label ?? servicesOptions.find(o => o.value === key)?.label ?? marketingServices.find(m => m.value === key)?.label ?? key;
    };
    const [open, setOpen] = useState(false);
    const [client, setClient] = useState<string>(quote.client ?? '');
    const [clientNumberLocal, setClientNumberLocal] = useState<string>(quote.clientPhoneNumber ?? '');
    const [budgetTypeLocal, setBudgetTypeLocal] = useState<string>(quote.budgetType ?? '');
    const [socialMediaLocal, setSocialMediaLocal] = useState<string[]>(quote.socialMedia ?? []);
    const [socialMediaInput, setSocialMediaInput] = useState<string>('');
    const [selectedServicesLocal, setSelectedServicesLocal] = useState<string[]>([]);
    const [selectedBudgetItemsLocal, setSelectedBudgetItemsLocal] = useState<string[]>([]);
    const [serviceDetails, setServiceDetails] = useState<Record<string, { quantity?: string; customDescription?: string; notes?: string; include?: boolean }>>({});
    const [totalPriceLocal, setTotalPriceLocal] = useState<string>(quote.totalPrice ? String(Number(quote.totalPrice)) : '');
    const [discountLocal, setDiscountLocal] = useState<string>(quote.discount ? String(Number(quote.discount)) : '');
    const [totalPriceFocused, setTotalPriceFocused] = useState(false);
    const [discountFocused, setDiscountFocused] = useState(false);
    const [submitting, setSubmitting] = useState(false);

    useEffect(() => {
        if (!open) return;

        const computeInitialSelected = (opts: { value: string; label: string }[]) => {
            const valueSet = new Set(opts.map(o => o.value));
            const labelToValue = new Map<string, string>();
            for (const o of opts) {
                if (o.label) labelToValue.set(String(o.label), o.value);
            }

            const svcKeysRaw = Array.from(new Set((quote.services || []).map((s: ServiceRef) => {
                const rec = s as unknown as Record<string, unknown>;
                return String(rec['key'] ?? s.Service?.code ?? s.BudgetItem?.code ?? s.customLabel ?? '');
            }).filter(Boolean)));

            const mapped: string[] = [];
            for (const k of svcKeysRaw) {
                if (valueSet.has(k)) {
                    mapped.push(k);
                    continue;
                }
                const mappedVal = labelToValue.get(k);
                if (mappedVal) mapped.push(mappedVal);
                else mapped.push(k);
            }

            const present = Array.from(new Set(mapped.filter(v => valueSet.has(v))));
            return present.length > 0 ? present : Array.from(new Set(mapped));
        };

        const ensureInitialOptions = (opts: { value: string; label: string }[]) => {
            const initial = computeInitialSelected(opts);
            const finalOpts = [...opts];
            for (const k of initial) {
                if (finalOpts.some(o => o.value === k)) continue;

                const byLabel = finalOpts.find(o => o.label === k);
                if (byLabel) continue;

                const sr = (quote.services || []).find((s: ServiceRef) => {
                    const rec = s as unknown as Record<string, unknown>;
                    const keyVal = String(rec['key'] ?? s.Service?.code ?? s.BudgetItem?.code ?? s.customLabel ?? '');
                    return keyVal === k || s.customLabel === k;
                }) as unknown as Record<string, unknown> | undefined;
                const labelGuess = sr ? String(sr['customLabel'] ?? k) : k;
                const optByLabel = finalOpts.find(o => o.label === labelGuess);
                if (optByLabel) {
                    if (!finalOpts.some(o => o.value === optByLabel.value)) finalOpts.push({ value: optByLabel.value, label: optByLabel.label });
                } else {
                    finalOpts.push({ value: k, label: labelGuess });
                }
            }
            return { finalOpts: dedupeOptions(finalOpts), initial: initial };
        };

        if (servicesOptions && servicesOptions.length > 0) {
            const { finalOpts, initial } = ensureInitialOptions([...servicesOptions]);
                setServicesForTypeState(finalOpts);
                setSelectedServicesLocal(Array.from(new Set(initial)));
            return;
        }

        if (!budgetTypeLocal) return;

        (async () => {
            try {
                const res = await fetch(`/api/budget-templates?id=${encodeURIComponent(budgetTypeLocal)}`);
                if (!res.ok) return;
                const tmpl = await res.json();
                const opts = (tmpl.services || []).map((s: unknown) => {
                    const rec = s as Record<string, unknown>;
                    const key = String(rec['key'] ?? rec['id'] ?? '');
                    const label = String(rec['label'] ?? rec['name'] ?? key);
                    return { value: key, label };
                });
                const { finalOpts, initial } = ensureInitialOptions(opts);
                setServicesForTypeState(finalOpts);
                setSelectedServicesLocal(Array.from(new Set(initial)));
            } catch {
            }
        })();
    }, [open, budgetTypeLocal, servicesOptions, quote.services]);

    useEffect(() => {
        if (!open) return;
        setSocialMediaLocal(quote.socialMedia ?? []);
    }, [open, quote.socialMedia]);

    useEffect(() => {
        (async () => {
            
            const next: Record<string, { quantity?: string; customDescription?: string; notes?: string; include?: boolean }> = {};

            const metaLookup = new Map<string, string>();
            if (servicesMeta && servicesMeta.length > 0) {
                for (const m of servicesMeta) metaLookup.set(String(m.value), String(m.defaultValue ?? ''));
            } else if (quote.budgetType) {
                try {
                    const r = await fetch(`/api/budget-templates?id=${encodeURIComponent(quote.budgetType)}`);
                    if (r.ok) {
                        const tmpl = await r.json();
                        for (const s of (tmpl.services || []) as unknown as Array<Record<string, unknown>>) {
                            const v = String(s['key'] ?? s['id'] ?? '');
                            const d = String(s['defaultValue'] ?? '');
                            if (v) metaLookup.set(v, d);
                        }
                    }
                } catch {
                }
            }

            if (quote.services && quote.services.length > 0) {
                for (const s of quote.services as QuoteItemRecord[]) {
                    const rec = s as unknown as Record<string, unknown>;
                    const key = String(rec['key'] ?? s.Service?.code ?? s.BudgetItem?.code ?? s.customLabel ?? '');
                    if (!key) continue;
                    const metaDefault = metaLookup.get(key);
                    const tmplRaw = rec['templateSnapshot'] ?? rec['template_snapshot'] ?? rec['snapshot'] ?? rec['template'] ?? undefined;
                    let tmpl: Record<string, unknown> | undefined = undefined;
                    if (typeof tmplRaw === 'string') {
                        try { tmpl = JSON.parse(tmplRaw); } catch { tmpl = undefined; }
                    } else if (typeof tmplRaw === 'object' && tmplRaw !== null) {
                        tmpl = tmplRaw as Record<string, unknown>;
                    }
                    const quantityVal = (s.quantity ?? (tmpl ? String(tmpl['quantity'] ?? tmpl['qty'] ?? tmpl['hours'] ?? '') : undefined)) as string | undefined;
                    const notesVal = (s.notes ?? (tmpl ? String(tmpl['notes'] ?? tmpl['note'] ?? '') : undefined)) as string | undefined;
                    const customDescVal = (s.customDescription ?? (tmpl ? String(tmpl['customDescription'] ?? tmpl['description'] ?? tmpl['custom_desc'] ?? '') : undefined)) as string | undefined;
                    const details = {
                        quantity: quantityVal ?? undefined,
                        customDescription: customDescVal ?? (metaDefault ?? (servicesMeta?.find(mm => mm.value === key)?.defaultValue ?? marketingServices.find(m => m.value === key)?.label ?? '')),
                        notes: notesVal ?? undefined,
                        include: typeof s.include === 'boolean' ? s.include : true,
                    };
                    next[key] = details;

                    try {
                        const labelGuess = String(s.customLabel ?? key);
                        if (templateMaps && quote.budgetType) {
                            const tmplMap = templateMaps[quote.budgetType] ?? {};
                            const foundKey = Object.keys(tmplMap).find(k => String(tmplMap[k]) === labelGuess);
                            if (foundKey) {
                                if (!next[foundKey]) next[foundKey] = details;
                            }
                        }
                        if (!next[labelGuess] && servicesMeta && servicesMeta.length > 0) {
                            const m = servicesMeta.find(mm => String(mm.label) === labelGuess);
                            if (m) {
                                if (!next[m.value]) next[m.value] = details;
                            }
                        }
                    } catch {
                    }
                    try {
                        const rec = s as unknown as Record<string, unknown>;
                        const svc = rec['Service'] as Record<string, unknown> | undefined;
                        const bitem = rec['BudgetItem'] as Record<string, unknown> | undefined;
                        const altKeys: Array<string | undefined> = [
                            svc ? String(svc['id'] ?? undefined) : undefined,
                            svc ? String(svc['code'] ?? undefined) : undefined,
                            bitem ? String(bitem['id'] ?? undefined) : undefined,
                            bitem ? String(bitem['code'] ?? undefined) : undefined,
                            s.customLabel ? String(s.customLabel) : undefined,
                        ];
                        for (const k2 of altKeys) {
                            if (!k2) continue;
                            if (!next[k2]) next[k2] = details;
                        }
                    } catch {
                    }
                }
            }

            for (const s of selectedServicesLocal) {
                if (!next[s]) {
                    const metaDefault = metaLookup.get(s);
                    const defaultDesc = metaDefault ?? (servicesMeta?.find(mm => mm.value === s)?.defaultValue ?? marketingServices.find(m => m.value === s)?.label ?? '');
                    next[s] = { include: true, customDescription: defaultDesc };
                }
            }

            setServiceDetails(next);
        })();
    }, [selectedServicesLocal, open, quote.services, marketingServices, quote.budgetType, servicesMeta, templateMaps]);

    const handleSave = async () => {
        setSubmitting(true);
        try {
            const payloadServices = selectedServicesLocal.map(s => ({
                key: s,
                quantity: serviceDetails[s]?.quantity ?? null,
                customDescription: serviceDetails[s]?.customDescription ?? null,
                notes: serviceDetails[s]?.notes ?? null,
                include: typeof serviceDetails[s]?.include === 'boolean' ? serviceDetails[s]!.include : true,
                unitPrice: null,
                itemTotal: null,
                budgetTemplateId: budgetTypeLocal || null,
                templateSnapshot: { quantity: serviceDetails[s]?.quantity ?? null, customDescription: serviceDetails[s]?.customDescription ?? null, notes: serviceDetails[s]?.notes ?? null }
            }));

            const res = await fetch(`/api/quotes/${quote.id}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    client: client.trim(),
                    clientNumber: clientNumberLocal.trim(),
                    budgetType: budgetTypeLocal || null,
                    socialMedia: socialMediaLocal,
                    services: payloadServices,
                    budgetItemIds: selectedBudgetItemsLocal,
                    totalPrice: totalPriceLocal ? Number(totalPriceLocal.replace(',', '.')) : null,
                    discount: discountLocal ? Number(discountLocal.replace(',', '.')) : null
                })
            });
            if (!res.ok) {
                const err = await res.json().catch(() => ({}));
                toast.error(err?.error || 'Erro ao atualizar orçamento');
                return;
            }
            toast.success('Orçamento atualizado');
            setOpen(false);
            onUpdated();
        } catch (err) {
            console.error('Erro atualizar orçamento:', err);
            toast.error('Erro ao atualizar orçamento');
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="icon" title="Editar orçamento"><Edit size={16} /></Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>Editar orçamento</DialogTitle>
                </DialogHeader>
                <div className="grid grid-cols-1 gap-4">
                    <div>
                        <Label>Tipo de orçamento</Label>
                        <Select onValueChange={(v) => setBudgetTypeLocal(v)} disabled>
                            <SelectTrigger>
                                <SelectValue placeholder="Selecione um tipo de orçamento" />
                            </SelectTrigger>
                            <SelectContent>
                                {budgetTypes.map(opt => (
                                    <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div className="grid xs:grid-cols-2 gap-4">
                        <div>
                            <Label>Cliente</Label>
                            <Input value={client} onChange={(e) => setClient(e.target.value)} />
                        </div>
                        <div>
                            <Label>Telefone</Label>
                            <Input value={formatPhoneOnInput(clientNumberLocal)} onChange={(e) => setClientNumberLocal(e.target.value.replace(/\D/g, '').slice(0, 11))} />
                        </div>
                    </div>
                    <div>
                        <Label>Redes sociais</Label>
                        <div className="flex gap-2">
                            <Input
                                value={socialMediaInput}
                                onChange={(e) => setSocialMediaInput(e.target.value)}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        e.preventDefault();
                                        const v = socialMediaInput.trim();
                                        if (v && !socialMediaLocal.includes(v)) setSocialMediaLocal(prev => [...prev, v]);
                                        setSocialMediaInput('');
                                    }
                                }}
                                placeholder="Digite e pressione Enter"
                            />
                            <Button onClick={() => { const v = socialMediaInput.trim(); if (!v) return; if (!socialMediaLocal.includes(v)) setSocialMediaLocal(prev => [...prev, v]); setSocialMediaInput(''); }}>
                                <Plus size={16} />
                            </Button>
                        </div>
                        {socialMediaLocal.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-2">
                                {socialMediaLocal.map((s, idx) => (
                                    <div key={idx} className="flex items-center">
                                        <Badge variant="secondary" className="rounded-full">{s}</Badge>
                                        <Button size="icon" variant="link" onClick={() => setSocialMediaLocal(prev => prev.filter(x => x !== s))} title={`Remover ${s}`}>
                                            <Trash2 className="h-4 w-4 text-red-500" />
                                        </Button>
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>
                    <div>
                        <Label>Serviços</Label>
                        <MultiSelect options={servicesForType} selected={selectedServicesLocal} onChange={(vals: string[]) => { setSelectedServicesLocal(vals); setSelectedBudgetItemsLocal([]); }} />
                    </div>
                    <div className="grid xs:grid-cols-2 gap-4">
                        <div>
                            <Label>Preço do orçamento (R$)</Label>
                            <Input
                                value={totalPriceFocused ? totalPriceLocal : formatCurrency(totalPriceLocal)}
                                onChange={(e) => setTotalPriceLocal(e.target.value.replace(/[^0-9,.,-]/g, '').replace(/\.+/g, '.'))}
                                onFocus={() => setTotalPriceFocused(true)}
                                onBlur={() => {
                                    setTotalPriceFocused(false);
                                    const parsed = parseSalary(totalPriceLocal);
                                    setTotalPriceLocal(parsed === null ? '' : String(parsed));
                                }}
                            />
                        </div>
                        <div>
                            <Label>Desconto (R$)</Label>
                            <Input
                                value={discountFocused ? discountLocal : formatCurrency(discountLocal)}
                                onChange={(e) => setDiscountLocal(e.target.value.replace(/[^0-9,.,-]/g, '').replace(/\.+/g, '.'))}
                                onFocus={() => setDiscountFocused(true)}
                                onBlur={() => {
                                    setDiscountFocused(false);
                                    const parsed = parseSalary(discountLocal);
                                    setDiscountLocal(parsed === null ? '' : String(parsed));
                                }}
                            />
                        </div>
                    </div>
                </div>
                {budgetTypeLocal && selectedServicesLocal.length > 0 && (
                    <div className="py-4 border-t">
                        <h3 className="text-sm font-medium mb-2">Detalhes dos serviços selecionados</h3>
                        <div className="grid gap-3">
                            {selectedServicesLocal.map((s) => (
                                <div key={s} className="p-4 border rounded">
                                    <Badge variant="success" className="py-1 rounded-full mb-1">{resolveLabel(s)}</Badge>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="space-y-1">
                                            <Label>Descrição</Label>
                                            <Textarea
                                                value={serviceDetails[s]?.customDescription ?? ''}
                                                onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setServiceDetails(prev => ({ ...prev, [s]: { ...(prev[s] ?? {}), customDescription: e.target.value } }))}
                                                rows={5}
                                            />
                                        </div>
                                        <div className="flex flex-col xs:flex-row md:flex-col h-full justify-between gap-2">
                                            <div className="space-y-1">
                                                <Label>Quantidade</Label>
                                                <Input type="text" value={serviceDetails[s]?.quantity ?? ''} onChange={(e: ChangeEvent<HTMLInputElement>) => setServiceDetails(prev => ({ ...prev, [s]: { ...(prev[s] ?? {}), quantity: e.target.value } }))} />
                                            </div>
                                            <div className="space-y-1">
                                                <Label>Horas / Observações</Label>
                                                <Input value={serviceDetails[s]?.notes ?? ''} onChange={(e: ChangeEvent<HTMLInputElement>) => setServiceDetails(prev => ({ ...prev, [s]: { ...(prev[s] ?? {}), notes: e.target.value } }))} />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
                <div className="flex flex-col xs:flex-row justify-end gap-2">
                    <Button variant="outline" onClick={() => setOpen(false)}>Cancelar</Button>
                    <Button onClick={handleSave} disabled={submitting}>{submitting ? <Ellipsis /> : 'Salvar'}</Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}
