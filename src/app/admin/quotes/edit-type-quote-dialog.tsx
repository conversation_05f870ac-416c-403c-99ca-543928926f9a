"use client"

import { useState, useEffect, ChangeEvent } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/app/components/ui/card';
import { Label } from '@/app/components/ui/label';
import { Input } from '@/app/components/ui/input';
import { Button } from '@/app/components/ui/button';
import { toast } from 'sonner';
import { Trash } from 'lucide-react';
import { Textarea } from '@/app/components/ui/textarea';

type NewServiceField = { key: string; label: string; description?: string };

export default function EditTypeQuoteDialog({
    isOpen,
    onClose,
    initial,
    onSaved
}: {
    isOpen: boolean;
    onClose: () => void;
    initial?: { id?: string; name?: string; description?: string; services?: unknown[] };
    onSaved?: (templates?: { value: string; label: string }[]) => void;
}) {
    const [name, setName] = useState(initial?.name ?? '');
    const [description, setDescription] = useState(initial?.description ?? '');
    const [services, setServices] = useState<NewServiceField[]>(() => (initial?.services || []).map((s: unknown) => { const rec = s as Record<string, unknown>; return ({ key: String(rec.key ?? ''), label: String(rec.label ?? ''), description: String(rec.defaultValue ?? '') }); }));
    const [saving, setSaving] = useState(false);

    useEffect(() => {
        if (!isOpen) return;
        setName(initial?.name ?? '');
        setDescription(initial?.description ?? '');
        setServices((initial?.services || []).map((s: unknown) => { const rec = s as Record<string, unknown>; return ({ key: String(rec.key ?? ''), label: String(rec.label ?? ''), description: String(rec.defaultValue ?? '') }); }));
    }, [isOpen, initial]);

    const addService = () => setServices(s => [...s, { key: `field_${Date.now()}`, label: '', description: '' }]);
    const removeService = (idx: number) => setServices(s => s.filter((_, i) => i !== idx));

    const save = async () => {
        if (!name.trim()) { toast.error('Nome é obrigatório'); return; }
        setSaving(true);
        try {
            const payloadAny: Record<string, unknown> = { name: name.trim(), description: description.trim(), fields: services.map(s => ({ key: s.key, label: s.label || s.key, type: 'STRING', defaultValue: s.description ?? null })) };
            let res: Response;
            if (initial?.id) {
                payloadAny.id = initial.id;
                res = await fetch('/api/budget-templates', { method: 'PATCH', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payloadAny) });
            } else {
                res = await fetch('/api/budget-templates', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payloadAny) });
            }
            if (!res.ok) {
                const err = await res.json().catch(() => ({}));
                toast.error(err?.error || 'Erro ao salvar tipo de orçamento');
                return;
            }
            toast.success('Tipo de orçamento salvo');
            if (onSaved) {
                const r = await fetch('/api/budget-templates');
                if (r.ok) {
                    const data = await r.json();
                    onSaved((data || []).map((t: unknown) => { const rec = t as Record<string, unknown>; return ({ value: String(rec.id ?? ''), label: String(rec.name ?? '') }); }));
                } else onSaved();
            }
            onClose();
        } catch (err) {
            console.error(err);
            toast.error('Erro ao salvar tipo de orçamento');
        } finally { setSaving(false); }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            <div className="absolute inset-0 bg-black/40" onClick={onClose} />
            <Card className="z-50 w-full max-w-xl mx-4">
                <CardHeader>
                    <CardTitle>{initial?.id ? 'Editar tipo de orçamento' : 'Editar tipo de orçamento'}</CardTitle>
                    <CardDescription>Edite o tipo de orçamento.</CardDescription>
                </CardHeader>
                <CardContent className='overflow-y-auto max-h-[70vh]'>
                    <div className="grid grid-cols-1 gap-4">
                        <div>
                            <Label>Nome</Label>
                            <Input value={name} onChange={(e: ChangeEvent<HTMLInputElement>) => setName(e.target.value)} placeholder="Nome do tipo de orçamento" />
                        </div>
                        <div>
                            <Label>Descrição</Label>
                            <Input value={description} onChange={(e: ChangeEvent<HTMLInputElement>) => setDescription(e.target.value)} placeholder="Descrição (opcional)" />
                        </div>
                        <div>
                            <div className="flex items-end gap-8 justify-between">
                                <div>
                                    <Label>Serviços</Label>
                                    <div className="text-xs text-zinc-500">Adicione os campos que estarão disponíveis para cada item deste tipo de orçamento.</div>
                                </div>
                                <Button size="sm" variant="outline" onClick={addService}>Adicionar serviço</Button>
                            </div>
                            <div className="mt-3 space-y-2">
                                {services.map((svc, idx) => (
                                    <div key={svc.key} className="px-4 py-2 space-y-2 border rounded">
                                        <div className='space-y-1'>
                                            <Label>Nome</Label>
                                            <Input
                                                value={svc.label}
                                                onChange={(e: ChangeEvent<HTMLInputElement>) => setServices(prev => { const next = [...prev]; next[idx] = { ...next[idx], label: e.target.value }; return next; })}
                                                placeholder="Nome do serviço"
                                            />
                                        </div>
                                        <div className='space-y-1'>
                                            <Label>Descrição</Label>
                                            <Textarea
                                                value={svc.description ?? ''}
                                                onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setServices(prev => { const next = [...prev]; next[idx] = { ...next[idx], description: e.target.value }; return next; })}
                                                placeholder="Descrição do serviço"
                                            />
                                        </div>
                                        <div className='flex justify-end'>
                                            <Button
                                                variant="outline"
                                                onClick={() => removeService(idx)}
                                            >
                                                <Trash size={16} color="red" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </CardContent>
                <div className="flex justify-end gap-2 p-4">
                    <Button variant="outline" onClick={onClose}>Cancelar</Button>
                    <Button onClick={save} disabled={saving}>{saving ? 'Salvando' : 'Salvar'}</Button>
                </div>
            </Card>
        </div>
    );
}