import { NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

export async function PATCH(request: Request) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email as string },
            select: { role: true },
        });

        if (user?.role !== "ADMIN" && user?.role !== "DEVELOPER") {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const url = new URL(request.url);
        const activityId = url.pathname.split('/').pop();
        
        const body = await request.json();
        const { description } = body;

        if (description === undefined) {
            return NextResponse.json(
                { error: "A descrição é obrigatória" },
                { status: 400 }
            );
        }

        const existingActivity = await prisma.weeklyActivity.findUnique({
            where: { id: activityId },
        });

        if (!existingActivity) {
            return NextResponse.json(
                { error: "Atividade não encontrada" },
                { status: 404 }
            );
        }

        const updatedActivities = await prisma.weeklyActivity.updateMany({
            where: {
                week: existingActivity.week,
                monthlyPlanningId: existingActivity.monthlyPlanningId
            },
            data: { description },
        });

        revalidatePath(`/monthly-planning/${existingActivity.monthlyPlanningId}`);

        return NextResponse.json({
            success: true,
            updatedCount: updatedActivities.count
        });
    } catch (error) {
        console.error("Erro ao atualizar atividades:", error);
        return NextResponse.json(
            { error: "Erro ao atualizar atividades" },
            { status: 500 }
        );
    }
}