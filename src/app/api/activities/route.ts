import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { Prisma } from '@prisma/client';

const ALLOWED_ROLES = ["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT", "DESIGNER_SENIOR"];

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
    if (!requestingUser) {
      return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
    }

    if (!ALLOWED_ROLES.includes(requestingUser.role)) {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const url = new URL(req.url);
    const monthlyPlanningId = url.searchParams.get('monthlyPlanningId') ?? undefined;
    const weekParam = url.searchParams.get('week');
    const week = weekParam ? parseInt(weekParam, 10) : undefined;

    const whereClause: Record<string, unknown> = {};
    if (monthlyPlanningId) whereClause.monthlyPlanningId = monthlyPlanningId;
    if (week !== undefined && !Number.isNaN(week)) whereClause.week = week;

    const activities = await prisma.weeklyActivity.findMany({
      where: whereClause as Prisma.WeeklyActivityWhereInput,
    });

    return NextResponse.json(activities);
  } catch (error) {
    console.error('Erro ao listar atividades semanais:', error);
    return NextResponse.json({ message: 'Erro ao listar atividades' }, { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    try {
      const session = await getServerSession(authOptions);
      if (!session?.user?.email) {
        return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
      }

      const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
      if (!requestingUser) {
        return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
      }

      if (!ALLOWED_ROLES.includes(requestingUser.role)) {
        return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
      }
    } catch (error) {
      console.error("Erro de autenticação ao criar atividade:", error);
      return NextResponse.json({ error: "Erro de autenticação" }, { status: 500 });
    }

    const data = await req.json();
    const { monthlyPlanningId, week, description } = data;

    if (!monthlyPlanningId || week === undefined) {
      return NextResponse.json(
        { message: "Dados incompletos: monthlyPlanningId e week são obrigatórios" },
        { status: 400 }
      );
    }

    const weekNumber = Number(week);
    if (isNaN(weekNumber)) {
      return NextResponse.json(
        { message: "O campo 'week' deve ser um número válido." },
        { status: 400 }
      );
    }

    const planningExists = await prisma.monthlyPlanning.findUnique({
      where: { id: monthlyPlanningId }
    });

    if (!planningExists) {
      return NextResponse.json({ message: "Planejamento não encontrado." }, { status: 404 });
    }

    const newActivity = await prisma.weeklyActivity.create({
      data: {
        monthlyPlanningId,
        week: weekNumber,
        description
      }
    });

    return NextResponse.json(newActivity);
  } catch (error) {
    console.error("Erro ao criar atividade:", error);

    return NextResponse.json({
      message: "Erro interno do servidor",
      details: error instanceof Error ? error.message : "Erro desconhecido"
    }, { status: 500 });
  }
}
