import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const adminRoles = ["ADMIN", "DEVELOPER"];
        
        if (!adminRoles.includes(user.role)) {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const { id } = await params;

        const auditLogs = await prisma.pointRecordAudit.findMany({
            where: { pointRecordId: id },
            include: {
                editedByUser: {
                    select: {
                        name: true,
                        email: true
                    }
                }
            },
            orderBy: { editedAt: 'desc' }
        });

        const formattedLogs = auditLogs.map(log => ({
            id: log.id,
            action: log.action,
            field: log.field,
            oldValue: log.oldValue,
            newValue: log.newValue,
            editedBy: log.editedBy,
            editedByName: log.editedByUser.name,
            editedAt: log.editedAt.toISOString()
        }));

        return NextResponse.json(formattedLogs);
    } catch (error) {
        console.error("Erro ao buscar auditoria:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}