import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";
import { createAuditLog } from "@/app/lib/audit";

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const adminRoles = ["ADMIN", "DEVELOPER"];
        
        if (!adminRoles.includes(user.role)) {
            return NextResponse.json({ error: "<PERSON><PERSON> negado" }, { status: 403 });
        }

        const { id } = await params;
        const { createdBy } = await request.json();

        const pointRecord = await prisma.pointRecord.findUnique({
            where: { id }
        });

        if (!pointRecord) {
            return NextResponse.json({ error: "Registro não encontrado" }, { status: 404 });
        }

        const oldCreatedBy = pointRecord.createdBy;
        
        await prisma.pointRecord.update({
            where: { id },
            data: {
                createdBy: createdBy
            }
        });

        await createAuditLog(id, 'UPDATE', user.id, 'createdBy', oldCreatedBy || undefined, createdBy);

        return NextResponse.json({ message: "Autor atualizado com sucesso" });
    } catch (error) {
        console.error("Erro ao atualizar autor do registro:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}