import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";
import { createAuditLog } from "@/app/lib/audit";

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const adminRoles = ["ADMIN", "DEVELOPER"];
        
        if (!adminRoles.includes(user.role)) {
            return NextResponse.json({ error: "<PERSON><PERSON> negado" }, { status: 403 });
        }

        const { id } = await params;
        const { clockIn, clockOut, date } = await request.json();

        if (!date) {
            return NextResponse.json({ error: "Data é obrigatória" }, { status: 400 });
        }

        const pointRecord = await prisma.pointRecord.findUnique({
            where: { id }
        });

        if (!pointRecord) {
            return NextResponse.json({ error: "Registro não encontrado" }, { status: 404 });
        }

        let totalHours = null;
        let timeBank = 0;
        if (clockIn && clockOut) {
            const clockInDate = new Date(clockIn + '-03:00');
            const clockOutDate = new Date(clockOut + '-03:00');
            totalHours = (clockOutDate.getTime() - clockInDate.getTime()) / (1000 * 60 * 60);
            timeBank = totalHours > 8.8 ? totalHours - 8.8 : 0;
        }

        const changes = [];
        if (clockIn !== (pointRecord.clockIn?.toISOString().slice(0, 19))) {
            changes.push({ field: 'clockIn', oldValue: pointRecord.clockIn?.toISOString(), newValue: clockIn });
        }
        if (clockOut !== (pointRecord.clockOut?.toISOString().slice(0, 19))) {
            changes.push({ field: 'clockOut', oldValue: pointRecord.clockOut?.toISOString(), newValue: clockOut });
        }
        if (date !== pointRecord.date.toISOString().split('T')[0]) {
            changes.push({ field: 'date', oldValue: pointRecord.date.toISOString().split('T')[0], newValue: date });
        }

        const updatedRecord = await prisma.pointRecord.update({
            where: { id },
            data: {
                clockIn: clockIn ? new Date(clockIn + '-03:00') : null,
                clockOut: clockOut ? new Date(clockOut + '-03:00') : null,
                date: (() => {
                    const [year, month, day] = date.split('-').map(Number);
                    return new Date(year, month - 1, day);
                })(),
                totalHours,
                timeBank
            },
            include: {
                user: {
                    select: {
                        name: true,
                        email: true
                    }
                }
            }
        });

        for (const change of changes) {
            await createAuditLog(id, 'UPDATE', user.id, change.field, change.oldValue, change.newValue);
        }

        const formattedRecord = {
            id: updatedRecord.id,
            userId: updatedRecord.userId,
            userName: updatedRecord.user.name || 'Usuário',
            userEmail: updatedRecord.user.email,
            clockIn: updatedRecord.clockIn?.toISOString(),
            clockOut: updatedRecord.clockOut?.toISOString(),
            date: updatedRecord.date.toISOString().split('T')[0],
            totalHours: updatedRecord.totalHours,
            createdAt: updatedRecord.createdAt.toISOString()
        };

        return NextResponse.json(formattedRecord);
    } catch (error) {
        console.error("Erro ao atualizar registro de ponto:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const adminRoles = ["ADMIN", "DEVELOPER"];
        
        if (!adminRoles.includes(user.role)) {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const { id } = await params;

        const pointRecord = await prisma.pointRecord.findUnique({
            where: { id }
        });

        if (!pointRecord) {
            return NextResponse.json({ error: "Registro não encontrado" }, { status: 404 });
        }

        await createAuditLog(id, 'DELETE', user.id);

        await prisma.pointRecord.delete({
            where: { id }
        });

        return NextResponse.json({ message: "Registro excluído com sucesso" });
    } catch (error) {
        console.error("Erro ao excluir registro de ponto:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}