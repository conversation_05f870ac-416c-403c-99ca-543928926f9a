import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";
import { createAuditLog } from "@/app/lib/audit";

export async function GET() {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const adminRoles = ["ADMIN", "DEVELOPER"];
        
        if (!adminRoles.includes(user.role)) {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const pointRecords = await prisma.pointRecord.findMany({
            include: {
                user: {
                    select: {
                        name: true,
                        email: true
                    }
                },
                createdByUser: {
                    select: {
                        name: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });

        const recordsWithAudit = await Promise.all(
            pointRecords.map(async (record) => {
                const hasEdits = await prisma.pointRecordAudit.findFirst({
                    where: { 
                        pointRecordId: record.id,
                        action: 'UPDATE'
                    }
                });

                return {
                    id: record.id,
                    userId: record.userId,
                    userName: record.user.name || 'Usuário',
                    userEmail: record.user.email,
                    clockIn: record.clockIn?.toISOString(),
                    clockOut: record.clockOut?.toISOString(),
                    date: record.date.toISOString().split('T')[0],
                    totalHours: record.totalHours,
                    timeBank: record.timeBank,
                    createdAt: record.createdAt.toISOString(),
                    createdBy: record.createdBy,
                    createdByName: record.createdByUser?.name,
                    hasEdits: !!hasEdits
                };
            })
        );

        return NextResponse.json(recordsWithAudit);
    } catch (error) {
        console.error("Erro ao buscar registros de ponto:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const adminRoles = ["ADMIN", "DEVELOPER"];
        
        if (!adminRoles.includes(user.role)) {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const { userId, clockIn, clockOut, date } = await request.json();

        if (!userId || !date) {
            return NextResponse.json({ error: "Dados obrigatórios não fornecidos" }, { status: 400 });
        }

        const targetUser = await prisma.user.findUnique({
            where: { id: userId }
        });

        if (!targetUser) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        let totalHours = null;
        let timeBank = 0;
        if (clockIn && clockOut) {
            const clockInDate = new Date(clockIn + '-03:00');
            const clockOutDate = new Date(clockOut + '-03:00');
            totalHours = (clockOutDate.getTime() - clockInDate.getTime()) / (1000 * 60 * 60);
            timeBank = totalHours > 8.8 ? totalHours - 8.8 : 0;
        }

        const pointRecord = await prisma.pointRecord.create({
            data: {
                userId,
                clockIn: clockIn ? new Date(clockIn + '-03:00') : null,
                clockOut: clockOut ? new Date(clockOut + '-03:00') : null,
                date: (() => {
                    const [year, month, day] = date.split('-').map(Number);
                    return new Date(year, month - 1, day);
                })(),
                totalHours,
                timeBank,
                createdBy: user.id
            },
            include: {
                user: {
                    select: {
                        name: true,
                        email: true
                    }
                },
                createdByUser: {
                    select: {
                        name: true
                    }
                }
            }
        });

        const formattedRecord = {
            id: pointRecord.id,
            userId: pointRecord.userId,
            userName: pointRecord.user.name || 'Usuário',
            userEmail: pointRecord.user.email,
            clockIn: pointRecord.clockIn?.toISOString(),
            clockOut: pointRecord.clockOut?.toISOString(),
            date: pointRecord.date.toISOString().split('T')[0],
            totalHours: pointRecord.totalHours,
            createdAt: pointRecord.createdAt.toISOString(),
            createdBy: pointRecord.createdBy,
            createdByName: pointRecord.createdByUser?.name
        };

        // Registrar auditoria de criação
        await createAuditLog(pointRecord.id, 'CREATE', user.id);

        return NextResponse.json(formattedRecord, { status: 201 });
    } catch (error) {
        console.error("Erro ao criar registro de ponto:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}

