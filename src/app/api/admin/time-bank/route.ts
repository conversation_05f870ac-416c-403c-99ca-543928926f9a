import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

interface Period {
    start: Date;
    end: Date;
    hours: number;
}

interface DayData {
    periods: Period[];
    totalHours: number;
}

interface UserDailyData {
    userName: string;
    userEmail: string;
    dailyRecords: Record<string, DayData>;
}

interface TimeBankUser {
    userId: string;
    userName: string;
    userEmail: string;
    totalTimeBank: number;
}

export async function GET() {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const adminRoles = ["ADMIN", "DEVELOPER"];

        if (!adminRoles.includes(user.role)) {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const today = new Date();
        const todayString = today.toLocaleDateString('en-CA');

        const excusedDays = await prisma.excusedDay.findMany({
            where: {
                date: {
                    lt: new Date(todayString + 'T00:00:00')
                }
            }
        });

        const records = await prisma.pointRecord.findMany({
            where: {
                date: {
                    lt: new Date(todayString + 'T00:00:00')
                },
            },
            include: {
                user: {
                    select: {
                        name: true,
                        email: true
                    }
                }
            },
            orderBy: {
                date: 'asc'
            }
        });

        const completeRecords = records.filter(record => record.clockIn && record.clockOut && record.totalHours);

        const userDailyHours = completeRecords.reduce((acc: Record<string, UserDailyData>, record) => {
            const userId = record.userId;
            const dateKey = record.date.toISOString().split('T')[0];

            if (!acc[userId]) {
                acc[userId] = {
                    userName: record.user.name || 'Usuário',
                    userEmail: record.user.email,
                    dailyRecords: {}
                };
            }

            if (!acc[userId].dailyRecords[dateKey]) {
                acc[userId].dailyRecords[dateKey] = {
                    periods: [],
                    totalHours: 0
                };
            }

            acc[userId].dailyRecords[dateKey].periods.push({
                start: new Date(record.clockIn!),
                end: new Date(record.clockOut!),
                hours: record.totalHours ?? 0
            });

            return acc;
        }, {});

        Object.keys(userDailyHours).forEach(userId => {
            Object.keys(userDailyHours[userId].dailyRecords).forEach(dateKey => {
                const dayData = userDailyHours[userId].dailyRecords[dateKey];

                dayData.periods.sort((a: Period, b: Period) => a.start.getTime() - b.start.getTime());

                dayData.totalHours = dayData.periods.reduce((sum: number, period: Period) => sum + period.hours, 0);

                dayData.totalHours = Math.min(dayData.totalHours, 24);
            });
        });

        const allUserIds = new Set([...Object.keys(userDailyHours), ...excusedDays.map(e => e.userId)]);

        const timeBankData: TimeBankUser[] = Array.from(allUserIds).map(userId => {
            const userData = userDailyHours[userId];
            const userExcusedDays = excusedDays.filter(e => e.userId === userId);

            const weeklyData: Record<string, { totalHours: number, workDays: number }> = {};

            if (userData) {
                Object.entries(userData.dailyRecords).forEach(([dateKey, dayData]: [string, DayData]) => {
                    const date = new Date(dateKey + 'T12:00:00');
                    const dayOfWeek = date.getDay();

                    if (dayOfWeek === 0) return;

                    const monday = new Date(date);
                    monday.setDate(date.getDate() - dayOfWeek + 1);
                    const weekKey = monday.toISOString().split('T')[0];

                    if (!weeklyData[weekKey]) {
                        weeklyData[weekKey] = { totalHours: 0, workDays: 0 };
                    }

                    weeklyData[weekKey].totalHours += dayData.totalHours;
                    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
                        weeklyData[weekKey].workDays += 1;
                    }
                });
            }

            userExcusedDays.forEach(excused => {
                const dateKey = excused.date.toISOString().split('T')[0];
                const date = new Date(dateKey + 'T12:00:00');
                const dayOfWeek = date.getDay();

                if (dayOfWeek === 0) return;

                const monday = new Date(date);
                monday.setDate(date.getDate() - dayOfWeek + 1);
                const weekKey = monday.toISOString().split('T')[0];

                if (!weeklyData[weekKey]) {
                    weeklyData[weekKey] = { totalHours: 0, workDays: 0 };
                }

                const isFalta = typeof excused.reason === 'string' && excused.reason.startsWith('FALTA|');

                if (dayOfWeek >= 1 && dayOfWeek <= 5) {
                    if (isFalta) {
                        weeklyData[weekKey].workDays += 1;
                    } else {
                        weeklyData[weekKey].totalHours += 8.8;
                        weeklyData[weekKey].workDays += 1;
                    }
                }
            });

            let totalTimeBank = 0;
            Object.entries(weeklyData).forEach(([, week]) => {
                const weeklyHours = week.totalHours;
                const workDays = week.workDays;

                const DAILY_EXPECTED_HOURS = 8.8;
                let expectedHours = 0;
                if (workDays >= 5) {
                    expectedHours = 44;
                } else {
                    expectedHours = workDays * DAILY_EXPECTED_HOURS;
                }

                if (weeklyHours >= expectedHours) {
                    totalTimeBank += (weeklyHours - expectedHours);
                } else {
                    totalTimeBank -= (expectedHours - weeklyHours);
                }
            });

            return {
                userId,
                userName: userData?.userName || 'Usuário',
                userEmail: userData?.userEmail || '',
                totalTimeBank: totalTimeBank
            };
        });

        const filteredTimeBankData = timeBankData.filter(user => user.totalTimeBank !== 0);

        return NextResponse.json(filteredTimeBankData);

    } catch (error) {
        console.error("Erro ao calcular banco de horas:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}