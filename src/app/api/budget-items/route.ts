import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(req: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const user = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!user) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const adminRoles = ['ADMIN', 'DEVELOPER'];
        if (!adminRoles.includes(user.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const templateId = url.searchParams.get('templateId') || undefined;
        const items = await prisma.budgetItem.findMany({
            where: templateId ? { budgetTemplateId: templateId } : {},
            orderBy: { sortOrder: 'asc' },
            select: { id: true, code: true, name: true, description: true }
        });
        return NextResponse.json(items);
    } catch (err) {
        console.error('GET /api/budget-items error', err);
        return NextResponse.json({ error: 'Erro ao listar itens de orçamento' }, { status: 500 });
    }
}
