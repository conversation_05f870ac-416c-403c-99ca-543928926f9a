import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

interface CalendarUpdateParams {
    name?: string;
    description?: string;
    color?: string;
    archived?: boolean;
}

export async function GET(
    req: Request
) {
    try {
        const url = new URL(req.url);
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const id = url.pathname.split('/').pop();

        const calendar = await prisma.calendar.findUnique({
            where: {
                id: id,
            },
            include: {
                events: {
                    orderBy: {
                        startDate: 'asc',
                    },
                },
                client: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
        });

        if (!calendar) {
            return NextResponse.json(
                { error: "Calendário não encontrado" },
                { status: 404 }
            );
        }

        return NextResponse.json(calendar);
    } catch (error) {
        console.error("Erro ao buscar calendário:", error);
        return NextResponse.json(
            { error: "Erro ao buscar calendário" },
            { status: 500 }
        );
    }
}

export async function PATCH(
    req: Request,
) {
    try {
        const url = new URL(req.url);
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const id = url.pathname.split('/').pop();

        const body: CalendarUpdateParams = await req.json();

        const calendar = await prisma.calendar.findUnique({
            where: {
                id: id,
            },
        });

        if (!calendar) {
            return NextResponse.json(
                { error: "Calendário não encontrado" },
                { status: 404 }
            );
        }

        const updatedCalendar = await prisma.calendar.update({
            where: {
                id: id,
            },
            data: {
                name: body.name,
                description: body.description,
                color: body.color,
                archived: body.archived,
                updatedAt: new Date(),
            },
        });

        return NextResponse.json(updatedCalendar);
    } catch (error) {
        console.error("Erro ao atualizar calendário:", error);
        return NextResponse.json(
            { error: "Erro ao atualizar calendário" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    req: Request,
) {
    try {
        const url = new URL(req.url);
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }
        const id = url.pathname.split('/').pop();

        const calendar = await prisma.calendar.findUnique({
            where: {
                id: id,
            },
        });

        if (!calendar) {
            return NextResponse.json(
                { error: "Calendário não encontrado" },
                { status: 404 }
            );
        }

        await prisma.event.deleteMany({
            where: {
                calendarId: id,
            },
        });

        await prisma.calendar.delete({
            where: {
                id: id,
            },
        });

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("Erro ao excluir calendário:", error);
        return NextResponse.json(
            { error: "Erro ao excluir calendário" },
            { status: 500 }
        );
    }
}
