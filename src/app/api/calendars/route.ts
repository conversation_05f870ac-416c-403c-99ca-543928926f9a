import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

interface CalendarCreateParams {
    name: string;
    description?: string;
    color?: string;
    clientId: string;
}

export async function GET(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const url = new URL(req.url);
        const clientId = url.searchParams.get('clientId');
        const showArchived = url.searchParams.get('archived') === 'true';
        const getAllCalendars = url.searchParams.get('all') === 'true';

        if (getAllCalendars) {
            const allCalendars = await prisma.calendar.findMany({
                where: {
                    archived: false,
                },
                include: {
                    client: {
                        select: {
                            id: true,
                            name: true,
                        },
                    },
                },
                orderBy: {
                    name: 'asc',
                },
            });
            return NextResponse.json(allCalendars);
        }

        if (!clientId) {
            return NextResponse.json({ error: "ID do cliente é obrigatório" }, { status: 400 });
        }

        const calendars = await prisma.calendar.findMany({
            where: {
                clientId: clientId,
                archived: showArchived,
            },
            include: {
                client: {
                    select: {
                        id: true,
                        name: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });

        return NextResponse.json(calendars);
    } catch (error) {
        console.error("Erro ao buscar calendários:", error);
        return NextResponse.json(
            { error: "Erro ao buscar calendários" },
            { status: 500 }
        );
    }
}

export async function POST(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const body: CalendarCreateParams & { sourceCalendarId?: string } = await req.json();

        if (!body.name || !body.clientId) {
            return NextResponse.json(
                { error: "Nome e ID do cliente são obrigatórios" },
                { status: 400 }
            );
        }

        const client = await prisma.client.findUnique({
            where: {
                id: body.clientId,
            },
        });

        if (!client) {
            return NextResponse.json(
                { error: "Cliente não encontrado" },
                { status: 404 }
            );
        }

        if (body.sourceCalendarId) {
            const sourceCalendar = await prisma.calendar.findUnique({
                where: {
                    id: body.sourceCalendarId,
                },
                include: {
                    events: true,
                    holidays: true,
                },
            });

            if (!sourceCalendar) {
                return NextResponse.json(
                    { error: "Calendário de origem não encontrado" },
                    { status: 404 }
                );
            }

            const newCalendar = await prisma.calendar.create({
                data: {
                    name: body.name,
                    description: body.description,
                    color: body.color,
                    clientId: body.clientId,
                    events: {
                        create: sourceCalendar.events.map(event => ({
                            title: event.title,
                            description: event.description,
                            startDate: event.startDate,
                            endDate: event.endDate,
                            allDay: event.allDay,
                            location: event.location
                        }))
                    },
                    holidays: {
                        create: sourceCalendar.holidays.map(holiday => ({
                            title: holiday.title,
                            description: holiday.description,
                            month: holiday.month,
                            day: holiday.day,
                            color: holiday.color,
                            allDay: holiday.allDay,
                            isRecurring: holiday.isRecurring
                        }))
                    }
                },
                include: {
                    client: {
                        select: {
                            id: true,
                            name: true
                        }
                    }
                }
            });

            return NextResponse.json(newCalendar, { status: 201 });
        } else {
            const newCalendar = await prisma.calendar.create({
                data: {
                    name: body.name,
                    description: body.description,
                    color: body.color,
                    clientId: body.clientId,
                },
                include: {
                    client: {
                        select: {
                            id: true,
                            name: true
                        }
                    }
                }
            });

            return NextResponse.json(newCalendar, { status: 201 });
        }
    } catch (error) {
        console.error("Erro ao criar calendário:", error);
        return NextResponse.json(
            { error: "Erro ao criar calendário" },
            { status: 500 }
        );
    }
}
