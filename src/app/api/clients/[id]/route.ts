import { createOrUpdateBirthdayEvent, createOrUpdateBrandAnniversaryEvent } from "@/app/lib/birthday-events";
import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
interface Content {
    activityDate: string;
    approved?: boolean;
    id?: string;
}

interface Activity {
    contents: Content[] | null;
    approved?: boolean;
}

interface MonthlyPlanning {
    activities: Activity[];
}

interface Owner {
    id?: string;
    name: string;
    cpf?: string;
    birthDate?: Date | string;
    rg?: string;
    issuingAgency?: string;
    maritalStatus?: string;
    nationality?: string;
    profession?: string;
    isPrimary?: boolean;
    clientId?: string;
    addressId?: string;
    address?: Address;
}

interface Address {
    id?: string;
    zipCode?: string;
    street?: string;
    number?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
    country?: string;
}

interface Owner {
    id?: string;
    name: string;
    cpf?: string;
    birthDate?: Date | string;
    rg?: string;
    issuingAgency?: string;
    maritalStatus?: string;
    nationality?: string;
    profession?: string;
    isPrimary?: boolean;
    clientId?: string;
    addressId?: string;
    address?: Address;
}

export async function GET(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const allowedRoles = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT', 'DESIGNER_SENIOR', 'CLIENT'];
        if (!allowedRoles.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const id = url.pathname.split('/').pop();
        const includeParam = url.searchParams.get('include') || '';
        const includeSegments = includeParam.split('.');

        if (!id) {
            return NextResponse.json({ message: "ID is required" }, { status: 400 });
        }

        const baseIncludeOptions = {
            monthlyPlannings: {
                include: {
                    activities: {
                        include: {
                            contents: {
                                include: {
                                    assignedTo: {
                                        select: {
                                            id: true,
                                            name: true,
                                            email: true,
                                            image: true
                                        }
                                    },
                                    steps: {
                                        include: {
                                            assignedTo: {
                                                select: {
                                                    id: true,
                                                    name: true,
                                                    email: true,
                                                    image: true
                                                }
                                            }
                                        }
                                    },
                                    reviewedBy: {
                                        select: {
                                            id: true,
                                            name: true,
                                            email: true,
                                            image: true
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        };

        const includeOptions = includeSegments.includes('resultsReport')
            ? { ...baseIncludeOptions, resultsReport: { include: { results: true } } }
            : baseIncludeOptions;


        const client = await prisma.$transaction(async (prisma) => {
            return await prisma.client.findUnique({
                where: { id },
                include: {
                    ...includeOptions,
                    storeAddress: true,
                    factoryAddress: true,
                    owners: {
                        include: {
                            address: true
                        }
                    }
                }
            });
        }, {
            isolationLevel: 'Serializable'
        });

        if (!client) {
            return NextResponse.json({ message: "Client not found" }, { status: 404 });
        }

        const processedClient = JSON.parse(JSON.stringify(client));

        processedClient.monthlyPlannings.forEach((planning: MonthlyPlanning) => {
            planning.activities.forEach((activity: Activity) => {
                if (activity.contents) {
                    activity.contents.forEach((content: Content & { clientId?: string }) => {
                        content.clientId = id;
                    });
                    activity.approved = activity.contents.some((c: Content) => (c as Content).approved === true);
                }
            });
        });

        processedClient.monthlyPlannings.forEach((planning: MonthlyPlanning) => {
            planning.activities.sort((activityA: Activity, activityB: Activity) => {
                const dateA: number = activityA.contents?.[0]?.activityDate ? new Date(activityA.contents[0].activityDate).getTime() : 0;
                const dateB: number = activityB.contents?.[0]?.activityDate ? new Date(activityB.contents[0].activityDate).getTime() : 0;
                return dateA - dateB;
            });

            planning.activities.forEach((activity: Activity) => {
                if (Array.isArray(activity.contents) && activity.contents.length > 1) {
                    activity.contents.sort((a: Content, b: Content) => {
                        const dateA: Date = new Date(a.activityDate);
                        const dateB: Date = new Date(b.activityDate);
                        return dateA.getTime() - dateB.getTime();
                    });
                }
            });
        });

        processedClient._timestamp = Date.now();

        return new NextResponse(JSON.stringify(processedClient), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-store, no-cache, must-revalidate, private',
                'Pragma': 'no-cache',
                'Expires': '0',
                'Surrogate-Control': 'no-store',
                'X-Accel-Expires': '0',
                'X-Request-ID': `${id}-${Date.now()}`
            }
        });
    } catch (error) {
        console.error("Error fetching client:", error instanceof Error ? error.message : String(error));
        return NextResponse.json({ message: "Error fetching client" }, { status: 500 });
    }
}

export async function PUT(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const allowedRoles = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];
        if (!allowedRoles.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const id = url.pathname.split('/').pop();

        if (!id) {
            return NextResponse.json({ message: "ID is required" }, { status: 400 });
        }

        const data = await req.json();

        if (data.action === 'archive' || data.action === 'unarchive') {
            const isArchiving = data.action === 'archive';

            const client = await prisma.client.update({
                where: { id: id },
                data: {
                    archived: isArchiving,
                    archivedAt: isArchiving ? new Date() : null
                }
            });

            if (isArchiving) {
                await prisma.generalDemand.updateMany({
                    where: { clientId: id },
                    data: { archived: true }
                });

                const monthlyPlannings = await prisma.monthlyPlanning.findMany({
                    where: { clientId: id },
                    include: {
                        activities: {
                            include: {
                                contents: true
                            }
                        }
                    }
                });

                for (const planning of monthlyPlannings) {
                    for (const activity of planning.activities) {
                        if (activity.contents.length > 0) {
                            await prisma.content.updateMany({
                                where: { weeklyActivityId: activity.id },
                                data: { archived: true }
                            });
                        }
                    }
                }
            }

            return NextResponse.json(client);
        }

        let brandAnniversaryDate = data.brandAnniversary;
        if (brandAnniversaryDate && typeof brandAnniversaryDate === 'string' && !brandAnniversaryDate.includes('T')) {
            brandAnniversaryDate = new Date(`${brandAnniversaryDate}T12:00:00Z`).toISOString();
        }

        const client = await prisma.client.update({
            where: { id: id },
            data: {
                name: data.name,
                phone: data.phone,
                instagramUsername: data.instagramUsername,
                companyName: data.companyName,
                tradingName: data.tradingName,
                monthlyPostsLimit: data.monthlyPostsLimit,
                monthlyStoriesLimit: data.monthlyStoriesLimit,
                additionalContent: data.additionalContent,
                cnpj: data.cnpj,
                brandAnniversary: brandAnniversaryDate,
                mainTypography: data.mainTypography,
                secondaryTypography: data.secondaryTypography,
                importantDetails: data.importantDetails,
                restrictions: data.restrictions,
                profileDescription: data.profileDescription,
                segment: data.segment,
                services: data.services,
                products: data.products,
                paymentMethod: data.paymentMethod,
                paymentDay: data.paymentDay ?
                    Math.min(Math.max(Number(data.paymentDay), 1), 31) : null,
                mission: data.mission,
                vision: data.vision,
                values: data.values
            }
        });

        if (brandAnniversaryDate) {
            await createOrUpdateBrandAnniversaryEvent(
                id,
                data.name || client.name,
                brandAnniversaryDate
            );
        }

        return NextResponse.json(client);
    } catch (error) {
        console.error("Error updating client:", error);
        return NextResponse.json({ message: "Error updating client" }, { status: 500 });
    }
}

export async function DELETE(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const allowedRoles = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];
        if (!allowedRoles.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const id = url.pathname.split('/').pop();

        if (!id) {
            return NextResponse.json({ message: "ID is required" }, { status: 400 });
        }

        await prisma.client.delete({
            where: { id: id }
        });

        return NextResponse.json({ message: "Client deleted" });
    } catch (error) {
        console.error("Error deleting client:", error);

        let errorMessage = "Error deleting client";
        if (error instanceof Error) {
            errorMessage = error.message;
        }

        return NextResponse.json({ message: errorMessage }, { status: 500 });
    }
}

export async function PATCH(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const allowedRoles = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];
        if (!allowedRoles.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const id = url.pathname.split('/').pop();

        if (!id) {
            return NextResponse.json({ message: "ID is required" }, { status: 400 });
        }

        const data = await req.json();

        const existingClient = await prisma.client.findUnique({
            where: { id }
        });

        if (!existingClient) {
            return NextResponse.json({ message: "Client not found" }, { status: 404 });
        }

        const { owners, ...cleanData } = { ...data };

        delete cleanData.monthlyPlannings;
        delete cleanData.resultsReport;
        delete cleanData.generalDemands;
        delete cleanData._timestamp;
        delete cleanData.id;
        delete cleanData.createdAt;
        delete cleanData.storeAddress;
        delete cleanData.factoryAddress;
        delete cleanData.storeAddressId;
        delete cleanData.factoryAddressId;

        if (cleanData.brandAnniversary && typeof cleanData.brandAnniversary === 'string') {
            if (!cleanData.brandAnniversary.includes('T')) {
                cleanData.brandAnniversary = new Date(`${cleanData.brandAnniversary}T12:00:00Z`).toISOString();
            }
        }

        const client = await prisma.$transaction(async (tx) => {
            await tx.client.update({
                where: { id },
                data: cleanData
            });

            if (cleanData.brandAnniversary) {
                await createOrUpdateBrandAnniversaryEvent(
                    id,
                    cleanData.name || existingClient.name,
                    cleanData.brandAnniversary
                );
            }

            const originalData = data;

            if (originalData.storeAddress) {
                if (existingClient.storeAddressId) {
                    await tx.address.update({
                        where: { id: existingClient.storeAddressId },
                        data: originalData.storeAddress
                    });
                } else {
                    const newAddress = await tx.address.create({
                        data: originalData.storeAddress
                    });
                    await tx.client.update({
                        where: { id },
                        data: { storeAddressId: newAddress.id }
                    });
                }
            }

            if (originalData.factoryAddress) {
                if (existingClient.factoryAddressId) {
                    await tx.address.update({
                        where: { id: existingClient.factoryAddressId },
                        data: originalData.factoryAddress
                    });
                } else {
                    const newAddress = await tx.address.create({
                        data: originalData.factoryAddress
                    });
                    await tx.client.update({
                        where: { id },
                        data: { factoryAddressId: newAddress.id }
                    });
                }
            }

            if (owners && owners.length > 0) {
                const ownerIds = owners
                    .filter((owner: Owner) => owner.id)
                    .map((owner: Owner) => owner.id);

                if (ownerIds.length > 0) {
                    await tx.owner.deleteMany({
                        where: {
                            clientId: id,
                            id: { notIn: ownerIds }
                        }
                    });
                } else {
                    await tx.owner.deleteMany({
                        where: { clientId: id }
                    });
                }

                for (const ownerData of owners) {
                    const { address, ...ownerFields } = ownerData;

                    const processedOwnerFields = { ...ownerFields };

                    delete processedOwnerFields.id;
                    delete processedOwnerFields.clientId;
                    delete processedOwnerFields.addressId;
                    delete processedOwnerFields.createdAt;
                    delete processedOwnerFields.updatedAt;

                    if (processedOwnerFields.birthDate) {
                        try {
                            if (typeof processedOwnerFields.birthDate === 'string' && !processedOwnerFields.birthDate.includes('T')) {
                                processedOwnerFields.birthDate = new Date(`${processedOwnerFields.birthDate}T12:00:00Z`).toISOString();
                            } else {
                                processedOwnerFields.birthDate = new Date(processedOwnerFields.birthDate.toString()).toISOString();
                            }
                        } catch {
                            delete processedOwnerFields.birthDate;
                        }
                    }

                    if (ownerData.id) {
                        const existingOwner = await tx.owner.findUnique({
                            where: { id: ownerData.id },
                            include: { address: true }
                        });

                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        const { id: ownerId, clientId, addressId, createdAt, updatedAt, ...cleanOwnerFields } = processedOwnerFields;

                        await tx.owner.update({
                            where: { id: ownerData.id },
                            data: {
                                ...cleanOwnerFields,
                                ...(address && {
                                    address: existingOwner?.addressId
                                        ? {
                                            update: {
                                                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                                                ...(({ id, createdAt, updatedAt, ...addressUpdate }) => addressUpdate)(address)
                                            }
                                        }
                                        : {
                                            create: {
                                                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                                                ...(({ id, createdAt, updatedAt, ...addressCreate }) => addressCreate)(address)
                                            }
                                        }
                                })
                            }
                        });

                        if (processedOwnerFields.birthDate) {
                            await createOrUpdateBirthdayEvent(
                                id,
                                cleanOwnerFields.name,
                                processedOwnerFields.birthDate
                            );
                        }
                    } else {
                        // eslint-disable-next-line @typescript-eslint/no-unused-vars
                        const { id: ownerId, clientId, addressId, createdAt, updatedAt, ...cleanOwnerFields } = processedOwnerFields;

                        await tx.owner.create({
                            data: {
                                ...cleanOwnerFields,
                                client: { connect: { id } },
                                ...(address && {
                                    address: {
                                        create: {
                                            // eslint-disable-next-line @typescript-eslint/no-unused-vars
                                            ...(({ id, createdAt, updatedAt, ...addressCreate }) => addressCreate)(address)
                                        }
                                    }
                                })
                            }
                        });

                        if (processedOwnerFields.birthDate) {
                            await createOrUpdateBirthdayEvent(
                                id,
                                cleanOwnerFields.name,
                                processedOwnerFields.birthDate
                            );
                        }
                    }
                }
            }

            return await tx.client.findUnique({
                where: { id },
                include: {
                    monthlyPlannings: {
                        include: {
                            activities: {
                                include: {
                                    contents: {
                                        include: {
                                            assignedTo: {
                                                select: {
                                                    id: true,
                                                    name: true,
                                                    email: true,
                                                    image: true
                                                }
                                            },
                                            steps: {
                                                include: {
                                                    assignedTo: {
                                                        select: {
                                                            id: true,
                                                            name: true,
                                                            email: true,
                                                            image: true
                                                        }
                                                    }
                                                }
                                            }
                                            ,
                                            reviewedBy: {
                                                select: {
                                                    id: true,
                                                    name: true,
                                                    email: true,
                                                    image: true
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    resultsReport: {
                        include: {
                            results: true
                        }
                    },
                    storeAddress: true,
                    factoryAddress: true,
                    owners: {
                        include: {
                            address: true
                        }
                    }
                }
            });
        });

        return NextResponse.json(client);
    } catch (error) {
        console.error("Error updating client:", error);

        let errorMessage = "Error updating client";
        if (error instanceof Error) {
            errorMessage = error.message;
        }

        return NextResponse.json({ message: errorMessage }, { status: 500 });
    }
}
