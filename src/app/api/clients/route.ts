import { createOrUpdateBrandAnniversaryEvent } from "@/app/lib/birthday-events";
import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

interface Owner {
    id?: string;
    name: string;
    cpf?: string;
    birthDate?: Date | string;
    rg?: string;
    issuingAgency?: string;
    maritalStatus?: string;
    nationality?: string;
    profession?: string;
    isPrimary?: boolean;
    clientId?: string;
    addressId?: string;
    address?: Address;
    createdAt?: string;
    updatedAt?: string;
}

interface Address {
    id?: string;
    zipCode?: string;
    street?: string;
    number?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
    country?: string;
    createdAt?: string;
    updatedAt?: string;
}

export async function GET(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const disallowedRoles = ['VIEWER', 'CLIENT'];
        if (disallowedRoles.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const showArchived = url.searchParams.get('archived') === 'true';
        const includeParam = url.searchParams.get('include') || '';
        const monthParam = url.searchParams.get('month');
        const yearParam = url.searchParams.get('year');

        const monthlyPlanningsFilter = {
            select: {
                id: true,
                month: true,
                year: true,
                status: true,
                createdAt: true,
            },
            orderBy: {
                createdAt: 'desc' as const
            },
            ...(monthParam && yearParam && {
                where: {
                    createdAt: {
                        gte: new Date(parseInt(yearParam), parseInt(monthParam) - 1, 1),
                        lt: new Date(parseInt(yearParam), parseInt(monthParam), 1)
                    }
                }
            })
        };

        const resultsReportFilter = includeParam.includes('resultsReport') ? {
            select: {
                id: true,
                month: true,
                year: true,
                createdAt: true,
            },
            ...(monthParam && yearParam && {
                where: {
                    createdAt: {
                        gte: new Date(parseInt(yearParam), parseInt(monthParam) - 1, 1),
                        lt: new Date(parseInt(yearParam), parseInt(monthParam), 1)
                    }
                }
            })
        } : undefined;

        const clientQuery = {
            where: {
                archived: showArchived
            },
            select: {
                id: true,
                name: true,
                phone: true,
                instagramUsername: true,
                monthlyPostsLimit: true,
                monthlyStoriesLimit: true,
                additionalContent: true,
                createdAt: true,
                archived: true,
                archivedAt: true,
                monthlyPlannings: monthlyPlanningsFilter,
                ...(resultsReportFilter && { resultsReport: resultsReportFilter })
            },
            orderBy: {
                createdAt: 'desc' as const,
            },
        };

        const clients = await prisma.client.findMany(clientQuery);
        return NextResponse.json(clients);
    } catch (error) {
        console.error("Error fetching clients:", error);
        return NextResponse.json({ message: "Error fetching clients" }, { status: 500 });
    }
}

export async function POST(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const allowedRoles = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];
        if (!allowedRoles.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const body = await req.json();

        if (!body.name || typeof body.name !== 'string') {
            throw new Error("Nome inválido");
        }

        const { storeAddress, factoryAddress, owners, ...clientData } = body;

        if (clientData.brandAnniversary && typeof clientData.brandAnniversary === 'string') {
            if (!clientData.brandAnniversary.includes('T')) {
                clientData.brandAnniversary = new Date(`${clientData.brandAnniversary}T12:00:00Z`).toISOString();
            }
        }

        const newClient = await prisma.client.create({
            data: {
                ...clientData,
                monthlyPostsLimit: Number(clientData.monthlyPostsLimit),
                monthlyStoriesLimit: Number(clientData.monthlyStoriesLimit),
                additionalContent: Number(clientData.additionalContent),
                ...(storeAddress && {
                    storeAddress: storeAddress.create 
                        ? storeAddress
                        : { create: storeAddress }
                }),
                ...(factoryAddress && {
                    factoryAddress: factoryAddress.create 
                        ? factoryAddress 
                        : { create: factoryAddress }
                }),
                ...(owners && owners.length > 0 && {
                    owners: {
                        create: owners.map((owner: Owner) => {
                            const { address, ...ownerFields } = owner;
                            const ownerData = { ...ownerFields };

                            if ('id' in ownerData) delete ownerData.id;
                            if ('clientId' in ownerData) delete ownerData.clientId;
                            if ('addressId' in ownerData) delete ownerData.addressId;
                            if ('createdAt' in ownerData) delete ownerData.createdAt;
                            if ('updatedAt' in ownerData) delete ownerData.updatedAt;

                            if (ownerData.birthDate) {
                                try {
                                    if (typeof ownerData.birthDate === 'string' && !ownerData.birthDate.includes('T')) {
                                        ownerData.birthDate = new Date(`${ownerData.birthDate}T12:00:00Z`).toISOString();
                                    } else {
                                        ownerData.birthDate = new Date(ownerData.birthDate.toString()).toISOString();
                                    }
                                } catch {
                                    delete ownerData.birthDate;
                                }
                            }

                            const processedAddress = address ? { ...address } : undefined;
                            if (processedAddress) {
                                if ('id' in processedAddress) delete processedAddress.id;
                                if ('createdAt' in processedAddress) delete processedAddress.createdAt;
                                if ('updatedAt' in processedAddress) delete processedAddress.updatedAt;
                            }
                            
                            return {
                                ...ownerData,
                                ...(processedAddress && {
                                    address: {
                                        create: processedAddress
                                    }
                                })
                            };
                        })
                    }
                })
            },
            include: {
                storeAddress: true,
                factoryAddress: true,
                owners: {
                    include: {
                        address: true
                    }
                }
            }
        });

        if (newClient.brandAnniversary) {
            await createOrUpdateBrandAnniversaryEvent(
                newClient.id,
                newClient.name,
                newClient.brandAnniversary
            );
        }

        return NextResponse.json(newClient, { status: 201 });
    } catch (error: unknown) {
        console.error("🚀 ~ POST ~ error:", error);
        const errorMessage = error instanceof Error ? error.message : "Error creating client";
        return NextResponse.json({ message: errorMessage }, { status: 500 });
    }
}
