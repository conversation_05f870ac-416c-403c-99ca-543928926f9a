import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { StepType } from "@prisma/client";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export async function POST(req: Request) {
    try {
        const { contentId, steps } = await req.json();

        if (!contentId) {
            return NextResponse.json({ message: "ID do conteúdo é obrigatório" }, { status: 400 });
        }

        if (!steps || !Array.isArray(steps)) {
            return NextResponse.json({ message: "Etapas são obrigatórias" }, { status: 400 });
        }

        const content = await prisma.content.findUnique({
            where: { id: contentId }
        });

        if (!content) {
            return NextResponse.json({ message: "Conteúdo não encontrado" }, { status: 404 });
        }

        await prisma.contentStep.deleteMany({
            where: { contentId }
        });

        const createdSteps = await Promise.all(
            steps.map(async (step) => {
                const { type, assignedToId } = step;

                if (!type || !assignedToId) {
                    throw new Error("Tipo e usuário responsável são obrigatórios para cada etapa");
                }

                return prisma.contentStep.create({
                    data: {
                        contentId,
                        type: type as StepType,
                        assignedToId
                    },
                    include: {
                        assignedTo: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                                image: true
                            }
                        }
                    }
                });
            })
        );

        const session = await getServerSession(authOptions);
        const assignerName = session?.user?.name || "Um usuário";

        for (const step of createdSteps) {
            if (step.assignedTo) {
                await prisma.notification.create({
                    data: {
                        content: `${assignerName} atribuiu você à etapa ${step.type} de um conteúdo`,
                        type: "assigned_step",
                        entityId: contentId,
                        entityType: "content_step",
                        userId: step.assignedTo.email
                    }
                });
            }
        }

        return NextResponse.json(createdSteps);
    } catch (error) {
        console.error("Erro ao criar etapas do conteúdo:", error);
        return NextResponse.json(
            { message: "Erro interno do servidor", error: error instanceof Error ? error.message : String(error) },
            { status: 500 }
        );
    }
}

export async function GET(req: Request) {
    try {
        const url = new URL(req.url);
        const contentId = url.searchParams.get("contentId");

        if (!contentId) {
            return NextResponse.json({ message: "ID do conteúdo é obrigatório" }, { status: 400 });
        }

        const steps = await prisma.contentStep.findMany({
            where: { contentId },
            include: {
                assignedTo: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        image: true
                    }
                }
            }
        });

        return NextResponse.json(steps);
    } catch (error) {
        console.error("Erro ao buscar etapas do conteúdo:", error);
        return NextResponse.json({ message: "Erro interno do servidor" }, { status: 500 });
    }
}