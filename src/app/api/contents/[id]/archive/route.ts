import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

export async function POST(request: Request) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email },
        });

        if (!user || (user.role !== "ADMIN" && user.role !== "DEVELOPER")) {
            return NextResponse.json({ error: "Permissão negada" }, { status: 403 });
        }

        const url = new URL(request.url);
        const id = url.pathname.split('/').slice(-2)[0];
        const body = await request.json();
        const { archived } = body;

        const content = await prisma.content.update({
            where: { id },
            data: {
                archived,
            },
        });

        return NextResponse.json(content);
    } catch (error) {
        console.error("Erro ao arquivar conteúdo:", error);
        return NextResponse.json(
            { error: "Erro ao processar a requisição" },
            { status: 500 }
        );
    }
}