import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export async function PATCH(req: Request) {
  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const contentId = pathParts[pathParts.length - 2];
    const { userId } = await req.json();

    if (!contentId) {
      return NextResponse.json({ message: "Content ID is required" }, { status: 400 });
    }

    const content = await prisma.content.findUnique({
      where: { id: contentId },
      include: {
        weeklyActivity: {
          include: {
            monthlyPlanning: {
              include: {
                client: true
              }
            }
          }
        }
      }
    });

    if (!content) {
      return NextResponse.json({ message: "Content not found" }, { status: 404 });
    }

    const session = await getServerSession(authOptions);
    const assignerName = session?.user?.name || "Um usuário";

    let assignedUser = null;
    if (userId) {
      assignedUser = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          image: true
        }
      });
    }

    const updatedContent = await prisma.content.update({
      where: { id: contentId },
      data: { 
        assignedToId: userId || null,
        ...(userId ? { status: "repassado" } : {})
      },
      include: {
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      }
    });

    if (userId && assignedUser) {
      const planningStatus = content.weeklyActivity.monthlyPlanning.status;
      const clientName = content.weeklyActivity.monthlyPlanning.client.name;
      const contentDate = new Date(content.activityDate).toLocaleDateString('pt-BR');
      const contentType = content.contentType;

      if (planningStatus === "aprovado") {
        await prisma.notification.create({
          data: {
            content: `${assignerName} atribuiu uma demanda de ${clientName} (${contentType}) para ${contentDate} `,
            type: "assigned_demand",
            entityId: contentId,
            entityType: "content",
            userId: assignedUser.email
          }
        });
      }
    }

    return NextResponse.json(updatedContent);
  } catch (error) {
    console.error("Error updating content assignment:", error instanceof Error ? error.message : String(error));
    return NextResponse.json({ message: "Error updating content assignment" }, { status: 500 });
  }
}
