import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import prisma from '@/app/lib/prisma';
import { authOptions } from '@/lib/auth';

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/');
    const contentId = pathSegments[pathSegments.length - 2];

    if (!contentId) {
      return NextResponse.json({ error: 'ID do conteúdo é obrigatório' }, { status: 400 });
    }

    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const comments = await prisma.imageComment.findMany({
      where: {
        contentId: contentId,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return NextResponse.json(comments);
  } catch (error) {
    console.error('Erro ao buscar comentários:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const contentId = pathSegments[pathSegments.length - 2];

    if (!contentId) {
      return NextResponse.json({ error: 'ID do conteúdo é obrigatório' }, { status: 400 });
    }

    const body = await request.json();
    const { url: imageUrl, x, y, text } = body as { url?: string; x?: number; y?: number; text?: string };

    if (typeof x !== 'number' || x < 0 || x > 100) {
      return NextResponse.json(
        { error: 'Posição X deve ser um número entre 0 e 100' },
        { status: 400 }
      );
    }

    if (typeof y !== 'number' || y < 0 || y > 100) {
      return NextResponse.json(
        { error: 'Posição Y deve ser um número entre 0 e 100' },
        { status: 400 }
      );
    }

    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      return NextResponse.json(
        { error: 'Texto do comentário é obrigatório' },
        { status: 400 }
      );
    }

    if (!imageUrl || typeof imageUrl !== 'string' || imageUrl.trim().length === 0) {
      return NextResponse.json(
        { error: 'URL da imagem é obrigatória' },
        { status: 400 }
      );
    }

    const content = await prisma.content.findUnique({
      where: { id: contentId },
    });

    if (!content) {
      return NextResponse.json(
        { error: 'Conteúdo não encontrado' },
        { status: 404 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não encontrado' },
        { status: 404 }
      );
    }

    const comment = await prisma.imageComment.create({
      data: {
        contentId,
        url: imageUrl!.trim(),
        x,
        y,
        text: text.trim(),
        authorId: user.id,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json(comment, { status: 201 });
  } catch (error) {
    console.error('Erro ao criar comentário:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
