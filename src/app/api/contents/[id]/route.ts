import prisma from "@/app/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { StepType, Prisma } from "@prisma/client";
import { NextResponse } from "next/server";

interface ContentStep {
    type: string;
    assignedToId: string;
}

export async function GET(request: Request) {
    try {
        const url = new URL(request.url);
        const id = url.pathname.split('/').pop();

        if (!id) {
            return NextResponse.json({ message: "ID não fornecido" }, { status: 400 });
        }

        const content = await prisma.content.findUnique({
            where: { id },
            include: {
                assignedTo: true
            }
        });

        if (!content) {
            return NextResponse.json({ message: "Conteúdo não encontrado" }, { status: 404 });
        }

        return new NextResponse(JSON.stringify(content), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
    } catch (error) {
        return NextResponse.json(
            { message: "Erro interno do servidor", error },
            { status: 500 }
        );
    }
}

export async function PUT(request: Request) {
    try {
        const url = new URL(request.url);
        const id = url.pathname.split('/').pop();

        const data = await request.json();

        const {
            activityDate,
            contentType,
            channel,
            destination,
            details,
            copywriting,
            caption,
            reference,
            priority,
            position,
            status,
            weeklyActivityId,
            urlStructuringFeed,
            assignedToId,
            carouselImagesCount,
            review,
            approved,
            steps
        } = data;

        const existingContent = await prisma.content.findUnique({
            where: { id }
        });

        if (!existingContent) {
            return NextResponse.json(
                { message: "Conteúdo não encontrado" },
                { status: 404 }
            );
        }

        const updateData: Prisma.ContentUncheckedUpdateInput = {};


        if (data.hasOwnProperty('activityDate')) {
            updateData.activityDate = new Date(activityDate);
        }
        if (data.hasOwnProperty('contentType')) {
            updateData.contentType = contentType;
        }
        if (data.hasOwnProperty('channel')) {
            updateData.channel = channel;
        }
        if (data.hasOwnProperty('destination')) {
            updateData.destination = destination;
        }
        if (data.hasOwnProperty('details')) {
            updateData.details = details;
        }

        if (data.hasOwnProperty('copywriting')) {
            updateData.copywriting = copywriting;
        }

        if (data.hasOwnProperty('caption')) {
            updateData.caption = caption;
        }

        if (data.hasOwnProperty('reference')) {
            updateData.reference = reference;
        }

        if (data.hasOwnProperty('priority')) {
            updateData.priority = priority;
        }

        if (data.hasOwnProperty('position')) {
            updateData.position = position;
        }

        if (data.hasOwnProperty('status')) {
            updateData.status = status;
        }

        if (data.hasOwnProperty('weeklyActivityId')) {
            updateData.weeklyActivityId = weeklyActivityId;
        }

        if (data.hasOwnProperty('urlStructuringFeed')) {
            // Normalize input: string | string[] | { url: string, review?: string }[]
            const urls: string[] = [];
            const reviews: string[] = [];

            if (typeof urlStructuringFeed === 'string') {
                if (urlStructuringFeed.trim()) {
                    urls.push(urlStructuringFeed.trim());
                    reviews.push('');
                }
            } else if (Array.isArray(urlStructuringFeed)) {
                for (const item of urlStructuringFeed) {
                    if (typeof item === 'string') {
                        const u = item.trim();
                        if (u) {
                            urls.push(u);
                            reviews.push('');
                        }
                    } else if (item && typeof item === 'object' && typeof item.url === 'string') {
                        const u = String(item.url).trim();
                        if (u) {
                            urls.push(u);
                            reviews.push(item.review ? String(item.review) : '');
                        }
                    } else {
                        // ignore invalid entries
                    }
                }
            }

            updateData.urlStructuringFeed = urls;
            // attach reviews array using the Prisma unchecked update input shape
            (updateData as Prisma.ContentUncheckedUpdateInput).urlStructuringFeedReviews = reviews;
        }

        if (data.hasOwnProperty('carouselImagesCount')) {
            updateData.carouselImagesCount = carouselImagesCount;
        }

        if (data.hasOwnProperty('assignedToId')) {
            if (assignedToId === null) {
                updateData.assignedToId = null;
            } else if (typeof assignedToId !== 'undefined') {
                updateData.assignedToId = assignedToId;
            }

            if (assignedToId && !data.hasOwnProperty('status')) {
                updateData.status = "repassado";
            }
        }

        if (data.hasOwnProperty('review')) {
            updateData.review = review;

            try {
                const session = await getServerSession(authOptions);
                if (session?.user?.id) {
                    (updateData as unknown as Record<string, unknown>).reviewedById = String(session.user.id);
                }
            } catch (e) {
                console.debug('Could not get server session to set reviewedById', e);
            }
        }

        if (data.hasOwnProperty('approved')) {
            updateData.approved = Boolean(approved);
        }

        try {
            const updatedContent = await prisma.content.update({
                where: { id },
                data: updateData,
                include: {
                    assignedTo: true,
                    reviewedBy: { select: { id: true, name: true, email: true } },
                    weeklyActivity: {
                        include: {
                            monthlyPlanning: {
                                include: {
                                    client: true
                                }
                            }
                        }
                    },
                    steps: {
                        include: {
                            assignedTo: true
                        }
                    }
                }
            });
            
            if (data.hasOwnProperty('review') &&
                review &&
                review.trim() !== '' &&
                updatedContent.assignedToId) {

                const contentWithRelations = await prisma.content.findUnique({
                    where: { id: updatedContent.id },
                    include: {
                        assignedTo: true,
                        weeklyActivity: {
                            include: {
                                monthlyPlanning: {
                                    include: {
                                        client: true
                                    }
                                }
                            }
                        }
                    }
                });

                if (contentWithRelations?.assignedTo?.email) {
                    const clientName = contentWithRelations.weeklyActivity?.monthlyPlanning?.client?.name || 'Cliente não especificado';

                    await prisma.notification.create({
                        data: {
                            content: `Alteração solicitada no conteúdo: ${updatedContent.details || 'Sem descrição'} (${clientName})`,
                            type: "review_content",
                            entityId: updatedContent.id,
                            entityType: "content",
                            userId: contentWithRelations.assignedTo.email,
                            importance: "high",
                        },
                    });
                }
            }

            if (steps && Array.isArray(steps)) {
                await prisma.contentStep.deleteMany({
                    where: { contentId: id }
                });

                if (steps.length > 0) {
                    await Promise.all(
                        steps.map((step: ContentStep) => {
                            if (id) {
                                return prisma.contentStep.create({
                                    data: {
                                        contentId: id,
                                        type: step.type as StepType,
                                        assignedToId: step.assignedToId
                                    }
                                });
                            }
                            return Promise.resolve();
                        })
                    );
                }
            }

            return new NextResponse(JSON.stringify(updatedContent), {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });
        } catch (dbError) {
            return NextResponse.json(
                { message: "Erro ao atualizar o conteúdo no banco de dados", error: dbError instanceof Error ? dbError.message : String(dbError) },
                { status: 500 }
            );
        }
    } catch (error) {
        return NextResponse.json(
            { message: "Erro interno do servidor", error },
            { status: 500 }
        );
    }
}

export async function DELETE(request: Request) {
    try {
        const url = new URL(request.url);
        const id = url.pathname.split('/').pop();

        const content = await prisma.content.findUnique({
            where: { id },
        });

        if (!content) {
            return NextResponse.json(
                { message: "Conteúdo não encontrado" },
                { status: 404 }
            );
        }

        await prisma.content.delete({
            where: { id },
        });

        return NextResponse.json({ success: true });
    } catch (error) {
        return NextResponse.json(
            { message: "Erro interno do servidor", error },
            { status: 500 }
        );
    }
}

export async function PATCH(request: Request) {
    try {
        const url = new URL(request.url);
        const id = url.pathname.split('/').pop();
        const body = await request.json();

        if (body && body.hasOwnProperty('approved')) {
            const { approved } = body;
            try {
                const updatedContent = await prisma.content.update({
                    where: { id },
                    data: { approved: Boolean(approved) },
                    include: {
                        assignedTo: true,
                        weeklyActivity: {
                            include: {
                                monthlyPlanning: {
                                    include: {
                                        client: true
                                    }
                                }
                            }
                        }
                    }
                });

                return NextResponse.json(updatedContent);
            } catch (prismaError) {
                console.error("Erro ao atualizar approved do conteúdo:", prismaError);
                return NextResponse.json(
                    { message: "Conteúdo não encontrado" },
                    { status: 404 }
                );
            }
        }

        if ('status' in body && Object.keys(body).length === 1) {
            const { status } = body;

            const validStatuses = [
                "pendente",
                "em andamento",
                "estruturação de feed",
                "feed estruturado",
                "repassado",
                "em revisão",
                "alteração",
                "pend. captação",
                "captado",
                "aprovado",
                "anúncio concluído",
                "concluído"
            ];

            if (!validStatuses.includes(status)) {
                return NextResponse.json(
                    {
                        message: "Status inválido",
                        validOptions: validStatuses
                    },
                    { status: 400 }
                );
            }

            try {
                const updatedContent = await prisma.content.update({
                    where: { id },
                    data: { status },
                    include: {
                        assignedTo: true,
                        weeklyActivity: {
                            include: {
                                monthlyPlanning: {
                                    include: {
                                        client: true
                                    }
                                }
                            }
                        }
                    }
                });

                return NextResponse.json(updatedContent);
            } catch (prismaError) {
                console.error("Erro ao atualizar status do conteúdo:", prismaError);
                return NextResponse.json(
                    { message: "Conteúdo não encontrado" },
                    { status: 404 }
                );
            }
        } else {
            return NextResponse.json({ message: "Operação não suportada" }, { status: 400 });
        }
    } catch (error) {
        return NextResponse.json(
            { message: "Erro interno do servidor", error },
            { status: 500 }
        );
    }
}