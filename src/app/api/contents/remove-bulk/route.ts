import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import prisma from "@/app/lib/prisma";
import { authOptions } from "@/lib/auth";

export async function DELETE(req: Request) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email || "" },
            select: { role: true },
        });

        if (user?.role !== "ADMIN" && user?.role !== "DEVELOPER") {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const { contentIds } = await req.json();

        if (!contentIds || !Array.isArray(contentIds) || contentIds.length === 0) {
            return NextResponse.json(
                { error: "IDs das demandas são obrigatórios" },
                { status: 400 }
            );
        }

        const existingContents = await prisma.content.findMany({
            where: { id: { in: contentIds } },
            select: {
                id: true,
                weeklyActivityId: true
            }
        });

        if (existingContents.length !== contentIds.length) {
            return NextResponse.json(
                { error: "Algumas demandas não foram encontradas ou não são demandas de conteúdo" },
                { status: 400 }
            );
        }

        const result = await prisma.content.deleteMany({
            where: { id: { in: contentIds } }
        });

        return NextResponse.json({
            message: "Demandas removidas com sucesso",
            count: result.count
        });
    } catch (error) {
        console.error("Erro ao remover demandas de conteúdo:", error);
        return NextResponse.json(
            { error: "Erro ao processar a solicitação" },
            { status: 500 }
        );
    }
}
