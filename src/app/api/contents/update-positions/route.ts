import { NextRequest, NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

interface UpdateItem {
  id: string;
  position: number;
  priority: string;
}

export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
    }

    const { updates, userId } = await request.json();

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json(
        { error: "Dados inválidos" },
        { status: 400 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email || "" },
      select: { id: true, role: true },
    });

    const isAdminOrDev = user?.role === "ADMIN" || user?.role === "DEVELOPER";

    if (!isAdminOrDev && user?.id !== userId) {
      return NextResponse.json(
        { error: "Não autorizado a modificar demandas de outros usuários" },
        { status: 403 }
      );
    }

    const contentIds = updates.map(update => update.id);

    if (!isAdminOrDev) {
      const existingContents = await prisma.content.findMany({
        where: {
          id: { in: contentIds },
          assignedToId: userId
        }
      });

      if (existingContents.length !== contentIds.length) {
        return NextResponse.json(
          { error: "Algumas demandas não pertencem ao colaborador especificado" },
          { status: 400 }
        );
      }
    }

    const updatePromises = updates.map((item: UpdateItem) => {
      const whereCondition = isAdminOrDev
        ? { id: item.id }
        : { id: item.id, assignedToId: userId };

      return prisma.content.update({
        where: whereCondition,
        data: {
          position: item.position,
          priority: item.priority,
        },
      });
    });

    await Promise.all(updatePromises);

    return NextResponse.json({ success: true });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error("Erro ao atualizar posições:", errorMessage);
    return NextResponse.json(
      { error: `Erro ao processar a requisição: ${errorMessage}` },
      { status: 500 }
    );
  }
}
