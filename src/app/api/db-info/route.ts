import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";

export async function GET() {
    try {
        const databaseUrl = process.env.DATABASE_URL || "";
        const isLocal = databaseUrl.includes("localhost") || databaseUrl.includes("127.0.0.1");
        const host = databaseUrl.match(/[@]([^:]+):/)?.[1] || "desconhecido";
        const dbName = databaseUrl.match(/\/([^?]+)/)?.[1] || "desconhecido";
        
        let isConnected = false;
        try {
            await prisma.$queryRaw`SELECT 1`;
            isConnected = true;
        } catch (dbError) {
            console.error("Erro na conexão com o banco:", dbError);
            isConnected = false;
        }

        return NextResponse.json({
            environment: isLocal ? "local" : "produção",
            host,
            database: dbName,
            isConnected,
        });
    } catch (error) {
        console.error("Erro ao obter informações do banco:", error);
        return NextResponse.json(
            { error: "Falha ao obter informações do banco de dados", isConnected: false },
            { status: 500 }
        );
    }
}