import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/app/lib/prisma';

export async function GET(request: NextRequest) {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
        return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
    if (!requestingUser) {
        return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
    }

    const disallowedRoles = ['VIEWER', 'CLIENT'];
    if (disallowedRoles.includes(requestingUser.role)) {
        return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    const url = new URL(request.url);
    const testUrl = url.searchParams.get('url');

    if (!testUrl) {
        return NextResponse.json({ error: 'URL não fornecida' }, { status: 400 });
    }

    // Função para extrair ID do Google Drive
    const extractGoogleDriveId = (url: string): string | null => {
        if (!url) return null;

        console.log('🔍 URL original:', url);

        const patterns = [
            /\/d\/([^/]+)/,              // https://drive.google.com/file/d/ID/view
            /id=([^&]+)/,                // ?id=ID
            /open\?id=([^&]+)/,          // https://drive.google.com/open?id=ID
            /thumbnail\?id=([^&]+)/,     // thumbnail direto
        ];

        for (let i = 0; i < patterns.length; i++) {
            const match = url.match(patterns[i]);
            if (match) {
                console.log(`✅ ID extraído via padrão ${i + 1}:`, match[1]);
                return match[1];
            }
        }

        console.log('❌ Não foi possível extrair ID da URL');
        return null;
    };

    const driveId = extractGoogleDriveId(testUrl);

    if (!driveId) {
        return NextResponse.json({
            success: false,
            error: 'Não foi possível extrair ID do Google Drive da URL',
            originalUrl: testUrl
        });
    }

    // Testar diferentes estratégias
    const strategies = [
        {
            name: 'Thumbnail w800',
            url: `https://drive.google.com/thumbnail?id=${driveId}&sz=w800`
        },
        {
            name: 'Thumbnail w400',
            url: `https://drive.google.com/thumbnail?id=${driveId}&sz=w400`
        },
        {
            name: 'Export View',
            url: `https://drive.google.com/uc?export=view&id=${driveId}`
        },
        {
            name: 'Direct File',
            url: `https://drive.google.com/file/d/${driveId}/view`
        },
        {
            name: 'Download Direct',
            url: `https://drive.google.com/uc?export=download&id=${driveId}`
        }
    ];

    const results = [];

    for (const strategy of strategies) {
        try {
            console.log(`🧪 Testando estratégia: ${strategy.name}`);
            const response = await fetch(strategy.url, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                    'Referer': 'https://drive.google.com/',
                },
                method: 'HEAD' // Só para verificar headers, sem baixar o conteúdo
            });

            results.push({
                strategy: strategy.name,
                url: strategy.url,
                status: response.status,
                statusText: response.statusText,
                contentType: response.headers.get('Content-Type'),
                contentLength: response.headers.get('Content-Length'),
                success: response.ok
            });

        } catch (error) {
            results.push({
                strategy: strategy.name,
                url: strategy.url,
                success: false,
                error: error instanceof Error ? error.message : 'Erro desconhecido'
            });
        }
    }

    return NextResponse.json({
        originalUrl: testUrl,
        extractedId: driveId,
        proxyUrl: `/api/drive-proxy?id=${driveId}`,
        strategies: results,
        recommendations: results
            .filter(r => r.success)
            .map(r => `${r.strategy}: ${r.contentType} (${r.contentLength} bytes)`)
    });
}
