import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/app/lib/prisma';

const completedStatuses = ['concluído', 'captado', 'pend. captação', 'em revisão', 'an<PERSON><PERSON> concluído', 'feed estruturado'];

export async function GET(request: Request) {
    try {
        const session = await getServerSession(authOptions);

        if (!session || !session.user) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const url = new URL(request.url);
        const monthParam = url.searchParams.get('month');
        const yearParam = url.searchParams.get('year');

        const month = monthParam ? parseInt(monthParam, 10) : null;
        const year = yearParam ? parseInt(yearParam, 10) : null;

        let contentDateFilter = {};
        let generalDemandDateFilter = {};

        if (month && year) {
            const startDate = new Date(year, month - 1, 1);
            const endDate = new Date(year, month, 0);

            contentDateFilter = {
                activityDate: {
                    gte: startDate,
                    lte: endDate
                }
            };

            generalDemandDateFilter = {
                dueDate: {
                    gte: startDate,
                    lte: endDate
                }
            };
        }

        const pendingContentDemands = await prisma.content.count({
            where: {
                ...contentDateFilter,
                status: {
                    notIn: completedStatuses
                },
                archived: false
            }
        });

        const pendingGeneralDemands = await prisma.generalDemand.count({
            where: {
                ...generalDemandDateFilter,
                status: {
                    notIn: completedStatuses
                },
                archived: false
            }
        });

        const totalPendingDemands = pendingContentDemands + pendingGeneralDemands;

        return NextResponse.json({
            count: totalPendingDemands,
            contentDemands: pendingContentDemands,
            generalDemands: pendingGeneralDemands
        });
    } catch (error) {
        console.error('Erro ao buscar contagem de demandas pendentes:', error);
        return NextResponse.json(
            { error: 'Erro ao buscar contagem de demandas pendentes' },
            { status: 500 }
        );
    }
}
