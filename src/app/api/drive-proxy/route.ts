import { NextRequest, NextResponse } from 'next/server';
// path import removed — not used

const imageCache = new Map<string, { buffer: ArrayBuffer, contentType: string, timestamp: number }>();
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000;

setInterval(() => {
    const now = Date.now();
    for (const [key, value] of imageCache.entries()) {
        if (now - value.timestamp > CACHE_EXPIRATION) {
            imageCache.delete(key);
        }
    }
}, 60 * 60 * 1000);

export async function GET(request: NextRequest) {
    const url = new URL(request.url);
    const fileId = url.searchParams.get('id');
    const noCache = url.searchParams.get('no-cache') === 'true';

    console.log('📡 Drive-proxy solicitação recebida:', {
        fileId,
        noCache,
        userAgent: request.headers.get('user-agent')
    });

    if (!fileId) {
        console.error('❌ ID do arquivo não fornecido');
        return NextResponse.json({ error: 'ID do arquivo não fornecido' }, { status: 400 });
    }

    try {
        if (!noCache && imageCache.has(fileId)) {
            const cachedImage = imageCache.get(fileId)!;
            console.log('💾 Imagem encontrada no cache:', fileId);

            return new NextResponse(cachedImage.buffer, {
                headers: {
                    'Content-Type': cachedImage.contentType,
                    'Cache-Control': 'public, max-age=86400',
                    'X-Cache': 'HIT'
                },
            });
        }

        // Estratégia 1: URL de thumbnail padrão
        const thumbnailUrl = `https://drive.google.com/thumbnail?id=${fileId}&sz=w800`;
        console.log('🔗 Tentando buscar imagem (estratégia 1):', thumbnailUrl);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        try {
            const response = await fetch(thumbnailUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                    'Referer': 'https://drive.google.com/',
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            console.log('📥 Resposta do Google Drive (estratégia 1):', {
                status: response.status,
                statusText: response.statusText,
                contentType: response.headers.get('Content-Type'),
                contentLength: response.headers.get('Content-Length')
            });

            if (response.ok) {
                const contentType = response.headers.get('Content-Type') || '';
                const contentLength = parseInt(response.headers.get('Content-Length') || '0');

                // Verificar se é realmente uma imagem
                if (contentType.startsWith('image/') && contentLength > 0) {
                    const buffer = await response.arrayBuffer();

                    console.log('✅ Imagem carregada com sucesso (estratégia 1):', {
                        fileId,
                        contentType,
                        bufferSize: buffer.byteLength
                    });

                    imageCache.set(fileId, {
                        buffer,
                        contentType,
                        timestamp: Date.now()
                    });

                    return new NextResponse(buffer, {
                        headers: {
                            'Content-Type': contentType,
                            'Cache-Control': 'public, max-age=86400',
                            'X-Cache': 'MISS'
                        },
                    });
                } else {
                    console.log('⚠️ Estratégia 1 retornou HTML em vez de imagem:', {
                        contentType,
                        contentLength
                    });
                }
            }

            // Estratégia 2: URL de thumbnail com tamanho diferente
            console.log('🔗 Tentando estratégia 2 com tamanho menor...');
            const smallThumbnailUrl = `https://drive.google.com/thumbnail?id=${fileId}&sz=w400`;

            const controller2 = new AbortController();
            const timeoutId2 = setTimeout(() => controller2.abort(), 10000);

            const response2 = await fetch(smallThumbnailUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                    'Referer': 'https://drive.google.com/',
                },
                signal: controller2.signal
            });

            clearTimeout(timeoutId2);

            if (response2.ok) {
                const contentType2 = response2.headers.get('Content-Type') || '';
                const contentLength2 = parseInt(response2.headers.get('Content-Length') || '0');

                if (contentType2.startsWith('image/') && contentLength2 > 0) {
                    const buffer2 = await response2.arrayBuffer();

                    console.log('✅ Imagem carregada com sucesso (estratégia 2):', {
                        fileId,
                        contentType: contentType2,
                        bufferSize: buffer2.byteLength
                    });

                    imageCache.set(fileId, {
                        buffer: buffer2,
                        contentType: contentType2,
                        timestamp: Date.now()
                    });

                    return new NextResponse(buffer2, {
                        headers: {
                            'Content-Type': contentType2,
                            'Cache-Control': 'public, max-age=86400',
                            'X-Cache': 'MISS'
                        },
                    });
                } else {
                    console.log('⚠️ Estratégia 2 retornou HTML em vez de imagem:', {
                        contentType: contentType2,
                        contentLength: contentLength2
                    });
                }
            }

            // Estratégia 3: URL de exportação direto
            console.log('🔗 Tentando estratégia 3 com exportação...');
            const exportUrl = `https://drive.google.com/uc?export=view&id=${fileId}`;

            const controller3 = new AbortController();
            const timeoutId3 = setTimeout(() => controller3.abort(), 10000);

            const incomingRange = request.headers.get('range') || undefined;
            if (incomingRange) console.log('➡️ Incoming Range header from client:', incomingRange);
            const fetchHeaders3: Record<string, string> = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                'Referer': 'https://drive.google.com/',
            };
            if (incomingRange) fetchHeaders3['Range'] = incomingRange;

            const response3 = await fetch(exportUrl, {
                headers: fetchHeaders3,
                signal: controller3.signal
            });

            clearTimeout(timeoutId3);

            if (response3.ok) {
                const contentType3 = response3.headers.get('Content-Type') || '';
                const contentLength3 = parseInt(response3.headers.get('Content-Length') || '0');

                // Se for imagem, continuamos a lógica de armazenar em cache e retornar buffer
                if (contentType3.startsWith('image/')) {
                    if (contentLength3 > 0) {
                        const buffer3 = await response3.arrayBuffer();

                        console.log('✅ Imagem carregada com sucesso (estratégia 3):', {
                            fileId,
                            contentType: contentType3,
                            bufferSize: buffer3.byteLength
                        });

                        imageCache.set(fileId, {
                            buffer: buffer3,
                            contentType: contentType3,
                            timestamp: Date.now()
                        });

                        return new NextResponse(buffer3, {
                            headers: {
                                'Content-Type': contentType3,
                                'Cache-Control': 'public, max-age=86400',
                                'X-Cache': 'MISS'
                            },
                        });
                    } else {
                        console.log('⚠️ Estratégia 3 retornou imagem vazia:', {
                            contentType: contentType3,
                            contentLength: contentLength3
                        });
                    }
                }

                // Se for vídeo, encaminhar o stream diretamente para o cliente
                if (contentType3.startsWith('video/') || contentType3.includes('octet-stream')) {
                    console.log('🎬 Vídeo detectado (estratégia 3), encaminhando stream:', { fileId, contentType: contentType3, downstreamStatus: response3.status });

                    const forwardHeaders: Record<string, string> = {
                        'Content-Type': contentType3,
                        'Cache-Control': 'public, max-age=86400',
                        'Accept-Ranges': 'bytes'
                    };

                    const downstreamContentRange = response3.headers.get('content-range');
                    const downstreamContentLength = parseInt(response3.headers.get('content-length') || '0');

                    // If downstream provided a Content-Range or already returned 206, just forward body/status/headers
                    if (downstreamContentRange || response3.status === 206) {
                        if (downstreamContentRange) forwardHeaders['Content-Range'] = downstreamContentRange;
                        if (downstreamContentLength) forwardHeaders['Content-Length'] = String(downstreamContentLength);
                        return new NextResponse(response3.body, {
                            status: response3.status,
                            headers: forwardHeaders
                        });
                    }

                    // Fallback: client requested Range but downstream returned 200 without Content-Range
                    // We'll fetch the full buffer and respond with a 206 partial slice when safe
                    const MAX_BUFFER_BYTES = 100 * 1024 * 1024; // 100 MB safe cap
                    if (incomingRange && downstreamContentLength > 0 && downstreamContentLength <= MAX_BUFFER_BYTES) {
                        try {
                            const full = await response3.arrayBuffer();
                            const total = full.byteLength;
                            const rangeMatch = incomingRange?.match(/bytes=(\d+)-(\d*)/);
                            const start = rangeMatch ? parseInt(rangeMatch[1], 10) : 0;
                            const end = rangeMatch && rangeMatch[2] ? Math.min(parseInt(rangeMatch[2], 10), total - 1) : Math.min(start + 1024 * 1024 * 4, total - 1); // cap chunk to 4MB if no end
                            const chunk = full.slice(start, end + 1);

                            forwardHeaders['Content-Range'] = `bytes ${start}-${end}/${total}`;
                            forwardHeaders['Content-Length'] = String(chunk.byteLength);

                            console.log('🔧 Fallback partial response (estratégia 3):', { fileId, start, end, total, chunkSize: chunk.byteLength });

                            return new NextResponse(chunk, {
                                status: 206,
                                headers: forwardHeaders
                            });
                        } catch (e) {
                            console.error('❌ Erro no fallback partial (estratégia 3):', e);
                        }
                    }

                    // Otherwise, forward the full response body (may be 200)
                    if (downstreamContentLength) forwardHeaders['Content-Length'] = String(downstreamContentLength);
                    return new NextResponse(response3.body, {
                        status: response3.status,
                        headers: forwardHeaders
                    });
                }
            }

            // Estratégia 4: URL de download direto (última tentativa)
            console.log('🔗 Tentando estratégia 4 com download direto...');
            const downloadUrl = `https://drive.google.com/uc?export=download&id=${fileId}`;

            const controller4 = new AbortController();
            const timeoutId4 = setTimeout(() => controller4.abort(), 10000);

            const fetchHeaders4: Record<string, string> = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                'Referer': 'https://drive.google.com/',
            };
            if (incomingRange) {
                fetchHeaders4['Range'] = incomingRange;
                console.log('➡️ Forwarding Range to download URL:', incomingRange);
            }

            const response4 = await fetch(downloadUrl, {
                headers: fetchHeaders4,
                signal: controller4.signal
            });

            clearTimeout(timeoutId4);

            if (response4.ok) {
                const contentType4 = response4.headers.get('Content-Type') || '';
                const contentLength4 = parseInt(response4.headers.get('Content-Length') || '0');

                // Se for imagem, armazenar e retornar buffer
                if (contentType4.startsWith('image/') && contentLength4 > 0) {
                    const buffer4 = await response4.arrayBuffer();

                    console.log('✅ Imagem carregada com sucesso (estratégia 4):', {
                        fileId,
                        contentType: contentType4,
                        bufferSize: buffer4.byteLength
                    });

                    imageCache.set(fileId, {
                        buffer: buffer4,
                        contentType: contentType4,
                        timestamp: Date.now()
                    });

                    return new NextResponse(buffer4, {
                        headers: {
                            'Content-Type': contentType4,
                            'Cache-Control': 'public, max-age=86400',
                            'X-Cache': 'MISS'
                        },
                    });
                }

                // Se for vídeo, encaminhar stream
                if ((contentType4.startsWith('video/') || contentType4.includes('octet-stream')) && contentLength4 > 0) {
                    console.log('🎬 Vídeo detectado (estratégia 4), encaminhando stream:', { fileId, contentType: contentType4, downstreamStatus: response4.status });

                    const forwardHeaders4: Record<string, string> = {
                        'Content-Type': contentType4,
                        'Cache-Control': 'public, max-age=86400',
                        'Accept-Ranges': 'bytes'
                    };

                    const downstreamContentRange4 = response4.headers.get('content-range');
                    const downstreamLength4 = parseInt(response4.headers.get('content-length') || '0');

                    if (downstreamContentRange4 || response4.status === 206) {
                        if (downstreamContentRange4) forwardHeaders4['Content-Range'] = downstreamContentRange4;
                        if (downstreamLength4) forwardHeaders4['Content-Length'] = String(downstreamLength4);
                        return new NextResponse(response4.body, {
                            status: response4.status,
                            headers: forwardHeaders4
                        });
                    }

                    const MAX_BUFFER_BYTES = 100 * 1024 * 1024;
                    if (incomingRange && downstreamLength4 > 0 && downstreamLength4 <= MAX_BUFFER_BYTES) {
                        try {
                            const full = await response4.arrayBuffer();
                            const total = full.byteLength;
                            const rangeMatch = incomingRange?.match(/bytes=(\d+)-(\d*)/);
                            const start = rangeMatch ? parseInt(rangeMatch[1], 10) : 0;
                            const end = rangeMatch && rangeMatch[2] ? Math.min(parseInt(rangeMatch[2], 10), total - 1) : Math.min(start + 1024 * 1024 * 4, total - 1);
                            const chunk = full.slice(start, end + 1);

                            forwardHeaders4['Content-Range'] = `bytes ${start}-${end}/${total}`;
                            forwardHeaders4['Content-Length'] = String(chunk.byteLength);

                            console.log('🔧 Fallback partial response (estratégia 4):', { fileId, start, end, total, chunkSize: chunk.byteLength });

                            return new NextResponse(chunk, {
                                status: 206,
                                headers: forwardHeaders4
                            });
                        } catch (e) {
                            console.error('❌ Erro no fallback partial (estratégia 4):', e);
                        }
                    }

                    if (downstreamLength4) forwardHeaders4['Content-Length'] = String(downstreamLength4);
                    return new NextResponse(response4.body, {
                        status: response4.status,
                        headers: forwardHeaders4
                    });
                }
            }

            throw new Error(`Todas as estratégias falharam. Última resposta: ${response4.status}`);

        } catch (error) {
            clearTimeout(timeoutId);

            const fetchError = error as Error;
            console.error('❌ Erro ao buscar imagem:', {
                fileId,
                error: fetchError.message,
                isTimeout: fetchError.name === 'AbortError'
            });

            if (fetchError && fetchError.name === 'AbortError') {
                throw new Error('Timeout ao buscar imagem');
            }

            throw error;
        }
    } catch (error) {
        console.error('❌ Erro geral no drive-proxy:', {
            fileId,
            error: error instanceof Error ? error.message : 'Erro desconhecido'
        });

        // redirecionar para placeholder público
        return NextResponse.redirect(new URL('/images/placeholder.png', request.url));
    }
}