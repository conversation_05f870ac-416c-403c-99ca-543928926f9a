import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

interface EventUpdateParams {
    title?: string;
    description?: string;
    startDate?: string;
    endDate?: string;
    allDay?: boolean;
    location?: string;
    calendarId?: string;
}

interface EventUpdateData {
    title?: string;
    description?: string;
    startDate?: Date;
    endDate?: Date | null;
    allDay?: boolean;
    location?: string;
    calendarId?: string;
    updatedAt: Date;
}

export async function GET(
    req: Request
) {
    try {
        const url = new URL(req.url);
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const id = url.pathname.split('/').pop();

        const event = await prisma.event.findUnique({
            where: {
                id: id,
            },
            include: {
                calendar: {
                    select: {
                        id: true,
                        name: true,
                        color: true,
                        client: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        });

        if (!event) {
            return NextResponse.json(
                { error: "Evento não encontrado" },
                { status: 404 }
            );
        }

        return NextResponse.json(event);
    } catch (error) {
        console.error("Erro ao buscar evento:", error);
        return NextResponse.json(
            { error: "Erro ao buscar evento" },
            { status: 500 }
        );
    }
}

export async function PATCH(
    req: Request
) {
    try {
        const url = new URL(req.url);
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const id = url.pathname.split('/').pop();
        const body: EventUpdateParams = await req.json();

        const event = await prisma.event.findUnique({
            where: {
                id: id,
            },
        });

        if (!event) {
            return NextResponse.json(
                { error: "Evento não encontrado" },
                { status: 404 }
            );
        }

        const updateData: EventUpdateData = {
            updatedAt: new Date(),
        };

        if (body.title !== undefined) updateData.title = body.title;
        if (body.description !== undefined) updateData.description = body.description;
        if (body.startDate !== undefined) updateData.startDate = new Date(body.startDate);
        if (body.endDate !== undefined) updateData.endDate = body.endDate ? new Date(body.endDate) : null;
        if (body.allDay !== undefined) updateData.allDay = body.allDay;
        if (body.location !== undefined) updateData.location = body.location;
        if (body.calendarId !== undefined) {
            const calendar = await prisma.calendar.findUnique({
                where: {
                    id: body.calendarId,
                },
            });

            if (!calendar) {
                return NextResponse.json(
                    { error: "Calendário não encontrado" },
                    { status: 404 }
                );
            }

            updateData.calendarId = body.calendarId;
        }

        const updatedEvent = await prisma.event.update({
            where: {
                id: id,
            },
            data: updateData,
        });

        return NextResponse.json(updatedEvent);
    } catch (error) {
        console.error("Erro ao atualizar evento:", error);
        return NextResponse.json(
            { error: "Erro ao atualizar evento" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    req: Request,
) {
    try {
        const url = new URL(req.url);
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const id = url.pathname.split('/').pop();

        const event = await prisma.event.findUnique({
            where: {
                id: id,
            },
        });

        if (!event) {
            return NextResponse.json(
                { error: "Evento não encontrado" },
                { status: 404 }
            );
        }

        await prisma.event.delete({
            where: {
                id: id,
            },
        });

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("Erro ao excluir evento:", error);
        return NextResponse.json(
            { error: "Erro ao excluir evento" },
            { status: 500 }
        );
    }
}
