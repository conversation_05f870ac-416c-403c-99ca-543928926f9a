import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { Prisma } from "@prisma/client";

interface EventCreateParams {
    title: string;
    description?: string;
    startDate: string;
    endDate?: string;
    allDay?: boolean;
    location?: string;
    calendarId: string;
}

export async function GET(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const url = new URL(req.url);
        const calendarId = url.searchParams.get('calendarId');
        const clientId = url.searchParams.get('clientId');
        const startDate = url.searchParams.get('startDate');
        const endDate = url.searchParams.get('endDate');
        
        const whereClause: Prisma.EventWhereInput = {};

        if (calendarId) {
            whereClause.calendarId = calendarId;
        }

        if (clientId) {
            whereClause.calendar = {
                clientId: clientId
            };
        }

        if (startDate && endDate) {
            whereClause.startDate = {
                gte: new Date(startDate)
            };
            whereClause.endDate = {
                lte: new Date(endDate)
            };
        } else if (startDate) {
            whereClause.startDate = {
                gte: new Date(startDate)
            };
        } else if (endDate) {
            whereClause.endDate = {
                lte: new Date(endDate)
            };
        }

        const events = await prisma.event.findMany({
            where: whereClause,
            include: {
                calendar: {
                    select: {
                        id: true,
                        name: true,
                        color: true,
                        client: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
            orderBy: {
                startDate: 'asc',
            },
        });

        return NextResponse.json(events);
    } catch (error) {
        console.error("Erro ao buscar eventos:", error);
        return NextResponse.json(
            { error: "Erro ao buscar eventos" },
            { status: 500 }
        );
    }
}

export async function POST(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const body: EventCreateParams = await req.json();

        if (!body.title || !body.startDate || !body.calendarId) {
            return NextResponse.json(
                { error: "Título, data de início e ID do calendário são obrigatórios" },
                { status: 400 }
            );
        }

        const calendar = await prisma.calendar.findUnique({
            where: {
                id: body.calendarId,
            },
        });

        if (!calendar) {
            return NextResponse.json(
                { error: "Calendário não encontrado" },
                { status: 404 }
            );
        }

        const newEvent = await prisma.event.create({
            data: {
                title: body.title,
                description: body.description,
                startDate: new Date(body.startDate),
                endDate: body.endDate ? new Date(body.endDate) : undefined,
                allDay: body.allDay,
                location: body.location,
                calendarId: body.calendarId,
            },
        });

        return NextResponse.json(newEvent, { status: 201 });
    } catch (error) {
        console.error("Erro ao criar evento:", error);
        return NextResponse.json(
            { error: "Erro ao criar evento" },
            { status: 500 }
        );
    }
}
