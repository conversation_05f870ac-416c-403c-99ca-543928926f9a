import { NextResponse } from "next/server";
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/app/lib/prisma';

const allowedRoles = ['ADMIN', 'DEVELOPER', 'DESIGNER_SENIOR'];

export async function GET() {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
        return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
    if (!requestingUser) {
        return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
    }

    if (!allowedRoles.includes(requestingUser.role)) {
        return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    return NextResponse.json(
        { message: "Rota descontinuada. Use /api/clients-for-feed-structuring?month=<m>&year=<y> para obter clientes elegíveis." },
        { status: 410 }
    );
}

export async function POST() {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
        return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
    if (!requestingUser) {
        return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
    }

    if (!allowedRoles.includes(requestingUser.role)) {
        return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    return NextResponse.json(
        { message: "Rota descontinuada. Use /api/clients-for-feed-structuring?month=<m>&year=<y> para obter clientes elegíveis." },
        { status: 410 }
    );
}
