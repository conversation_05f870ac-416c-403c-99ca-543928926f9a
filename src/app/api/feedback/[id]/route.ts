import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

export async function PATCH(req: Request) {
    try {
        const url = new URL(req.url);
        const id = url.pathname.split('/').pop();
        
        if (!id) {
            return NextResponse.json({ error: "ID não fornecido" }, { status: 400 });
        }

        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email || "" },
            select: { role: true },
        });

        if (user?.role !== "ADMIN" && user?.role !== "DEVELOPER") {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const { status } = await req.json();

        const updatedFeedback = await prisma.feedback.update({
            where: { id },
            data: { status },
        });

        return NextResponse.json(updatedFeedback);
    } catch (error) {
        console.error("Erro ao atualizar feedback:", error);
        return NextResponse.json(
            { error: "Erro ao processar a solicitação" },
            { status: 500 }
        );
    }
}