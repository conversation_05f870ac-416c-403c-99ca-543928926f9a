import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import prisma from "@/app/lib/prisma";
import { authOptions } from "@/lib/auth";

export async function POST(req: Request) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
        }

        const { title, description, type } = await req.json();

        if (!title || !description || !type) {
            return NextResponse.json({ error: "Dados inválidos" }, { status: 400 });
        }

        const feedback = await prisma.feedback.create({
            data: {
                title,
                description,
                type,
                userEmail: session.user.email || null,
                userName: session.user.name || null,
            },
        });

        await prisma.notification.create({
            data: {
                content: `${session.user.name || 'Um usuário'} enviou um novo feedback: "${title}"`,
                type: "new_feedback",
                entityId: feedback.id,
                entityType: "feedback",
                reference: "/admin/feedbacks",
                importance: type === "BUG" ? "high" : "normal",
                userId: null
            }
        });

        return NextResponse.json(feedback, { status: 201 });
    } catch (error) {
        console.error("Erro ao criar feedback:", error);
        return NextResponse.json(
            { error: "Erro ao processar a solicitação" },
            { status: 500 }
        );
    }
}

export async function GET() {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
        }
        const user = await prisma.user.findUnique({
            where: { email: session.user.email || "" },
            select: { role: true },
        });

        if (user?.role !== "ADMIN" && user?.role !== "DEVELOPER" && user?.role !== "GENERAL_ASSISTANT") {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const feedbacks = await prisma.feedback.findMany({
            orderBy: { createdAt: "desc" },
        });

        return NextResponse.json(feedbacks);
    } catch (error) {
        console.error("Erro ao buscar feedbacks:", error);
        return NextResponse.json(
            { error: "Erro ao processar a solicitação" },
            { status: 500 }
        );
    }
}