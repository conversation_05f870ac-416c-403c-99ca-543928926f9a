import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { Prisma } from "@prisma/client";

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    const url = new URL(req.url);
    const id = url.pathname.split('/').pop();

    if (!id) {
      return NextResponse.json({ error: "ID é obrigatório" }, { status: 400 });
    }

    const generalDemand = await prisma.generalDemand.findUnique({
      where: { id },
      include: {
        client: {
          select: {
            id: true,
            name: true,
          },
        },
        looseClient: {
          select: {
            id: true,
            name: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    if (!generalDemand) {
      return NextResponse.json(
        { error: "Demanda geral não encontrada" },
        { status: 404 }
      );
    }

    return NextResponse.json(generalDemand);
  } catch (error) {
    console.error("Erro ao buscar demanda geral:", error);
    return NextResponse.json(
      { error: "Erro ao processar a solicitação" },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email || "" },
      select: { role: true, accessLevel: true },
    });

    if (
      user?.role !== "ADMIN" &&
      user?.role !== "DEVELOPER" &&
      user?.accessLevel !== "EDITOR"
    ) {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const url = new URL(req.url);
    const id = url.pathname.split('/').pop();

    if (!id) {
      return NextResponse.json({ error: "ID é obrigatório" }, { status: 400 });
    }

    const data = await req.json();
    const {
      title,
      description,
      dueDate,
      status,
      priority,
      clientId,
      assignedToId,
      useLooseClient,
      looseClientName,
      urlStructuringFeed,
      review
    } = data;

    const existingDemand = await prisma.generalDemand.findUnique({
      where: { id },
      include: {
        assignedTo: {
          select: {
            email: true,
          },
        },
      },
    });

    if (!existingDemand) {
      return NextResponse.json(
        { error: "Demanda geral não encontrada" },
        { status: 404 }
      );
    }

    if (!useLooseClient && clientId) {
      const client = await prisma.client.findUnique({
        where: { id: clientId },
      });

      if (!client) {
        return NextResponse.json(
          { error: "Cliente não encontrado" },
          { status: 404 }
        );
      }
    }

    const assignedUser = assignedToId ? await prisma.user.findUnique({
      where: { id: assignedToId },
      select: {
        id: true,
        email: true,
        name: true,
      },
    }) : null;

    if (assignedToId && !assignedUser) {
      return NextResponse.json(
        { error: "Usuário atribuído não encontrado" },
        { status: 404 }
      );
    }

    const updateData: Prisma.GeneralDemandUpdateInput = {};
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (dueDate !== undefined) updateData.dueDate = dueDate ? new Date(dueDate) : null;
    if (status !== undefined) updateData.status = status;
    if (priority !== undefined) updateData.priority = priority;
    if (urlStructuringFeed !== undefined) updateData.urlStructuringFeed = urlStructuringFeed;
    if (review !== undefined) updateData.review = review;

    if (useLooseClient) {
      updateData.client = { disconnect: true };

      if (looseClientName) {
        const looseClient = await prisma.looseClient.create({
          data: {
            name: looseClientName
          }
        });

        updateData.looseClient = { connect: { id: looseClient.id } };
      }
    } else if (clientId !== undefined) {
      updateData.looseClient = { disconnect: true };
      updateData.client = { connect: { id: clientId } };
    }

    if (assignedToId !== undefined) {
      updateData.assignedTo = assignedToId ? { connect: { id: assignedToId } } : { disconnect: true };
      if (assignedToId && status === undefined) {
        updateData.status = "repassado";
      }
    }

    const updatedGeneralDemand = await prisma.generalDemand.update({
      where: { id },
      data: updateData,
      include: {
        client: {
          select: {
            id: true,
            name: true,
          },
        },
        looseClient: {
          select: {
            id: true,
            name: true,
          },
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    if (
      assignedToId &&
      assignedToId !== existingDemand.assignedToId &&
      assignedUser
    ) {
      await prisma.notification.create({
        data: {
          content: `Demanda geral atribuída : ${updatedGeneralDemand.title} (${
            updatedGeneralDemand.client?.name ||
            updatedGeneralDemand.looseClient?.name ||
            'Cliente não especificado'
          })`,
          type: "assigned_general_demand",
          entityId: updatedGeneralDemand.id,
          entityType: "general_demand",
          userId: assignedUser.email,
        },
      });
    }

    if (
      review !== undefined &&
      review.trim() !== '' &&
      updatedGeneralDemand.assignedTo?.email
    ) {
      await prisma.notification.create({
        data: {
          content: `Alteração solicitada na demanda: ${updatedGeneralDemand.title} (${
            updatedGeneralDemand.client?.name ||
            updatedGeneralDemand.looseClient?.name ||
            'Cliente não especificado'
          })`,
          type: "review_general_demand",
          entityId: updatedGeneralDemand.id,
          entityType: "general_demand",
          userId: updatedGeneralDemand.assignedTo.email,
          importance: "high",
        },
      });
    }

    return NextResponse.json(updatedGeneralDemand);
  } catch (error) {
    console.error("Erro ao atualizar demanda geral:", error);
    return NextResponse.json(
      { error: "Erro ao processar a solicitação" },
      { status: 500 }
    );
  }
}

export async function DELETE(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email || "" },
      select: { role: true },
    });

    if (user?.role !== "ADMIN" && user?.role !== "DEVELOPER") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const url = new URL(req.url);
    const id = url.pathname.split('/').pop();

    if (!id) {
      return NextResponse.json({ error: "ID é obrigatório" }, { status: 400 });
    }

    const existingDemand = await prisma.generalDemand.findUnique({
      where: { id },
    });

    if (!existingDemand) {
      return NextResponse.json(
        { error: "Demanda geral não encontrada" },
        { status: 404 }
      );
    }

    await prisma.generalDemand.delete({
      where: { id },
    });

    return NextResponse.json({ message: "Demanda geral excluída com sucesso" });
  } catch (error) {
    console.error("Erro ao excluir demanda geral:", error);
    return NextResponse.json(
      { error: "Erro ao processar a solicitação" },
      { status: 500 }
    );
  }
}

export const PATCH = PUT;
