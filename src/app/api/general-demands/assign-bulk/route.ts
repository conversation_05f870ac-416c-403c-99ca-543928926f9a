import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

interface ClientGroup {
  name: string;
  count: number;
}

interface ClientGroups {
  [clientId: string]: ClientGroup;
}

export async function PATCH(req: Request) {
  try {
    const { demandIds, userId } = await req.json();

    if (!demandIds || !Array.isArray(demandIds) || demandIds.length === 0) {
      return NextResponse.json({ message: "IDs das demandas são obrigatórios" }, { status: 400 });
    }

    const session = await getServerSession(authOptions);
    const assignerName = session?.user?.name || "Um usuário";

    let assignedUser = null;
    if (userId) {
      assignedUser = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          image: true
        }
      });
    }

    const existingDemands = await prisma.generalDemand.findMany({
      where: { id: { in: demandIds } },
      include: {
        client: true,
        looseClient: true
      }
    });

    if (existingDemands.length !== demandIds.length) {
      return NextResponse.json({ message: "Alguns IDs de demandas são inválidos" }, { status: 400 });
    }

    const updatedDemands = await prisma.generalDemand.updateMany({
      where: { id: { in: demandIds } },
      data: { 
        assignedToId: userId || null,
        ...(userId ? { status: "repassado" } : {})
      }
    });

    const demandsWithAssignee = await prisma.generalDemand.findMany({
      where: { id: { in: demandIds } },
      include: {
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      }
    });

    if (userId && assignedUser) {
      const clientGroups = existingDemands.reduce<ClientGroups>((groups, demand) => {
        const clientInfo = (() => {
          if (demand.client) {
            return {
              id: demand.client.id,
              name: demand.client.name
            };
          } else if (demand.looseClient) {
            return {
              id: demand.looseClient.id,
              name: `${demand.looseClient.name} ⦁ não fixo`
            };
          } else {
            return {
              id: 'unknown',
              name: 'Cliente não especificado'
            };
          }
        })();

        const updatedGroups = { ...groups };

        if (!updatedGroups[clientInfo.id]) {
          updatedGroups[clientInfo.id] = {
            name: clientInfo.name,
            count: 0
          };
        }

        updatedGroups[clientInfo.id].count++;
        return updatedGroups;
      }, {});

      for (const clientId in clientGroups) {
        const client = clientGroups[clientId];
        await prisma.notification.create({
          data: {
            content: `${assignerName} atribuiu ${client.count} demanda${client.count > 1 ? 's' : ''} pontu${client.count > 1 ? 'ais' : 'al'} de ${client.name} `,
            type: "assigned_general_demand",
            entityType: "general_demand",
            userId: assignedUser.email
          }
        });
      }
    }

    return NextResponse.json({
      count: updatedDemands.count,
      demands: demandsWithAssignee
    });
  } catch (error) {
    console.error("Erro ao atribuir demandas gerais:", error);
    return NextResponse.json(
      { message: "Erro ao atribuir demandas gerais", error },
      { status: 500 }
    );
  }
}
