import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import prisma from "@/app/lib/prisma";
import { authOptions } from "@/lib/auth";

export async function DELETE(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email || "" },
      select: { role: true },
    });

    if (user?.role !== "ADMIN" && user?.role !== "DEVELOPER") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const { demandIds } = await req.json();

    if (!demandIds || !Array.isArray(demandIds) || demandIds.length === 0) {
      return NextResponse.json(
        { error: "IDs de demandas são obrigatórios" },
        { status: 400 }
      );
    }

    const existingDemands = await prisma.generalDemand.findMany({
      where: { id: { in: demandIds } },
      select: { id: true }
    });

    if (existingDemands.length !== demandIds.length) {
      return NextResponse.json(
        { error: "Algumas demandas não foram encontradas ou não são demandas pontuais" },
        { status: 400 }
      );
    }

    const result = await prisma.generalDemand.deleteMany({
      where: { id: { in: demandIds } }
    });

    return NextResponse.json({
      message: "Demandas removidas com sucesso",
      count: result.count
    });
  } catch (error) {
    console.error("Erro ao remover demandas:", error);
    return NextResponse.json(
      { error: "Erro ao processar a solicitação" },
      { status: 500 }
    );
  }
}
