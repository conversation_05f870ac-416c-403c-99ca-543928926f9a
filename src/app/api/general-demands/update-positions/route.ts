import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

interface PositionUpdate {
  id: string;
  position: number;
  priority: string;
}

interface UpdatePositionsRequest {
  updates: PositionUpdate[];
  userId: string;
}

export async function PATCH(req: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Não autenticado" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email || "" },
      select: { role: true },
    });

    if (user?.role !== "ADMIN" && user?.role !== "DEVELOPER") {
      return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
    }

    const data: UpdatePositionsRequest = await req.json();
    const { updates, userId } = data;

    if (!updates || !Array.isArray(updates) || updates.length === 0) {
      return NextResponse.json(
        { error: "Atualizações de posição são obrigatórias" },
        { status: 400 }
      );
    }

    if (!userId) {
      return NextResponse.json(
        { error: "ID do colaborador é obrigatório" },
        { status: 400 }
      );
    }

    for (const update of updates) {
      if (!update.id || typeof update.position !== 'number' || !update.priority) {
        return NextResponse.json(
          { error: "Formato de atualização inválido" },
          { status: 400 }
        );
      }
    }

    const demandIds = updates.map(update => update.id);

    if (user?.role !== "ADMIN" && user?.role !== "DEVELOPER") {
      const existingDemands = await prisma.generalDemand.findMany({
        where: {
          id: { in: demandIds },
          assignedToId: userId
        }
      });

      if (existingDemands.length !== demandIds.length) {
        return NextResponse.json(
          { error: "Algumas demandas não pertencem ao colaborador especificado" },
          { status: 400 }
        );
      }
    }

    const updatePromises = updates.map((update: PositionUpdate) => {
      const whereCondition = user?.role === "ADMIN" || user?.role === "DEVELOPER"
        ? { id: update.id }
        : { id: update.id, assignedToId: userId };

      return prisma.generalDemand.update({
        where: whereCondition,
        data: {
          position: update.position,
          priority: update.priority
        }
      });
    });

    await Promise.all(updatePromises);

    return NextResponse.json({ success: true, count: updates.length });
  } catch (error) {
    console.error("Erro ao atualizar posições das demandas:", error);
    return NextResponse.json(
      { error: "Erro ao processar a solicitação" },
      { status: 500 }
    );
  }
}
