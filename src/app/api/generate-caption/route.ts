import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const { originalCaption } = body;

        if (!originalCaption) {
            return NextResponse.json({ error: 'Legenda original é obrigatória' }, { status: 400 });
        }

        const prompt = `Melhore esta legenda mantendo todo o conteúdo importante, mas garantindo que tenha no máximo 523 caracteres. Se já estiver dentro do limite, faça apenas pequenos ajustes para melhorar a clareza. Mantenha o tom original e todas as hashtags relevantes:

"${originalCaption}"

Legenda otimizada:`;

        const apiKey = process.env.GEMINI_API_KEY;
        if (!apiKey) {
            return NextResponse.json({ error: 'Chave da API Gemini não encontrada' }, { status: 500 });
        }

        const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                contents: [{ parts: [{ text: prompt }] }]
            }),
        });

        if (response.ok) {
            const data = await response.json();
            const caption = data.candidates?.[0]?.content?.parts?.[0]?.text || 'Erro ao gerar legenda';
            return NextResponse.json({ caption });
        } else {
            const errorData = await response.text();
            return NextResponse.json({ error: 'Erro na API Gemini: ' + errorData }, { status: 500 });
        }

    } catch (error) {
        console.error('Erro na API:', error);
        return NextResponse.json({ error: 'Erro interno: ' + (error instanceof Error ? error.message : 'Erro desconhecido') }, { status: 500 });
    }
}