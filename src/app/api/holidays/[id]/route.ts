import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

interface HolidayUpdateParams {
    title?: string;
    description?: string;
    month?: number;
    day?: number | null;
    dayNotFixed?: string;
    color?: string;
    allDay?: boolean;
    isRecurring?: boolean;
}

export async function GET(
    req: Request,
) {
    try {
        const url = new URL(req.url);
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const id = url.pathname.split('/').pop();

        const holiday = await prisma.holidayEvent.findUnique({
            where: {
                id: id,
            },
            include: {
                calendar: {
                    select: {
                        id: true,
                        name: true,
                        color: true,
                        client: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        });

        if (!holiday) {
            return NextResponse.json(
                { error: "Data comemorativa não encontrada" },
                { status: 404 }
            );
        }

        return NextResponse.json(holiday);
    } catch (error) {
        console.error("Erro ao buscar data comemorativa:", error);
        return NextResponse.json(
            { error: "Erro ao buscar data comemorativa" },
            { status: 500 }
        );
    }
}

export async function PATCH(
    req: Request,
) {
    try {
        const url = new URL(req.url);
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const id = url.pathname.split('/').pop();
        const body: HolidayUpdateParams = await req.json();

        const holiday = await prisma.holidayEvent.findUnique({
            where: {
                id: id,
            },
        });

        if (!holiday) {
            return NextResponse.json(
                { error: "Data comemorativa não encontrada" },
                { status: 404 }
            );
        }

        if (body.month !== undefined && (body.month < 1 || body.month > 12)) {
            return NextResponse.json(
                { error: "Mês inválido" },
                { status: 400 }
            );
        }

        if (body.month !== undefined && body.day !== undefined && body.day !== null) {
            const daysInMonth = new Date(new Date().getFullYear(), body.month, 0).getDate();
            if (body.day < 1 || body.day > daysInMonth) {
                return NextResponse.json(
                    { error: "Dia inválido para o mês selecionado" },
                    { status: 400 }
                );
            }
        }

        const updatedHoliday = await prisma.holidayEvent.update({
            where: {
                id: id,
            },
            data: {
                title: body.title,
                description: body.description,
                month: body.month,
                day: body.day,
                dayNotFixed: body.dayNotFixed,
                color: body.color,
                allDay: body.allDay,
                isRecurring: body.isRecurring,
                updatedAt: new Date(),
            },
        });

        return NextResponse.json(updatedHoliday);
    } catch (error) {
        console.error("Erro ao atualizar data comemorativa:", error);
        return NextResponse.json(
            { error: "Erro ao atualizar data comemorativa" },
            { status: 500 }
        );
    }
}

export async function DELETE(
    req: Request
) {
    try {
        const url = new URL(req.url);
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const id = url.pathname.split('/').pop();

        const holiday = await prisma.holidayEvent.findUnique({
            where: {
                id: id,
            },
        });

        if (!holiday) {
            return NextResponse.json(
                { error: "Data comemorativa não encontrada" },
                { status: 404 }
            );
        }

        await prisma.holidayEvent.delete({
            where: {
                id: id,
            },
        });

        return NextResponse.json({ success: true });
    } catch (error) {
        console.error("Erro ao excluir data comemorativa:", error);
        return NextResponse.json(
            { error: "Erro ao excluir data comemorativa" },
            { status: 500 }
        );
    }
}
