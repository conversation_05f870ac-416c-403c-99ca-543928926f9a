import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";

interface HolidayCreateParams {
    title: string;
    description?: string;
    month: number;
    day?: number;
    dayNotFixed?: string;
    color?: string;
    allDay?: boolean;
    isRecurring?: boolean;
    calendarId: string;
}

export async function GET(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const url = new URL(req.url);
        const calendarId = url.searchParams.get("calendarId");

        if (!calendarId) {
            return NextResponse.json(
                { error: "ID do calendário é obrigatório" },
                { status: 400 }
            );
        }

        const holidays = await prisma.holidayEvent.findMany({
            where: {
                calendarId: calendarId,
            },
            orderBy: [
                { month: 'asc' },
                { day: 'asc' },
            ],
            include: {
                calendar: {
                    select: {
                        id: true,
                        name: true,
                        color: true,
                        client: {
                            select: {
                                id: true,
                                name: true,
                            },
                        },
                    },
                },
            },
        });

        return NextResponse.json(holidays);
    } catch (error) {
        console.error("Erro ao buscar datas comemorativas:", error);
        return NextResponse.json(
            { error: "Erro ao buscar datas comemorativas" },
            { status: 500 }
        );
    }
}

export async function POST(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const body: HolidayCreateParams = await req.json();

        if (!body.title || !body.calendarId || body.month === undefined || (body.day === undefined && !body.dayNotFixed)) {
            return NextResponse.json(
                { error: "Campos obrigatórios não preenchidos" },
                { status: 400 }
            );
        }

        if (body.month < 1 || body.month > 12) {
            return NextResponse.json(
                { error: "Mês inválido" },
                { status: 400 }
            );
        }

        if (body.day !== undefined) {
            const daysInMonth = new Date(new Date().getFullYear(), body.month, 0).getDate();
            if (body.day < 1 || body.day > daysInMonth) {
                return NextResponse.json(
                    { error: "Dia inválido para o mês selecionado" },
                    { status: 400 }
                );
            }
        }

        const calendar = await prisma.calendar.findUnique({
            where: {
                id: body.calendarId,
            },
        });

        if (!calendar) {
            return NextResponse.json(
                { error: "Calendário não encontrado" },
                { status: 404 }
            );
        }

        const holiday = await prisma.holidayEvent.create({
            data: {
                title: body.title,
                description: body.description,
                month: body.month,
                day: body.day !== undefined ? body.day : null,
                dayNotFixed: body.dayNotFixed,
                color: body.color,
                allDay: body.allDay !== undefined ? body.allDay : true,
                isRecurring: body.isRecurring !== undefined ? body.isRecurring : true,
                calendarId: body.calendarId,
            },
        });

        return NextResponse.json(holiday);
    } catch (error) {
        console.error("Erro ao criar data comemorativa:", error);
        return NextResponse.json(
            { error: "Erro ao criar data comemorativa" },
            { status: 500 }
        );
    }
}
