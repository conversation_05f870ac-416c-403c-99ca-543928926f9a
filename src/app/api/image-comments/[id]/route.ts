import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import prisma from '@/app/lib/prisma';
import { authOptions } from '@/lib/auth';

export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const url = new URL(req.url);
    const params = url.pathname.split('/').pop();
    const commentId = params;
    const body = await req.json();
    const { text, x, y } = body;

    const existingComment = await prisma.imageComment.findUnique({
      where: { id: commentId },
      include: {
        author: true,
      },
    });

    if (!existingComment) {
      return NextResponse.json(
        { error: 'Comentário não encontrado' },
        { status: 404 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não encontrado' },
        { status: 404 }
      );
    }

    const canEdit =
      existingComment.authorId === user.id ||
      user.role === 'ADMIN' ||
      user.role === 'DEVELOPER';

    if (!canEdit) {
      return NextResponse.json(
        { error: 'Sem permissão para editar este comentário' },
        { status: 403 }
      );
    }

    const updateData: { text?: string; x?: number; y?: number } = {};

    if (text !== undefined) {
      if (typeof text !== 'string' || text.trim().length === 0) {
        return NextResponse.json(
          { error: 'Texto do comentário é obrigatório' },
          { status: 400 }
        );
      }
      updateData.text = text.trim();
    }

    if (x !== undefined) {
      if (typeof x !== 'number' || x < 0 || x > 100) {
        return NextResponse.json(
          { error: 'Posição X deve ser um número entre 0 e 100' },
          { status: 400 }
        );
      }
      updateData.x = x;
    }

    if (y !== undefined) {
      if (typeof y !== 'number' || y < 0 || y > 100) {
        return NextResponse.json(
          { error: 'Posição Y deve ser um número entre 0 e 100' },
          { status: 400 }
        );
      }
      updateData.y = y;
    }

    const updatedComment = await prisma.imageComment.update({
      where: { id: commentId },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json(updatedComment);
  } catch (error) {
    console.error('Erro ao atualizar comentário:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const url = new URL(req.url);
    const params = url.pathname.split('/').pop();
    const commentId = params;

    const existingComment = await prisma.imageComment.findUnique({
      where: { id: commentId },
      include: {
        author: true,
      },
    });

    if (!existingComment) {
      return NextResponse.json(
        { error: 'Comentário não encontrado' },
        { status: 404 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não encontrado' },
        { status: 404 }
      );
    }

    const canDelete =
      existingComment.authorId === user.id ||
      user.role === 'ADMIN' ||
      user.role === 'DEVELOPER';

    if (!canDelete) {
      return NextResponse.json(
        { error: 'Sem permissão para deletar este comentário' },
        { status: 403 }
      );
    }

    await prisma.imageComment.delete({
      where: { id: commentId },
    });

    return NextResponse.json({ message: 'Comentário deletado com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar comentário:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
