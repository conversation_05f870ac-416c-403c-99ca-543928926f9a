import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const allowedRolesForGet = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT', 'COPY', 'DESIGNER_SENIOR'];
        if (!allowedRolesForGet.includes(requestingUser.role)) {
            return NextResponse.json({ error: '<PERSON><PERSON> negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const clientId = url.searchParams.get('clientId');
        const month = url.searchParams.get('month');
        const year = url.searchParams.get('year');
        const planningId = url.searchParams.get('planningId');

        if (planningId) {
            const planning = await prisma.monthlyPlanning.findUnique({
                where: { id: planningId },
                include: {
                    client: true,
                    activities: {
                        include: {
                            contents: true
                        }
                    }
                }
            });

            if (!planning) {
                return NextResponse.json(
                    { message: "Planejamento não encontrado" },
                    { status: 404 }
                );
            }

            const contentCounts = planning.activities.reduce((counts, activity) => {
                activity.contents.forEach(content => {
                    const destination = content.destination?.toLowerCase();
                    if (destination === "feed" || destination === "story/feed") {
                        counts.posts++;
                    }
                    if (destination === "story" || destination === "story/feed") {
                        if (content.contentType?.toLowerCase() === "carrossel" && destination === "story" && content.carouselImagesCount) {
                            counts.stories += content.carouselImagesCount;
                        } else {
                            counts.stories++;
                        }
                    }
                });
                return counts;
            }, { posts: 0, stories: 0 });

            const postsLimitExceeded = planning.client.monthlyPostsLimit 
                ? contentCounts.posts >= planning.client.monthlyPostsLimit
                : false;

            const storiesLimitExceeded = planning.client.monthlyStoriesLimit 
                ? contentCounts.stories >= planning.client.monthlyStoriesLimit
                : false;

            return NextResponse.json({
                planningId,
                clientName: planning.client.name,
                monthlyPostsLimit: planning.client.monthlyPostsLimit,
                monthlyStoriesLimit: planning.client.monthlyStoriesLimit,
                currentPostCount: contentCounts.posts,
                currentStoryCount: contentCounts.stories,
                postsLimitExceeded,
                storiesLimitExceeded,
                remainingPosts: planning.client.monthlyPostsLimit 
                    ? Math.max(0, planning.client.monthlyPostsLimit - contentCounts.posts) 
                    : null,
                remainingStories: planning.client.monthlyStoriesLimit 
                    ? Math.max(0, planning.client.monthlyStoriesLimit - contentCounts.stories) 
                    : null
            });
        }

        if (!clientId || !month || !year) {
            return NextResponse.json(
                { message: "Parâmetros incompletos: clientId, month e year são obrigatórios" },
                { status: 400 }
            );
        }

        const existingPlannings = await prisma.monthlyPlanning.findMany({
            where: {
                clientId,
                month: parseInt(month),
                year: parseInt(year)
            },
            include: {
                client: true,
                activities: {
                    include: {
                        contents: true
                    }
                }
            }
        });

        return NextResponse.json(existingPlannings);
    } catch (error) {
        console.error("Erro ao buscar planejamentos:", error);
        return NextResponse.json(
            { message: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}

export async function POST(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const allowedRolesForPost = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];
        if (!allowedRolesForPost.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const data = await req.json();
        const { clientId, month, year } = data;

        if (!clientId || !month || !year) {
            return NextResponse.json(
                { message: "Dados incompletos. É necessário fornecer clientId, month e year" },
                { status: 400 }
            );
        }

        const clientExists = await prisma.client.findUnique({
            where: { id: clientId }
        });

        if (!clientExists) {
            return NextResponse.json(
                { message: "Cliente não encontrado" },
                { status: 404 }
            );
        }

        const existingPlanning = await prisma.monthlyPlanning.findFirst({
            where: {
                clientId,
                month,
                year
            }
        });

        if (existingPlanning) {
            return NextResponse.json(
                { message: "Já existe um planejamento para este cliente neste mês e ano" },
                { status: 409 }
            );
        }

        const newPlanning = await prisma.monthlyPlanning.create({
            data: {
                clientId,
                month,
                year
            }
        });

        return NextResponse.json(newPlanning, { status: 201 });
    } catch (error) {
        console.error("Erro ao criar planejamento:", error);
        return NextResponse.json(
            { message: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}