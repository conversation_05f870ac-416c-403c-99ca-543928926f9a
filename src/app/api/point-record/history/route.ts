import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

export async function GET() {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const allowedRoles = ["DESIGNER", "COPY", "DESIGNER_SENIOR", "DESIGNER_JUNIOR", "ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"];
        
        if (!allowedRoles.includes(user.role)) {
            return NextResponse.json({ error: "<PERSON><PERSON> negado" }, { status: 403 });
        }

        const records = await prisma.pointRecord.findMany({
            where: {
                userId: user.id
            },
            orderBy: {
                date: 'desc'
            },
            take: 50
        });

        const formattedRecords = records.map(record => ({
            id: record.id,
            clockIn: record.clockIn?.toISOString(),
            clockOut: record.clockOut?.toISOString(),
            date: record.date.toISOString().split('T')[0],
            totalHours: record.totalHours,
            createdAt: record.createdAt.toISOString()
        }));

        return NextResponse.json(formattedRecords);

    } catch (error) {
        console.error("Erro ao buscar histórico de ponto:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}