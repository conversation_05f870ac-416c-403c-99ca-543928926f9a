import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const { action, date, userIP } = await request.json();

        if (!action || !date) {
            return NextResponse.json({ error: "Dados obrigatórios não fornecidos" }, { status: 400 });
        }

        const allowedIPs = [
            '*************',
            '127.0.0.1',
            '**************',
            '***************'
        ];

        if (userIP && !allowedIPs.includes(userIP)) {
            return NextResponse.json({
                error: "Registro de ponto não autorizado para este local."
            }, { status: 403 });
        }

        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        if (action === 'in') {
            const pointRecord = await prisma.pointRecord.create({
                data: {
                    userId: user.id,
                    date: today,
                    clockIn: now
                }
            });

            return NextResponse.json({
                id: pointRecord.id,
                clockIn: pointRecord.clockIn?.toISOString(),
                clockOut: null,
                date: pointRecord.date.toISOString().split('T')[0],
                totalHours: pointRecord.totalHours,
                hasOpenEntry: true
            });
        } else if (action === 'out') {
            const lastOpenRecord = await prisma.pointRecord.findFirst({
                where: {
                    userId: user.id,
                    date: today,
                    clockIn: { not: null },
                    clockOut: null
                },
                orderBy: { clockIn: 'desc' }
            });

            if (!lastOpenRecord) {
                return NextResponse.json({ error: "Nenhuma entrada em aberto para registrar saída" }, { status: 400 });
            }

            const totalHours = (now.getTime() - lastOpenRecord.clockIn!.getTime()) / (1000 * 60 * 60);
            const timeBank = totalHours > 8.8 ? totalHours - 8.8 : 0;

            const pointRecord = await prisma.pointRecord.update({
                where: { id: lastOpenRecord.id },
                data: {
                    clockOut: now,
                    totalHours,
                    timeBank
                }
            });

            return NextResponse.json({
                id: pointRecord.id,
                clockIn: pointRecord.clockIn?.toISOString(),
                clockOut: pointRecord.clockOut?.toISOString(),
                date: pointRecord.date.toISOString().split('T')[0],
                totalHours: pointRecord.totalHours
            });
        }

    } catch (error) {
        console.error("Erro ao registrar ponto:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}