import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { Prisma } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(req: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const allowedRoles = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];
        if (!allowedRoles.includes(requestingUser.role)) {
            return NextResponse.json({ error: '<PERSON><PERSON> negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const segments = url.pathname.split('/').filter(Boolean);
        const id = segments[segments.length - 1];
        const quote = await prisma.quote.findUnique({
            where: { id }, select: {
                id: true,
                client: true,
                clientPhoneNumber: true,
                budgetType: true,
                totalPrice: true,
                discount: true,
                createdAt: true,
                services: {
                    orderBy: { sortOrder: 'asc' },
                    select: {
                        id: true,
                        quoteId: true,
                        serviceId: true,
                        budgetItemId: true,
                        include: true,
                        customLabel: true,
                        customDescription: true,
                        quantity: true,
                        notes: true,
                        budgetTemplateId: true,
                        templateSnapshot: true,
                        unitPrice: true,
                        itemTotal: true,
                        Service: { select: { id: true, code: true, name: true } },
                        BudgetItem: { select: { id: true, code: true, name: true } },
                    }
                }
            }
        });
        if (!quote) return NextResponse.json({ error: 'Orçamento não encontrado' }, { status: 404 });
        return NextResponse.json(quote);
    } catch (err) {
        console.error('GET /api/quotes/[id] error', err);
        return NextResponse.json({ error: 'Erro ao buscar orçamento' }, { status: 500 });
    }
}

export async function PATCH(req: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const allowedRoles = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];
        if (!allowedRoles.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const segments = url.pathname.split('/').filter(Boolean);
        const id = segments[segments.length - 1];
        const body = await req.json();

        const client = body.client ?? undefined;
        const clientNumber = body.clientNumber ?? undefined;
        const socialMediaRaw: unknown[] = Array.isArray(body.socialMedia) ? body.socialMedia as unknown[] : [];
        const socialMedia = body.socialMedia === undefined ? undefined : socialMediaRaw.map((s: unknown) => (s === null || s === undefined) ? '' : String(s)).filter(Boolean);
        const totalPriceRaw = body.totalPrice ?? undefined;
        const totalPriceNumber = totalPriceRaw !== undefined ? Number(totalPriceRaw) : undefined;
        const discountRaw = body.discount ?? undefined;
        const discountNumber = discountRaw !== undefined ? Number(discountRaw) : undefined;
        const budgetType = body.budgetType ?? undefined;
        const servicesInputRaw: unknown[] = Array.isArray(body.services) ? body.services : [];
        const servicesInput: { key: string; quantity?: string; customDescription?: string; notes?: string; include?: boolean; unitPrice?: number | null; itemTotal?: number | null; budgetTemplateId?: string | null; templateSnapshot?: Prisma.InputJsonValue | undefined }[] = servicesInputRaw.map((s) => {
            if (typeof s === 'string') return { key: s };
            if (s && typeof s === 'object') {
                const rec = s as Record<string, unknown>;
                return {
                    key: String(rec['key'] ?? rec['value'] ?? rec['code'] ?? ''),
                    quantity: rec['quantity'] ? String(rec['quantity']) : undefined,
                    customDescription: rec['customDescription'] ? String(rec['customDescription']) : undefined,
                    notes: rec['notes'] ? String(rec['notes']) : undefined,
                    include: typeof rec['include'] === 'boolean' ? rec['include'] as boolean : undefined,
                    unitPrice: rec['unitPrice'] !== undefined ? (rec['unitPrice'] === null ? null : Number(rec['unitPrice'])) : undefined,
                    itemTotal: rec['itemTotal'] !== undefined ? (rec['itemTotal'] === null ? null : Number(rec['itemTotal'])) : undefined,
                    budgetTemplateId: rec['budgetTemplateId'] ? String(rec['budgetTemplateId']) : undefined,
                    templateSnapshot: (rec['templateSnapshot'] ?? undefined) as Prisma.InputJsonValue | undefined,
                };
            }
            return { key: '' };
        });
        const budgetItemInputs: string[] = Array.isArray(body.budgetItemIds) ? body.budgetItemIds : [];

        const resolvedServiceIds: { id: string; key: string; quantity?: string; customDescription?: string; notes?: string; include?: boolean; unitPrice?: number | null; itemTotal?: number | null; budgetTemplateId?: string | null; templateSnapshot?: Prisma.InputJsonValue | undefined }[] = [];
        const unknownServiceLabels: { label: string; quantity?: string; customDescription?: string; notes?: string; include?: boolean; unitPrice?: number | null; itemTotal?: number | null; budgetTemplateId?: string | null; templateSnapshot?: Prisma.InputJsonValue | undefined }[] = [];
        if (servicesInput.length > 0) {
            const keys = servicesInput.map(s => s.key).filter(Boolean) as string[];
            const found = await prisma.service.findMany({ where: { OR: [{ id: { in: keys } }, { code: { in: keys } }] } });
            for (const inp of servicesInput) {
                const f = found.find(x => x.id === inp.key || x.code === inp.key);
                if (f) resolvedServiceIds.push({ id: f.id, key: inp.key, quantity: inp.quantity, customDescription: inp.customDescription, notes: inp.notes, include: inp.include, unitPrice: inp.unitPrice, itemTotal: inp.itemTotal, budgetTemplateId: inp.budgetTemplateId, templateSnapshot: inp.templateSnapshot });
                else unknownServiceLabels.push({ label: inp.key, quantity: inp.quantity, customDescription: inp.customDescription, notes: inp.notes, include: inp.include, unitPrice: inp.unitPrice, itemTotal: inp.itemTotal, budgetTemplateId: inp.budgetTemplateId, templateSnapshot: inp.templateSnapshot });
            }
        }

        const resolvedBudgetItemIds: string[] = [];
        const unknownBudgetLabels: string[] = [];
        if (budgetItemInputs.length > 0) {
            const found = await prisma.budgetItem.findMany({ where: { OR: [{ id: { in: budgetItemInputs } }, { code: { in: budgetItemInputs } }] } });
            for (const inp of budgetItemInputs) {
                const f = found.find(x => x.id === inp || x.code === inp);
                if (f) resolvedBudgetItemIds.push(f.id);
                else unknownBudgetLabels.push(inp);
            }
        }

        const updated = await prisma.$transaction(async (tx) => {
            await tx.quote.update({ where: { id }, data: { ...(client !== undefined ? { client } : {}), ...(clientNumber !== undefined ? { clientPhoneNumber: clientNumber } : {}), ...(socialMedia !== undefined ? { socialMedia } : {}), ...(budgetType !== undefined ? { budgetType } : {}), ...(totalPriceNumber !== undefined ? { totalPrice: totalPriceNumber } : {}), ...(discountNumber !== undefined ? { discount: discountNumber } : {}) } });

            if (resolvedBudgetItemIds.length > 0 || resolvedServiceIds.length > 0 || unknownServiceLabels.length > 0 || unknownBudgetLabels.length > 0) {
                await tx.quoteItem.deleteMany({ where: { quoteId: id } });
                const itemsToCreate: Prisma.QuoteItemUncheckedCreateInput[] = [];
                for (const bid of resolvedBudgetItemIds) itemsToCreate.push({ quoteId: id, budgetItemId: bid });
                for (const sid of resolvedServiceIds) itemsToCreate.push({ quoteId: id, serviceId: sid.id, quantity: sid.quantity, customDescription: sid.customDescription, notes: sid.notes, include: sid.include, unitPrice: sid.unitPrice, itemTotal: sid.itemTotal, budgetTemplateId: sid.budgetTemplateId, templateSnapshot: sid.templateSnapshot });
                for (const label of unknownServiceLabels) itemsToCreate.push({ quoteId: id, customLabel: label.label, quantity: label.quantity, customDescription: label.customDescription, notes: label.notes, include: label.include, unitPrice: label.unitPrice, itemTotal: label.itemTotal, budgetTemplateId: label.budgetTemplateId, templateSnapshot: label.templateSnapshot });
                for (const label of unknownBudgetLabels) itemsToCreate.push({ quoteId: id, customLabel: label });
                if (itemsToCreate.length > 0) {
                    for (const it of itemsToCreate) {
                        await tx.quoteItem.create({ data: it });
                    }
                }
            }

            return tx.quote.findUnique({ where: { id }, include: { services: { include: { Service: true, BudgetItem: true } } } });
        });

        return NextResponse.json(updated);
    } catch (err) {
        console.error('PATCH /api/quotes/[id] error', err);
        return NextResponse.json({ error: 'Erro ao atualizar orçamento' }, { status: 500 });
    }
}

export async function DELETE(req: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        const allowedRoles = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];
        if (!allowedRoles.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const segments = url.pathname.split('/').filter(Boolean);
        const id = segments[segments.length - 1];
        await prisma.quoteItem.deleteMany({ where: { quoteId: id } });
        await prisma.quote.delete({ where: { id } });
        return NextResponse.json({ success: true });
    } catch (err) {
        console.error('DELETE /api/quotes/[id] error', err);
        return NextResponse.json({ error: 'Erro ao deletar orçamento' }, { status: 500 });
    }
}
