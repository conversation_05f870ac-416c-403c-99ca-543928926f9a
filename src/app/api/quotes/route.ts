import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { Prisma } from '@prisma/client';
import type { Service, BudgetItem } from '@prisma/client';

export async function GET() {
    try {
        const quotes = await prisma.quote.findMany({
            orderBy: { createdAt: 'desc' },
            select: {
                id: true,
                client: true,
                socialMedia: true,
                clientPhoneNumber: true,
                budgetType: true,
                totalPrice: true,
                discount: true,
                createdAt: true,
                services: {
                    orderBy: { sortOrder: 'asc' },
                    select: {
                        id: true,
                        quoteId: true,
                        serviceId: true,
                        budgetItemId: true,
                        include: true,
                        customLabel: true,
                        customDescription: true,
                        quantity: true,
                        notes: true,
                        budgetTemplateId: true,
                        templateSnapshot: true,
                        unitPrice: true,
                        itemTotal: true,
                        Service: { select: { id: true, code: true, name: true } },
                        BudgetItem: { select: { id: true, code: true, name: true } },
                    }
                }
            }
        });
        return NextResponse.json(quotes);
    } catch (err) {
        console.error('GET /api/quotes error', err);
        return NextResponse.json({ error: 'Erro ao listar orçamentos' }, { status: 500 });
    }
}

export async function POST(req: NextRequest) {
    try {
        const body = await req.json();

        const client = (body.client || '').trim();
        if (!client) return NextResponse.json({ error: 'client é obrigatório' }, { status: 400 });

        const clientNumber = body.clientNumber ?? null;
        const socialMediaRaw: unknown[] = Array.isArray(body.socialMedia) ? body.socialMedia as unknown[] : [];
        const socialMedia = socialMediaRaw.map((s: unknown) => (s === null || s === undefined) ? '' : String(s)).filter(Boolean);
        const totalPriceRaw = body.totalPrice ?? null;
        const totalPriceNumber = totalPriceRaw !== null ? Number(totalPriceRaw) : null;
        const discountRaw = body.discount ?? null;
        const discountNumber = discountRaw !== null ? Number(discountRaw) : null;
        const budgetType = body.budgetType ?? null;
        const servicesInputRaw: unknown[] = Array.isArray(body.services) ? body.services : [];
        const servicesInput: { key: string; quantity?: string; customDescription?: string; notes?: string; include?: boolean; unitPrice?: number | null; itemTotal?: number | null; budgetTemplateId?: string | null; templateSnapshot?: Prisma.InputJsonValue }[] = servicesInputRaw.map((s) => {
            if (typeof s === 'string') return { key: s };
            if (s && typeof s === 'object') {
                const rec = s as Record<string, unknown>;
                return {
                    key: String(rec['key'] ?? rec['value'] ?? rec['code'] ?? ''),
                    quantity: rec['quantity'] ? String(rec['quantity']) : undefined,
                    customDescription: rec['customDescription'] ? String(rec['customDescription']) : undefined,
                    notes: rec['notes'] ? String(rec['notes']) : undefined,
                    include: typeof rec['include'] === 'boolean' ? rec['include'] as boolean : undefined,
                    unitPrice: rec['unitPrice'] !== undefined ? (rec['unitPrice'] === null ? null : Number(rec['unitPrice'])) : undefined,
                    itemTotal: rec['itemTotal'] !== undefined ? (rec['itemTotal'] === null ? null : Number(rec['itemTotal'])) : undefined,
                    budgetTemplateId: rec['budgetTemplateId'] ? String(rec['budgetTemplateId']) : undefined,
                    templateSnapshot: rec['templateSnapshot'] ?? undefined,
                };
            }
            return { key: '' };
        });
        const budgetItemInputs: string[] = Array.isArray(body.budgetItemIds) ? body.budgetItemIds : [];

        const resolvedServiceIds: { id: string; key: string; quantity?: string; customDescription?: string; notes?: string; include?: boolean; unitPrice?: number | null; itemTotal?: number | null; budgetTemplateId?: string | null; templateSnapshot?: Prisma.InputJsonValue }[] = [];
        const unknownServiceLabels: { label: string; quantity?: string; customDescription?: string; notes?: string; include?: boolean; unitPrice?: number | null; itemTotal?: number | null; budgetTemplateId?: string | null; templateSnapshot?: Prisma.InputJsonValue }[] = [];
        if (servicesInput.length > 0) {
            const keys = servicesInput.map(s => s.key).filter(Boolean) as string[];
            const found = await prisma.service.findMany({ where: { OR: [{ id: { in: keys } }, { code: { in: keys } }] } }) as Array<Pick<Service, 'id' | 'code'>>;
            for (const inp of servicesInput) {
                const f = found.find(x => x.id === inp.key || x.code === inp.key);
                if (f) resolvedServiceIds.push({ id: f.id, key: inp.key, quantity: inp.quantity, customDescription: inp.customDescription, notes: inp.notes, include: inp.include, unitPrice: inp.unitPrice, itemTotal: inp.itemTotal, budgetTemplateId: inp.budgetTemplateId, templateSnapshot: inp.templateSnapshot });
                else unknownServiceLabels.push({ label: inp.key, quantity: inp.quantity, customDescription: inp.customDescription, notes: inp.notes, include: inp.include, unitPrice: inp.unitPrice, itemTotal: inp.itemTotal, budgetTemplateId: inp.budgetTemplateId, templateSnapshot: inp.templateSnapshot });
            }
        }

        const resolvedBudgetItemIds: string[] = [];
        const unknownBudgetLabels: string[] = [];
        if (budgetItemInputs.length > 0) {
            const found = await prisma.budgetItem.findMany({ where: { OR: [{ id: { in: budgetItemInputs } }, { code: { in: budgetItemInputs } }] } }) as Array<Pick<BudgetItem, 'id' | 'code'>>;
            for (const inp of budgetItemInputs) {
                const f = found.find(x => x.id === inp || x.code === inp);
                if (f) resolvedBudgetItemIds.push(f.id);
                else unknownBudgetLabels.push(inp);
            }
        }

        const result = await prisma.$transaction(async (tx) => {
            const quote = await tx.quote.create({ data: { client, clientPhoneNumber: clientNumber, socialMedia: socialMedia, ...(budgetType !== null ? { budgetType } : {}), ...(totalPriceNumber !== null ? { totalPrice: totalPriceNumber } : {}), ...(discountNumber !== null ? { discount: discountNumber } : {}) } });

            const itemsToCreate: Prisma.QuoteItemUncheckedCreateInput[] = [];
            for (const bid of resolvedBudgetItemIds) {
                const it: Prisma.QuoteItemUncheckedCreateInput = { quoteId: quote.id, budgetItemId: bid, budgetTemplateId: budgetType ?? null };
                itemsToCreate.push(it);
            }
            for (const sid of resolvedServiceIds) {
                const it: Prisma.QuoteItemUncheckedCreateInput = {
                    quoteId: quote.id,
                    serviceId: sid.id,
                    quantity: sid.quantity,
                    customDescription: sid.customDescription,
                    notes: sid.notes,
                    include: sid.include,
                    unitPrice: sid.unitPrice ?? undefined,
                    itemTotal: sid.itemTotal ?? undefined,
                    budgetTemplateId: sid.budgetTemplateId ?? null,
                    templateSnapshot: sid.templateSnapshot as Prisma.InputJsonValue | undefined,
                };
                itemsToCreate.push(it);
            }
            for (const label of unknownServiceLabels) {
                const it: Prisma.QuoteItemUncheckedCreateInput = {
                    quoteId: quote.id,
                    customLabel: label.label,
                    quantity: label.quantity,
                    customDescription: label.customDescription,
                    notes: label.notes,
                    include: label.include,
                    unitPrice: label.unitPrice ?? undefined,
                    itemTotal: label.itemTotal ?? undefined,
                    budgetTemplateId: label.budgetTemplateId ?? null,
                    templateSnapshot: label.templateSnapshot as Prisma.InputJsonValue | undefined,
                };
                itemsToCreate.push(it);
            }
            for (const label of unknownBudgetLabels) {
                const it: Prisma.QuoteItemUncheckedCreateInput = { quoteId: quote.id, customLabel: label };
                itemsToCreate.push(it);
            }

            if (itemsToCreate.length > 0) {
                const hasJson = itemsToCreate.some(i => i.templateSnapshot !== undefined);
                if (!hasJson) {
                    await tx.quoteItem.createMany({ data: itemsToCreate as unknown as Prisma.QuoteItemCreateManyInput[] });
                } else {
                    for (const it of itemsToCreate) {
                        await tx.quoteItem.create({ data: it as unknown as Prisma.QuoteItemCreateInput });
                    }
                }
            }

            return tx.quote.findUnique({ where: { id: quote.id }, include: { services: { include: { Service: true, BudgetItem: true } } } });
        });

        return NextResponse.json(result);
    } catch (err) {
        console.error('POST /api/quotes error', err);
        const e = err as { code?: string; meta?: unknown } | undefined;
        if (e?.code === 'P2003') {
            const meta = e.meta ?? null;
            return NextResponse.json({ error: 'Violação de chave estrangeira ao criar itens do orçamento', detail: meta }, { status: 400 });
        }
        return NextResponse.json({ error: 'Erro ao criar orçamento' }, { status: 500 });
    }
}
