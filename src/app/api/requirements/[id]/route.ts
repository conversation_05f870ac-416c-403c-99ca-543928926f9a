import prisma from "@/app/lib/prisma";
import { Content, MonthlyPlanning, WeeklyActivity } from "@prisma/client";
import { NextResponse } from "next/server";
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ message: 'Não autorizado' }, { status: 401 });
    }

    const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
    if (!requestingUser) {
      return NextResponse.json({ message: 'Usuário não encontrado' }, { status: 404 });
    }

    const allowedRoles = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];
    if (!allowedRoles.includes(requestingUser.role)) {
      return NextResponse.json({ message: '<PERSON><PERSON> negado' }, { status: 403 });
    }

    const url = new URL(req.url);
    const id = url.pathname.split('/').pop();

    if (!id) {
      return NextResponse.json({ message: "ID is required" }, { status: 400 });
    }

    const client = await prisma.client.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        instagramUsername: true,
      }
    });

    if (!client) {
      return NextResponse.json({ message: "Client not found" }, { status: 404 });
    }

    const plannings = await prisma.monthlyPlanning.findMany({
      where: { clientId: id },
      include: {
        activities: {
          include: {
            contents: true
          }
        }
      }
    });

    type ContentWithActivity = Content & { weeklyActivity: WeeklyActivity };

    const allContents: ContentWithActivity[] = [];
    plannings.forEach((planning: MonthlyPlanning & {
      activities: (WeeklyActivity & {
        contents: Content[]
      })[]
    }) => {
      planning.activities.forEach(activity => {
        if (activity.contents && activity.contents.length > 0) {
          allContents.push(...activity.contents.map(content => ({
            ...content,
            weeklyActivity: {
              id: activity.id,
              monthlyPlanningId: activity.monthlyPlanningId,
              week: activity.week,
              description: activity.description
            }
          })));
        }
      });
    });

    const sortedContents = allContents.sort((a, b) =>
      new Date(b.activityDate).getTime() - new Date(a.activityDate).getTime()
    );

    return NextResponse.json({
      client,
      contents: sortedContents
    }, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache'
      }
    });
  } catch (error) {
    console.error("Error fetching requirements:", error instanceof Error ? error.message : String(error));
    return NextResponse.json({ message: "Error fetching requirements" }, { status: 500 });
  }
}
