import { NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const ALLOWED_ROLES = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];

export async function GET(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: 'Sem sessão' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user?.email ?? undefined } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        if (!ALLOWED_ROLES.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const id = url.pathname.split('/').pop();
        if (!id) {
            return NextResponse.json({ error: 'ID do relatório é obrigatório' }, { status: 400 });
        }

        const report = await prisma.resultsReport.findUnique({
            where: { id },
            include: { results: true }
        });

        if (!report) {
            return NextResponse.json({ error: 'Relatório não encontrado' }, { status: 404 });
        }

        return NextResponse.json(report);
    } catch (error) {
        console.error('Erro ao buscar relatório:', error);
        return NextResponse.json({ error: 'Erro ao buscar relatório' }, { status: 500 });
    }
}

export async function PUT(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: 'Sem sessão' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user?.email ?? undefined } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        if (!ALLOWED_ROLES.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const id = url.pathname.split('/').pop();
        if (!id) {
            return NextResponse.json({ error: 'ID do relatório é obrigatório' }, { status: 400 });
        }

        const body = await req.json();
        const { results } = body;

        const existingReport = await prisma.resultsReport.findUnique({
            where: { id: id },
            include: { results: true }
        });

        if (!existingReport) {
            return NextResponse.json({ error: 'Relatório não encontrado' }, { status: 404 });
        }

        const resultId = existingReport.results[0]?.id;

        if (!resultId) {
            return NextResponse.json({ error: 'Resultado não encontrado' }, { status: 404 });
        }

        await prisma.result.update({
            where: { id: resultId },
            data: results[0]
        });

        const updatedReport = await prisma.resultsReport.update({
            where: { id: id },
            data: { updatedAt: new Date() },
            include: { results: true }
        });

        return NextResponse.json(updatedReport);
    } catch (error) {
        console.error('Erro ao atualizar relatório:', error);
        return NextResponse.json({ error: 'Erro ao atualizar relatório' }, { status: 500 });
    }
}