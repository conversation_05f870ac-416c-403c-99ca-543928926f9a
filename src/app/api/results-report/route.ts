import { NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { Prisma } from '@prisma/client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const ALLOWED_ROLES = ['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'];

export async function GET(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: 'Sem sessão' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user?.email ?? undefined } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        if (!ALLOWED_ROLES.includes(requestingUser.role)) {
            return NextResponse.json({ error: '<PERSON><PERSON> negado' }, { status: 403 });
        }

        const url = new URL(req.url);
        const clientId = url.searchParams.get('clientId') ?? undefined;
        const monthParam = url.searchParams.get('month');
        const yearParam = url.searchParams.get('year');

        const month = monthParam ? parseInt(monthParam, 10) : undefined;
        const year = yearParam ? parseInt(yearParam, 10) : undefined;

        const whereClause: Record<string, unknown> = {};
        if (clientId) whereClause.clientId = clientId;
        if (month !== undefined && !Number.isNaN(month)) whereClause.month = month;
        if (year !== undefined && !Number.isNaN(year)) whereClause.year = year;

        const reports = await prisma.resultsReport.findMany({
            where: whereClause as Prisma.ResultsReportWhereInput,
            include: { results: true },
            orderBy: { updatedAt: 'desc' }
        });

        return NextResponse.json(reports);
    } catch (error) {
        console.error('Erro ao listar relatórios:', error);
        return NextResponse.json({ error: 'Erro ao listar relatórios' }, { status: 500 });
    }
}

export async function POST(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ error: 'Sem sessão' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user?.email ?? undefined } });
        if (!requestingUser) {
            return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });
        }

        if (!ALLOWED_ROLES.includes(requestingUser.role)) {
            return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
        }

        const body = await req.json();
        const { clientId, month, year, results } = body;

        const clientExists = await prisma.client.findUnique({
            where: { id: clientId }
        });

        if (!clientExists) {
            return NextResponse.json({ error: 'Cliente não encontrado' }, { status: 404 });
        }

        const report = await prisma.resultsReport.create({
            data: {
                clientId,
                month,
                year,
                updatedAt: new Date(),
                results: {
                    create: results[0]
                }
            },
            include: {
                results: true
            }
        });

        return NextResponse.json(report, { status: 201 });
    } catch (error) {
        console.error('Erro ao criar relatório:', error);
        return NextResponse.json({ error: 'Erro ao criar relatório' }, { status: 500 });
    }
}
