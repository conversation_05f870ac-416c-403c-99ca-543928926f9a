import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

interface Period {
    start: Date;
    end: Date;
    hours: number;
}

interface DayData {
    periods: Period[];
    totalHours: number;
}

export async function GET() {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });

        const user = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!user) return NextResponse.json({ error: 'Usuário não encontrado' }, { status: 404 });

        const today = new Date();
        const todayString = today.toLocaleDateString('en-CA');

        const excusedDays = await prisma.excusedDay.findMany({ where: { userId: user.id, date: { lt: new Date(todayString + 'T00:00:00') } } });

        const records = await prisma.pointRecord.findMany({
            where: { userId: user.id, date: { lt: new Date(todayString + 'T00:00:00') } },
            orderBy: { date: 'asc' }
        });

        const completeRecords = records.filter(r => r.clockIn && r.clockOut && r.totalHours);

        const userDailyHours = completeRecords.reduce((acc: Record<string, DayData>, record) => {
            const dateKey = record.date.toISOString().split('T')[0];
            if (!acc[dateKey]) acc[dateKey] = { periods: [], totalHours: 0 };
            acc[dateKey].periods.push({ start: new Date(record.clockIn!), end: new Date(record.clockOut!), hours: record.totalHours ?? 0 });
            return acc;
        }, {});

        Object.keys(userDailyHours).forEach(dateKey => {
            const day = userDailyHours[dateKey];
            day.periods.sort((a, b) => a.start.getTime() - b.start.getTime());
            day.totalHours = day.periods.reduce((s, p) => s + p.hours, 0);
            day.totalHours = Math.min(day.totalHours, 24);
        });

        const weeklyData: Record<string, { totalHours: number; workDays: number }> = {};

        Object.entries(userDailyHours).forEach(([dateKey, dayData]) => {
            const date = new Date(dateKey + 'T12:00:00');
            const dayOfWeek = date.getDay();
            if (dayOfWeek === 0) return;
            const monday = new Date(date);
            monday.setDate(date.getDate() - dayOfWeek + 1);
            const weekKey = monday.toISOString().split('T')[0];
            if (!weeklyData[weekKey]) weeklyData[weekKey] = { totalHours: 0, workDays: 0 };
            weeklyData[weekKey].totalHours += dayData.totalHours;
            if (dayOfWeek >= 1 && dayOfWeek <= 5) weeklyData[weekKey].workDays += 1;
        });

        excusedDays.forEach(exc => {
            const dateKey = exc.date.toISOString().split('T')[0];
            const date = new Date(dateKey + 'T12:00:00');
            const dayOfWeek = date.getDay();
            if (dayOfWeek === 0) return;
            const monday = new Date(date);
            monday.setDate(date.getDate() - dayOfWeek + 1);
            const weekKey = monday.toISOString().split('T')[0];
            if (!weeklyData[weekKey]) weeklyData[weekKey] = { totalHours: 0, workDays: 0 };
            if (dayOfWeek >= 1 && dayOfWeek <= 5) {
                weeklyData[weekKey].totalHours += 8.8;
                weeklyData[weekKey].workDays += 1;
            }
        });

        let totalTimeBank = 0;
        Object.values(weeklyData).forEach(week => {
            const weeklyHours = week.totalHours;
            const workDays = week.workDays;
            const DAILY_EXPECTED_HOURS = 8.8;
            const expectedHours = workDays >= 5 ? 44 : workDays * DAILY_EXPECTED_HOURS;
            if (weeklyHours >= expectedHours) totalTimeBank += (weeklyHours - expectedHours);
            else totalTimeBank -= (expectedHours - weeklyHours);
        });

        return NextResponse.json({ userId: user.id, userName: user.name, userEmail: user.email, totalTimeBank });
    } catch (error) {
        console.error('Erro ao buscar banco de horas do usuário:', error);
        return NextResponse.json({ error: 'Erro interno' }, { status: 500 });
    }
}
