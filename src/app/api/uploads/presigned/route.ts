import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { getToken } from 'next-auth/jwt';

function sanitizeFilename(name: string) {
    return name.replace(/[^a-zA-Z0-9._-]/g, '_');
}

export async function POST(request: NextRequest) {
    try {
        const token = await getToken({ req: request as unknown as NextRequest, secret: process.env.NEXT_AUTH_SECRET });
        if (!token) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const body = await request.json();
        const filename = body?.filename as string | undefined;
        const contentType = (body?.contentType as string | undefined) ?? 'application/octet-stream';
        const folder = (body?.folder as string | undefined) ?? 'uploads';

        if (!filename) {
            return NextResponse.json({ error: 'filename is required' }, { status: 400 });
        }

        const userId = token?.email ?? token?.sub ?? 'anonymous';
        const safeName = sanitizeFilename(filename);
        const key = `${folder}/${userId}/${Date.now()}_${safeName}`;

        const bucket = process.env.S3_BUCKET;
        const region = process.env.AWS_REGION;

        if (!bucket || !region) {
            return NextResponse.json({ error: 'S3_BUCKET or AWS_REGION not configured' }, { status: 500 });
        }

        const client = new S3Client({
            region,
            credentials: {
                accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
                secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || ''
            }
        });

        const command = new PutObjectCommand({
            Bucket: bucket,
            Key: key,
            ContentType: contentType,
        });

        const url = await getSignedUrl(client, command, { expiresIn: 60 * 15 });

        return NextResponse.json({ url, key, bucket });
    } catch (error) {
        console.error('Erro ao gerar presigned URL:', error);
        return NextResponse.json({ error: 'internal_error' }, { status: 500 });
    }
}
