import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export async function GET() {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email || "" },
            select: { role: true, id: true },
        });

        const isAdmin = user?.role === "ADMIN" || user?.role === "DEVELOPER";

        const notifications = await prisma.notification.findMany({
            orderBy: {
                createdAt: 'desc'
            },
            take: 50
        });

        const contents = await prisma.content.findMany({
            where: {
                archived: false,
                ...(isAdmin ? {} : { assignedToId: user?.id })
            },
            include: {
                assignedTo: {
                    select: {
                        name: true,
                        email: true
                    }
                },
                weeklyActivity: {
                    include: {
                        monthlyPlanning: {
                            include: {
                                client: {
                                    select: {
                                        name: true
                                    }
                                }
                            }
                        }
                    }
                }
            },
            orderBy: {
                updatedAt: 'desc'
            },
            take: 30
        });

        const demands = await prisma.generalDemand.findMany({
            where: {
                archived: false,
                ...(isAdmin ? {} : { assignedToId: user?.id })
            },
            include: {
                assignedTo: {
                    select: {
                        name: true,
                        email: true
                    }
                },
                client: {
                    select: {
                        name: true
                    }
                },
                looseClient: {
                    select: {
                        name: true
                    }
                }
            },
            orderBy: {
                updatedAt: 'desc'
            },
            take: 30
        });

        const activitiesFromNotifications = notifications.map(notification => ({
            id: notification.id,
            type: 'notification',
            title: notification.content,
            description: `Tipo: ${notification.type}`,
            timestamp: notification.createdAt,
            relatedId: notification.entityId,
            relatedType: notification.entityType,
            importance: notification.importance || 'normal',
            isRead: notification.isRead,
            reference: notification.reference
        }));

        const activitiesFromContents = contents.map(content => ({
            id: content.id,
            type: 'content',
            title: `Conteúdo: ${content.contentType} (${content.channel || 'Feed'})`,
            description: `Cliente: ${content.weeklyActivity.monthlyPlanning.client.name} | Status: ${content.status}`,
            timestamp: content.updatedAt,
            relatedId: content.id,
            relatedType: 'content',
            importance: content.priority || 'normal',
            assignedTo: content.assignedTo?.name,
            dueDate: content.activityDate
        }));

        const activitiesFromDemands = demands.map(demand => ({
            id: demand.id,
            type: 'demand',
            title: demand.title,
            description: `Cliente: ${demand.client?.name || demand.looseClient?.name || 'Sem cliente'} | Status: ${demand.status}`,
            timestamp: demand.updatedAt,
            relatedId: demand.id,
            relatedType: 'demand',
            importance: demand.priority || 'normal',
            assignedTo: demand.assignedTo?.name,
            dueDate: demand.dueDate
        }));

        const allActivities = [
            ...activitiesFromNotifications,
            ...activitiesFromContents,
            ...activitiesFromDemands
        ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

        return NextResponse.json(allActivities, { status: 200 });
    } catch (error) {
        console.error("Erro ao buscar atividades dos usuários:", error);
        
        if (error instanceof Error) {
            return NextResponse.json(
                { error: error.message },
                { status: 400 }
            );
        }
        
        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}
