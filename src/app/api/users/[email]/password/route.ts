import { NextRequest, NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { compare, hash } from "bcryptjs";

export async function PUT(
    req: NextRequest,
    context: { params: Promise<{ email: string }> }
) {
    try {
        const { email } = await context.params;
        const { currentPassword, newPassword } = await req.json();

        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: "Não autenticado" },
                { status: 401 }
            );
        }

        const currentUser = await prisma.user.findUnique({
            where: { email: session.user.email || "" },
            select: { email: true, role: true }
        });

        if (!currentUser) {
            return NextResponse.json(
                { error: "Usuário não encontrado" },
                { status: 404 }
            );
        }

        const isSelfUpdate = currentUser.email === email;
        const isAdmin = currentUser.role === "ADMIN" || currentUser.role === "DEVELOPER";

        if (!isSelfUpdate && !isAdmin) {
            return NextResponse.json(
                { error: "Sem permissão para alterar a senha de outro usuário" },
                { status: 403 }
            );
        }

        const userToUpdate = await prisma.user.findUnique({
            where: { email },
            select: {
                id: true,
                hashedPassword: true,
                accounts: {
                    select: {
                        provider: true
                    }
                }
            }
        });

        if (!userToUpdate) {
            return NextResponse.json(
                { error: "Usuário não encontrado" },
                { status: 404 }
            );
        }

        const isGoogleAccount = userToUpdate.accounts.some(account => account.provider === "google");
        const hasExistingPassword = !!userToUpdate.hashedPassword;

        if (hasExistingPassword && !isGoogleAccount && !currentPassword) {
            return NextResponse.json(
                { error: "Senha atual é obrigatória" },
                { status: 400 }
            );
        }

        if (hasExistingPassword && currentPassword) {
            const isPasswordValid = await compare(currentPassword, userToUpdate.hashedPassword!);
            if (!isPasswordValid) {
                return NextResponse.json(
                    { error: "Senha atual incorreta" },
                    { status: 400 }
                );
            }
        }

        if (!newPassword || newPassword.length < 6) {
            return NextResponse.json(
                { error: "A nova senha deve ter pelo menos 6 caracteres" },
                { status: 400 }
            );
        }

        const hashedPassword = await hash(newPassword, 10);

        await prisma.user.update({
            where: { email },
            data: { hashedPassword }
        });

        return NextResponse.json(
            { message: isGoogleAccount && !hasExistingPassword ? "Senha cadastrada com sucesso" : "Senha alterada com sucesso" },
            { status: 200 }
        );
    } catch (error) {
        console.error("Erro ao alterar senha:", error);
        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}
