import { NextRequest, NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export async function GET(req: Request): Promise<Response> {
    const url = new URL(req.url);

    const email = url.pathname.split("/").pop();

    if (!email) {
        return NextResponse.json(
            { error: "Email is required" },
            { status: 400 }
        );
    }

    try {
        const user = await prisma.user.findUnique({
            where: { email },
            select: {
                id: true,
                name: true,
                email: true,
                role: true,
                accounts: true,
                accessLevel: true,
                client: {
                    select: {
                        id: true,
                        name: true,
                    }
                },
                createdAt: true,
                hashedPassword: true,
            },
        });

        if (!user) {
            return NextResponse.json(
                { error: "User not found" },
                { status: 404 }
            );
        }

    const { hashedPassword, ...userWithoutHash } = user as { hashedPassword?: string } & Record<string, unknown>;
    return NextResponse.json({ ...userWithoutHash, hasPassword: !!hashedPassword });
    } catch (error) {
        console.error("Error fetching user:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

export async function PUT(
    req: Request,
    context: { params: Promise<{ email: string }> }
) {
    const { email } = await context.params;

    try {
        const { name } = await req.json();

        const updatedUser = await prisma.user.update({
            where: { email },
            data: { name },
        });

        return NextResponse.json(updatedUser);
    } catch (error) {
        console.error("Error updating user:", error);
        return NextResponse.json({ message: "Failed to update user" }, { status: 500 });
    }
}

export async function DELETE(
    request: NextRequest,
    context: { params: Promise<{ email: string }> }
) {
    try {
        const { email } = await context.params;

        const session = await getServerSession(authOptions);

        if (!session?.user) {
            console.error('DELETE user - Authentication error: No session found');
            return NextResponse.json(
                { error: 'Não autenticado' },
                { status: 401 }
            );
        }

        const currentUser = await prisma.user.findUnique({
            where: { email: session.user.email || '' },
            select: { email: true, role: true }
        });

        if (!currentUser) {
            console.error('DELETE user - Authentication error: User not found in database');
            return NextResponse.json(
                { error: 'Usuário não encontrado' },
                { status: 401 }
            );
        }

        const isSelfDelete = currentUser.email === email;
        const isAdmin = currentUser.role === 'ADMIN' || currentUser.role === 'DEVELOPER';

        if (!isSelfDelete && !isAdmin) {
            console.error('DELETE user - Permission error: User does not have permission');
            return NextResponse.json(
                { error: 'Sem permissão para executar esta ação' },
                { status: 403 }
            );
        }

        const userToDelete = await prisma.user.findUnique({
            where: { email }
        });

        if (!userToDelete) {
            return NextResponse.json(
                { error: 'Usuário não encontrado' },
                { status: 404 }
            );
        }

        await prisma.user.delete({
            where: { email },
        });

        return NextResponse.json({ message: "Conta excluída com sucesso!" }, { status: 200 });
    } catch (error) {
        console.error('Erro ao deletar usuário:', error);
        return NextResponse.json(
            { error: 'Falha ao deletar o usuário' },
            { status: 500 }
        );
    }
}
