import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";

export async function POST(req: Request) {
    try {
        const body = await req.json();
        const { userId, clientId } = body as { userId?: string; clientId?: string };

        if (!userId) {
            return NextResponse.json({ message: 'userId é obrigatório' }, { status: 400 });
        }

        const user = await prisma.user.findUnique({ where: { id: userId } });
        if (!user) {
            return NextResponse.json({ message: 'Usuário não encontrado' }, { status: 404 });
        }

        if (clientId) {
            const client = await prisma.client.findUnique({ where: { id: clientId } });
            if (!client) {
                return NextResponse.json({ message: 'Cliente não encontrado' }, { status: 404 });
            }

            if (client.userId && client.userId !== userId) {
                return NextResponse.json({ message: 'Cliente já está atribuído a outro usuário' }, { status: 409 });
            }

            const updatedClient = await prisma.client.update({
                where: { id: clientId },
                data: { userId },
            });

            return NextResponse.json({ success: true, client: updatedClient }, { status: 200 });
        } else {
            await prisma.client.updateMany({
                where: { userId },
                data: { userId: null },
            });

            return NextResponse.json({ success: true, message: 'Cliente desatribuído' }, { status: 200 });
        }

    } catch (error) {
        console.error('Erro ao atribuir cliente:', error);
        return NextResponse.json({ message: 'Erro interno' }, { status: 500 });
    }
}
