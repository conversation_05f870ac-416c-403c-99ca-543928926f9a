import { NextRequest, NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import bcrypt from 'bcryptjs';

export async function POST(req: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        if (!session?.user?.email) {
            return NextResponse.json({ message: 'Não autorizado' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user.email } });
        if (!requestingUser) {
            return NextResponse.json({ message: 'Usuário solicitante não encontrado' }, { status: 404 });
        }

        if (!["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"].includes(requestingUser.role)) {
            return NextResponse.json({ message: '<PERSON><PERSON> negado' }, { status: 403 });
        }

        const body = await req.json();
    const { email, clientId, name } = body as { email?: string; clientId?: string; name?: string };

        if (!email) {
            return NextResponse.json({ message: 'E-mail é obrigatório' }, { status: 400 });
        }

        const existing = await prisma.user.findUnique({ where: { email } });
        if (existing) {
            return NextResponse.json({ message: 'Usuário já existe' }, { status: 409 });
        }

        // Create new user with default role CLIENT and accessLevel VIEWER
        // Gerar senha temporária
        function generateTempPassword(length = 10) {
            const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz23456789!@#$%';
            let pass = '';
            for (let i = 0; i < length; i++) {
                pass += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return pass;
        }

        const tempPassword = generateTempPassword();
        const hashedPassword = await bcrypt.hash(tempPassword, 10);

        const newUser = await prisma.user.create({
            data: {
                email,
                name: name || undefined,
                hashedPassword,
                role: 'CLIENT',
                accessLevel: 'VIEWER',
                client: clientId ? { connect: { id: clientId } } : undefined,
            },
        });

    // Não enviamos e-mail automaticamente. Retornamos a senha temporária
    // para que o usuário administrador copie/mostre ao novo usuário.
    // Remover hashedPassword do objeto retornado
    const userObj = { ...newUser } as Record<string, unknown>;
    if ('hashedPassword' in userObj) delete userObj.hashedPassword;

    return NextResponse.json({ user: userObj, tempPassword }, { status: 201 });
    } catch (error) {
        console.error('Erro ao criar usuário:', error);
        return NextResponse.json({ message: 'Erro interno' }, { status: 500 });
    }
}
