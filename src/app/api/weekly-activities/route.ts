import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const contentId = url.searchParams.get('contentId');
    const planningId = url.searchParams.get('planningId');

    if (!contentId && !planningId) {
      return NextResponse.json(
        { message: "É necessário fornecer contentId ou planningId" },
        { status: 400 }
      );
    }

    let monthlyPlanningId: string | null = null;

    if (contentId) {
      const content = await prisma.content.findUnique({
        where: { id: contentId },
        include: {
          weeklyActivity: true
        }
      });

      if (!content) {
        return NextResponse.json(
          { message: "Conteúdo não encontrado" },
          { status: 404 }
        );
      }

      monthlyPlanningId = content.weeklyActivity.monthlyPlanningId;
    } else if (planningId) {
      monthlyPlanningId = planningId;
    }

    const weeklyActivities = await prisma.weeklyActivity.findMany({
      where: {
        monthlyPlanningId: monthlyPlanningId!
      },
      orderBy: {
        week: 'asc'
      }
    });

    return NextResponse.json(weeklyActivities);
  } catch (error) {
    console.error("Erro ao buscar atividades semanais:", error);
    return NextResponse.json(
      { message: "Erro interno do servidor" },
      { status: 500 }
    );
  }
}
