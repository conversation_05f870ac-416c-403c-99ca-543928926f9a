export const AboutPlanning = () => {
    return (
        <div className="mt-4 space-y-4 text-sm">
            <section>
                <h3 className="font-semibold text-gray-900 dark:text-gray-200">Objetivo</h3>
                <p className="text-gray-700 dark:text-gray-200 leading-relaxed">
                    Estruturar o planejamento de conteúdo com clareza e organização, garantindo consistência nas
                    publicações e alinhamento com os objetivos estratégicos.
                </p>
            </section>
            <section className="space-y-4">
                {[
                    {
                        title: "Planejamento de Datas e Temas",
                        items: [
                            "Criar um calendário de conteúdo, definindo datas e temas para cada publicação.",
                            "Planejar campanhas específicas, como Black Friday, lançamentos ou promoções sazonais.",
                        ],
                    },
                    {
                        title: "Diferentes Formatos e Canais",
                        items: [
                            "Diversificar os formatos de conteúdo (posts, stories, vídeos curtos, etc.) para ampliar o alcance e engajamento.",
                            "Definir o propósito de cada formato e o canal ideal para sua divulgação.",
                        ],
                    },
                    {
                        title: "Definição de KPIs para o Mês",
                        items: [
                            "Selecionar indicadores-chave de performance (KPIs) que serão acompanhados e analisados ao longo do mês.",
                        ],
                    },
                    {
                        title: "Materiais Necessários",
                        items: [
                            "Preparar materiais complementares, como banners, materiais para WhatsApp, PDFs, catálogos.",
                            "Garantir a criação de materiais específicos para tráfego pago.",
                        ],
                    },
                ].map((section, index) => (
                    <div key={index} className="border border-zinc-300 dark:border-zinc-800 rounded-lg p-4">
                        <h4 className="text-md font-semibold text-gray-900 dark:text-gray-400 mb-2">{section.title}</h4>
                        <ul className="space-y-2">
                            {section.items.map((item, itemIndex) => (
                                <li key={itemIndex} className="flex items-start">
                                    <span className="inline-block w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                                    <span className="text-gray-700 dark:text-gray-200 text-sm">{item}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                ))}
            </section>
        </div>
    )
};