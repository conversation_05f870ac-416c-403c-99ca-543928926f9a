"use client"

import { Separator } from "@/app/components/ui/separator";

export function AboutRequirements() {
  return (
    <div className="space-y-6 mt-4">
      <div>
        <h3 className="text-lg font-medium">Sobre esta etapa</h3>
        <p className="text-sm text-muted-foreground mt-1">
          A etapa de demandas é onde você gerencia e acompanha o status de todos os conteúdos planejados para o cliente.
        </p>
      </div>
      
      <Separator />
      
      <div>
        <h3 className="text-lg font-medium">Fluxo de aprovação</h3>
        <p className="text-sm text-muted-foreground mt-1">
          O fluxo de aprovação de conteúdo segue estas etapas:
        </p>
        
        <div className="mt-4 space-y-4">
          <div className="border-l-4 border-gray-200 dark:border-gray-700 pl-4">
            <h4 className="font-medium">1. Pendente</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Conteúdo foi planejado mas ainda não começou a ser produzido.
            </p>
          </div>
          
          <div className="border-l-4 border-yellow-200 dark:border-yellow-900 pl-4">
            <h4 className="font-medium">2. Em andamento</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Conteúdo está sendo produzido pela equipe.
            </p>
          </div>
          
          <div className="border-l-4 border-orange-200 dark:border-orange-900 pl-4">
            <h4 className="font-medium">3. Pendente captação</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Conteúdo está aguardando captação de material.
            </p>
          </div>
          
          <div className="border-l-4 border-blue-200 dark:border-blue-900 pl-4">
            <h4 className="font-medium">4. Captado</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Material foi captado e está pronto para ser estruturado.
            </p>
          </div>
          
          <div className="border-l-4 border-violet-300 dark:border-violet-700 pl-4">
            <h4 className="font-medium">5. Feed estruturado</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Conteúdo foi estruturado no feed e está aguardando aprovação final.
            </p>
          </div>
          
          <div className="border-l-4 border-green-200 dark:border-green-900 pl-4">
            <h4 className="font-medium">6. Concluído</h4>
            <p className="text-sm text-muted-foreground mt-1">
              Conteúdo foi aprovado e está pronto para publicação.
            </p>
          </div>
        </div>
      </div>
      
      <Separator />
      
      <div>
        <h3 className="text-lg font-medium">Atribuição de tarefas</h3>
        <p className="text-sm text-muted-foreground mt-1">
          Você pode atribuir cada conteúdo a um membro específico da equipe para facilitar o acompanhamento e a responsabilidade.
        </p>
        <p className="text-sm text-muted-foreground mt-2">
          Para atribuir uma tarefa, clique no ícone de usuário no card do conteúdo e selecione o membro da equipe responsável.
        </p>
      </div>
      
      <Separator />
      
      <div>
        <h3 className="text-lg font-medium">Dicas</h3>
        <ul className="mt-2 space-y-2 text-sm text-muted-foreground list-disc pl-5">
          <li>Use os filtros para encontrar rapidamente conteúdos específicos</li>
          <li>Atualize regularmente o status dos conteúdos para manter a equipe informada</li>
          <li>Atribua responsáveis para cada conteúdo para melhorar a organização</li>
          <li>Verifique regularmente os conteúdos pendentes para evitar atrasos</li>
        </ul>
      </div>
    </div>
  );
}
