/* eslint-disable @next/next/no-img-element */
"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import Image from "next/image";
import { Button } from "@/app/components/ui/button";
import { Label } from "@/app/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/app/components/ui/select";
import { toast } from "sonner";
import { Ellipsis, Plus, Trash2, Grid3X3, List, Eye, ImageUp, Pointer, ChevronLeft, ChevronRight } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/app/components/ui/dialog";
import { Card, CardContent, CardFooter } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs"
import S3UploadButton from '@/app/components/S3UploadButton';

interface AddFeedUrlModalProps {
  contentId: string
  currentUrl?: string | string[]
  currentUrlTypes?: string[]
  currentMediaTypes?: string[]
  currentUrlThumbnails?: string[]
  currentUrlFolder?: string
  activityDate?: string
  onUrlUpdated: (
    contentId: string,
    newUrls: string[],
    newUrlTypes: string[],
    newMediaTypes?: string[],
    newUrlFolder?: string,
    newUrlThumbnails?: string[]
  ) => void
  type?: "content" | "general"
}

interface UrlItem {
  id: string
  url: string
  type: "feed" | "story"
  mediaType: "foto" | "video"
  thumbnailUrl?: string
}

const extractGoogleDriveId = (url: string): string | null => {
  if (!url) return null
  try {
    const u = new URL(url)
    const host = u.hostname.toLowerCase()
    if (!host.includes('drive.google.com') && !host.includes('docs.google.com') && !host.includes('googleusercontent.com')) {
      return null
    }
    const match1 = url.match(/\/d\/([^/]+)/)
    if (match1) return match1[1]
    const match2 = url.match(/id=([^&]+)/)
    if (match2) return match2[1]
    return null
  } catch {
    return null
  }
}

function extractS3KeyFromUrl(url?: string) {
  if (!url) return null
  try {
    const publicBase = process.env.NEXT_PUBLIC_S3_BASE_URL
    if (publicBase && url.startsWith(publicBase.replace(/\/$/, ''))) {
      return url.slice(publicBase.replace(/\/$/, '').length + 1)
    }
    const m = url.match(/^https?:\/\/[a-z0-9.-]+\.s3(?:-[a-z0-9-]+)?\.amazonaws\.com\/(.+)/i)
    if (m) return decodeURIComponent(m[1])
    return null
  } catch {
    return null
  }
}

export const AddFeedUrlModal = ({
  contentId,
  currentUrl,
  currentUrlTypes,
  currentMediaTypes,
  currentUrlThumbnails,
  currentUrlFolder,
  activityDate,
  onUrlUpdated,
  type = "content",
}: AddFeedUrlModalProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [urls, setUrls] = useState<UrlItem[]>([])
  const [urlFolder, setUrlFolder] = useState<string | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [activeTab, setActiveTab] = useState("all")

  const feedCount = urls.filter((u) => u.type === "feed").length
  const storyCount = urls.filter((u) => u.type === "story").length
  const [presignedMap, setPresignedMap] = useState<Record<string, string>>({});
  const fetchingRef = useRef<Record<string, boolean>>({});
  const [fullscreenOpen, setFullscreenOpen] = useState(false)
  const [fullscreenGallery, setFullscreenGallery] = useState<string[]>([])
  const [fullscreenIndex, setFullscreenIndex] = useState<number>(0)

  useEffect(() => {
    if (!isOpen) return;

    const keysToFetch = new Set<string>();

    urls.forEach((item) => {
      const candidates: (string | undefined)[] = [];
      if (item.url) candidates.push(item.url);
      if (item.thumbnailUrl) candidates.push(item.thumbnailUrl);
      if (item.mediaType === 'video' && item.thumbnailUrl) candidates.push(item.thumbnailUrl);

      candidates.forEach((src) => {
        if (!src) return;
        const driveId = extractGoogleDriveId(src);
        if (driveId) return;
        const keyFromUrl = extractS3KeyFromUrl(src);
        if (keyFromUrl) {
          keysToFetch.add(keyFromUrl);
        } else if (!/^(https?:)?\/\//i.test(src) && src.trim() !== '') {
          keysToFetch.add(src);
        }
      });
    });

    keysToFetch.forEach((key) => {
      if (presignedMap[key] || fetchingRef.current[key]) return;

      const existingPresigned = urls
        .flatMap((u) => [u.url, u.thumbnailUrl])
        .find((s) => s && extractS3KeyFromUrl(s) === key && /X-Amz-/.test(s));
      if (existingPresigned) {
        setPresignedMap((prev) => ({ ...prev, [key]: existingPresigned as string }));
        return;
      }

      fetchingRef.current[key] = true;
      fetch('/api/uploads/presigned-get', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ key }),
      })
        .then((res) => {
          if (!res.ok) throw new Error('not ok')
          return res.json()
        })
        .then((data) => {
          if (data?.url) setPresignedMap((prev) => ({ ...prev, [key]: data.url }))
        })
        .catch(() => { })
        .finally(() => {
          fetchingRef.current[key] = false;
        })
    })
  }, [isOpen, urls, presignedMap]);

  useEffect(() => {
    const checkForUnsavedChanges = () => {
      if (!hasInitialized) return false
      const currentUrls = urls.map((item) => item.url)
      const currentTypes = urls.map((item) => item.type)
      const currentMediaTypes = urls.map((item) => item.mediaType)
      const originalUrls = Array.isArray(currentUrl) ? currentUrl : currentUrl ? [currentUrl] : []
      const originalTypes = currentUrlTypes || []
      const originalMediaTypes = currentMediaTypes || Array(originalUrls.length).fill("foto")
      const urlsChanged = JSON.stringify(currentUrls) !== JSON.stringify(originalUrls)
      const typesChanged = JSON.stringify(currentTypes) !== JSON.stringify(originalTypes)
      const mediaTypesChanged = JSON.stringify(currentMediaTypes) !== JSON.stringify(originalMediaTypes)
      return urlsChanged || typesChanged || mediaTypesChanged
    }
    setHasUnsavedChanges(checkForUnsavedChanges())
  }, [urls, hasInitialized, currentUrl, currentUrlTypes, currentMediaTypes])

  const handleCloseModal = () => {
    if (hasUnsavedChanges) {
      if (confirm("Você tem alterações não salvas. Deseja realmente fechar sem salvar?")) {
        setIsOpen(false)
      }
    } else {
      setIsOpen(false)
    }
  }

  const closeFullscreen = () => {
    setFullscreenOpen(false)
    setFullscreenGallery([])
    setFullscreenIndex(0)
  }

  const buildDisplaySource = (item: UrlItem, preferredQuality: 'low' | 'medium' = 'medium') => {
    const source = item.mediaType === 'video' && item.thumbnailUrl ? item.thumbnailUrl : item.url || ''
    const driveId = extractGoogleDriveId(source)
    if (driveId) return `/api/drive-proxy?id=${driveId}&quality=${preferredQuality}&size=medium`
    const s3Key = extractS3KeyFromUrl(source)
    if (s3Key && presignedMap[s3Key]) return presignedMap[s3Key]
    if (/^(https?:)?\/\//i.test(source) || source.startsWith('data:')) return source
    if (presignedMap[source]) return presignedMap[source]
    return source
  }

  const openFullscreenFromList = (list: UrlItem[], index: number) => {
    const gallery = list.map((it) => buildDisplaySource(it, 'medium')).filter(Boolean) as string[]
    if (!gallery.length) return
    const idx = Math.min(Math.max(0, index), gallery.length - 1)
    setFullscreenGallery(gallery)
    setFullscreenIndex(idx)
    setFullscreenOpen(true)
  }

  const nextFullscreen = useCallback(() => {
    setFullscreenIndex((i) => (fullscreenGallery.length ? (i + 1) % fullscreenGallery.length : 0))
  }, [fullscreenGallery.length])

  const prevFullscreen = useCallback(() => {
    setFullscreenIndex((i) => (fullscreenGallery.length ? (i - 1 + fullscreenGallery.length) % fullscreenGallery.length : 0))
  }, [fullscreenGallery.length])

  useEffect(() => {
    if (!fullscreenOpen) return
    const handler = (e: KeyboardEvent) => {
      if (e.key === 'Escape') return closeFullscreen()
      if (e.key === 'ArrowRight') return nextFullscreen()
      if (e.key === 'ArrowLeft') return prevFullscreen()
    }
    window.addEventListener('keydown', handler)
    return () => window.removeEventListener('keydown', handler)
  }, [fullscreenOpen, nextFullscreen, prevFullscreen])

  useEffect(() => {
    if (isOpen && !hasInitialized) {
      if (currentUrl) {
        if (Array.isArray(currentUrl)) {
          const urlItems = currentUrl.map((url, index) => ({
            id: `url-${index}`,
            url,
            type: (currentUrlTypes?.[index] === "story" ? "story" : "feed") as "feed" | "story",
            mediaType: (currentMediaTypes?.[index] === "video" ? "video" : "foto") as "foto" | "video",
            thumbnailUrl: currentUrlThumbnails?.[index] || undefined,
          }))
          setUrls(urlItems)
        } else {
          setUrls([
            {
              id: "url-0",
              url: currentUrl,
              type: currentUrlTypes?.[0] === "story" ? "story" : "feed",
              mediaType: currentMediaTypes?.[0] === "video" ? "video" : "foto",
              thumbnailUrl: currentUrlThumbnails?.[0] || undefined,
            },
          ])
        }
      } else {
        setUrls([{ id: "url-0", url: "", type: "feed", mediaType: "foto", thumbnailUrl: undefined }])
      }
      setUrlFolder(currentUrlFolder || null)
      setHasInitialized(true)
    }
  }, [isOpen, currentUrl, currentUrlTypes, currentMediaTypes, currentUrlThumbnails, currentUrlFolder, hasInitialized])

  useEffect(() => {
    if (!isOpen) {
      setHasInitialized(false)
    }
  }, [isOpen])

  const handleOpenDialog = () => {
    setIsOpen(true)
  }

  const handleUpdateUrl = async () => {
    if (!contentId) return
    setIsUpdating(true)
    try {
      const urlsToValidate = urls.filter((item) => item.url.trim() !== "")
      for (const urlItem of urlsToValidate) {
        const val = urlItem.url.trim()
        const isHttp = /^((http|https):\/\/|data:)/.test(val)
        const isDrive = !!extractGoogleDriveId(val)
        const isKeyLike = /^[^\s]+$/.test(val)
        if (!isHttp && !isDrive && !isKeyLike) {
          toast.error("Por favor, insira uma URL pública válida (http(s)://...), um link do Drive ou use o upload")
          setIsUpdating(false)
          return
        }

        if (urlItem.thumbnailUrl && urlItem.thumbnailUrl.trim() !== '') {
          const t = urlItem.thumbnailUrl.trim()
          const isHttpT = /^((http|https):\/\/|data:)/.test(t)
          const isDriveT = !!extractGoogleDriveId(t)
          const isKeyLikeT = /^[^\s]+$/.test(t)
          if (!isHttpT && !isDriveT && !isKeyLikeT) {
            toast.error("Por favor, insira uma URL de miniatura válida (http(s)://...), um link do Drive ou use o upload")
            setIsUpdating(false)
            return
          }
        }
      }

      const apiUrl =
        type === "general"
          ? `/api/general-demands/${contentId}/url-structuring`
          : `/api/contents/${contentId}/url-structuring`

      const urlsToSave = urlsToValidate.map((item) => item.url)
      const urlsWithTypes = urlsToValidate.map((item) => ({
        url: item.url,
        type: item.type,
        mediaType: item.mediaType,
        thumbnailUrl: item.thumbnailUrl,
      }))

      const response = await fetch(apiUrl, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          urlStructuringFeed: urlsToSave,
          urlsWithTypes: urlsWithTypes,
          urlFolder: urlFolder,
        }),
      })

      if (!response.ok) {
        throw new Error(`Erro ao atualizar URLs: ${response.status}`)
      }

      const urlTypesToSave = urlsToValidate.map((item) => item.type)
      const mediaTypesToSave = urlsToValidate.map((item) => item.mediaType)
      const thumbnailsToSave = urlsToValidate.map((item) => item.thumbnailUrl || '')

      toast.success("Salvo com sucesso")
      onUrlUpdated(contentId, urlsToSave, urlTypesToSave, mediaTypesToSave, urlFolder || undefined, thumbnailsToSave)
      setHasUnsavedChanges(false)
      setIsOpen(false)
    } catch (error) {
      console.error("Erro ao atualizar URLs:", error)
      toast.error("Erro ao atualizar URLs")
    } finally {
      setIsUpdating(false)
    }
  }

  const handleUrlChange = (index: number, newUrl: string) => {
    const newUrls = [...urls]
    newUrls[index].url = newUrl
    setUrls(newUrls)
  }

  const handleTypeChange = (index: number, newType: "feed" | "story") => {
    const newUrls = [...urls]
    newUrls[index].type = newType
    setUrls(newUrls)
  }

  const handleMediaTypeChange = (index: number, newMediaType: "foto" | "video") => {
    const newUrls = [...urls]
    newUrls[index].mediaType = newMediaType
    if (newMediaType === "foto") {
      newUrls[index].thumbnailUrl = undefined
    }
    setUrls(newUrls)
  }

  const handleThumbnailUrlChange = (index: number, newThumbnailUrl: string) => {
    const newUrls = [...urls]
    newUrls[index].thumbnailUrl = newThumbnailUrl
    setUrls(newUrls)
  }

  const addNewUrl = () => {
    const newId = `url-${Date.now()}`
    setUrls([...urls, { id: newId, url: "", type: "feed", mediaType: "foto", thumbnailUrl: undefined }])
  }

  const removeUrl = (index: number) => {
    const newUrls = urls.filter((_, i) => i !== index)
    setUrls(newUrls)
  }

  const validUrls = urls.filter((item) => item.url.trim() !== "")

  return (
    <>
      <Button variant="outline" size="icon" onClick={handleOpenDialog} title="Gerenciar criativos">
        <ImageUp size={14} />
      </Button>

      <Dialog open={isOpen} onOpenChange={handleCloseModal}>
        <DialogContent className="max-w-7xl w-[95vw] max-h-[95vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex flex-wrap gap-2 items-center justify-between">
              <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2">
                <div className="flex flex-col items-start gap-1">
                  <h5>
                    Visualizar ou editar criativos
                  </h5>
                  {activityDate && <span className="text-xs text-muted-foreground">
                    {new Date(activityDate).toLocaleDateString('pt-BR')}
                  </span>}
                </div>
                {hasUnsavedChanges && (
                  <Badge
                    variant="secondary"
                    className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                  >
                    Não salvo
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2 mt-6">
                <Badge variant="outline">{feedCount} Feed</Badge>
                <Badge variant="outline">{storyCount} Story</Badge>
                <Badge variant="outline">{validUrls.length} Total</Badge>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6 overflow-hidden">
            <div className="space-y-4 overflow-y-auto pr-2">
              <Card>
                <CardContent className="p-4">
                  <Label className="text-sm font-medium">Criativos</Label>

                  <p className="text-xs text-muted-foreground mb-4">
                    Configure o formato e tipo de mídia.
                  </p>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {urls.map((urlItem, index) => (
                      <Card key={urlItem.id} className="rounded-md">
                        <CardContent className="p-3">
                          <div className="flex flex-col xs:flex-row items-start gap-3">
                            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex-shrink-0 overflow-hidden">
                              {(() => {
                                const source = urlItem.mediaType === 'video' && urlItem.thumbnailUrl ? urlItem.thumbnailUrl : urlItem.url || ''
                                const driveId = extractGoogleDriveId(source)
                                if (!source) return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                if (driveId) {
                                  const src = `/api/drive-proxy?id=${driveId}&quality=low&size=small`
                                  return (
                                    <div className="w-full h-full relative">
                                      <Image
                                        src={src}
                                        alt={`Preview ${index + 1}`}
                                        width={64}
                                        height={64}
                                        className="w-full h-full object-cover cursor-pointer"
                                        onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))}
                                      />
                                      <div className="absolute inset-0 flex items-end justify-end p-1 pointer-events-none">
                                        <Pointer size={14} className="text-white bg-black/40 rounded p-0.5" />
                                      </div>
                                    </div>
                                  )
                                }

                                const s3KeyFromUrl = extractS3KeyFromUrl(source)
                                if (s3KeyFromUrl) {
                                  const cached = presignedMap[s3KeyFromUrl]
                                  if (cached) {
                                    return (
                                      <div className="w-full h-full relative">
                                        <img src={cached} alt={`Preview ${index + 1}`} width={64} height={64} className="w-full h-full object-cover cursor-pointer" onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))} />
                                        <div className="absolute inset-0 flex items-end justify-end p-1 pointer-events-none">
                                          <Pointer size={14} className="text-white bg-black/40 rounded p-0.5" />
                                        </div>
                                      </div>
                                    )
                                  }

                                  ; (async () => {
                                    try {
                                      const res = await fetch('/api/uploads/presigned-get', {
                                        method: 'POST',
                                        headers: { 'Content-Type': 'application/json' },
                                        body: JSON.stringify({ key: s3KeyFromUrl }),
                                      })
                                      if (!res.ok) return
                                      const data = await res.json().catch(() => null)
                                      if (data?.url) {
                                        setPresignedMap((prev) => ({ ...prev, [s3KeyFromUrl]: data.url }))
                                      }
                                    } catch { }
                                  })()

                                  return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                }

                                if (/^(https?:)?\/\//i.test(source) || source.startsWith('data:')) {
                                  return (
                                    <div className="w-full h-full relative">
                                      <img src={source} alt={`Preview ${index + 1}`} width={64} height={64} className="w-full h-full object-cover cursor-pointer" onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))} />
                                      <div className="absolute inset-0 flex items-end justify-end p-1 pointer-events-none">
                                        <Pointer size={14} className="text-white bg-black/40 rounded p-0.5" />
                                      </div>
                                    </div>
                                  )
                                }

                                const cached = presignedMap[source]
                                if (cached) {
                                  return (
                                    <div className="w-full h-full relative">
                                      <img src={cached} alt={`Preview ${index + 1}`} width={64} height={64} className="w-full h-full object-cover cursor-pointer" onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))} />
                                      <div className="absolute inset-0 flex items-center justify-center p-1 pointer-events-none">
                                        <Pointer size={18} className="text-white bg-black/40 rounded p-0.5" />
                                      </div>
                                    </div>
                                  )
                                }

                                ; (async () => {
                                  try {
                                    const res = await fetch('/api/uploads/presigned-get', {
                                      method: 'POST',
                                      headers: { 'Content-Type': 'application/json' },
                                      body: JSON.stringify({ key: source }),
                                    })
                                    if (!res.ok) return
                                    const data = await res.json().catch(() => null)
                                    if (data?.url) {
                                      setPresignedMap((prev) => ({ ...prev, [source]: data.url }))
                                    }
                                  } catch { }
                                })()

                                return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                              })()}
                            </div>

                            <div className="flex-1 space-y-2">
                              <div className="flex items-center gap-2">
                                <Badge variant="secondary" className="text-xs border-none">
                                  Criativo {index + 1}
                                </Badge>
                                <Badge variant={urlItem.type === "feed" ? "default" : "secondary"} className="border-none">
                                  {urlItem.type === "feed" ? "Feed" : "Story"}
                                </Badge>
                                <Badge variant="outline">{urlItem.mediaType === "foto" ? "Foto" : "Vídeo"}</Badge>
                              </div>

                              <div className="flex flex-col xl:flex-row xl:items-center justify-between gap-2">
                                <S3UploadButton
                                  accept="image/*,video/*"
                                  onUploaded={(res) => handleUrlChange(index, res.key || '')}
                                />

                                <div className="flex flex-wrap gap-2">
                                  <Select
                                    value={urlItem.type}
                                    onValueChange={(value: "feed" | "story") => handleTypeChange(index, value)}
                                  >
                                    <SelectTrigger className="w-24 h-8">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="feed">Feed</SelectItem>
                                      <SelectItem value="story">Story</SelectItem>
                                    </SelectContent>
                                  </Select>

                                  <Select
                                    value={urlItem.mediaType}
                                    onValueChange={(value: "foto" | "video") => handleMediaTypeChange(index, value)}
                                  >
                                    <SelectTrigger className="w-24 h-8">
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="foto">Foto</SelectItem>
                                      <SelectItem value="video">Vídeo</SelectItem>
                                    </SelectContent>
                                  </Select>

                                  <Button
                                    variant="destructive"
                                    size="lg"
                                    onClick={() => removeUrl(index)}
                                    className="h-8 w-8"
                                    title="Remover URL"
                                  >
                                    <Trash2 size={14} />
                                  </Button>
                                </div>
                              </div>

                              {urlItem.mediaType === "video" && urlItem.type === "feed" && (
                                <div className="mt-2 flex flex-col items-end">
                                  <Label className="text-xs text-muted-foreground">URL da capa</Label>
                                  <div className="flex gap-2 mt-1">
                                    <S3UploadButton
                                      accept="image/*"
                                      onUploaded={(res) => handleThumbnailUrlChange(index, res.key || '')}
                                    />

                                    <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden flex-shrink-0">
                                      {(() => {
                                        const source = urlItem.thumbnailUrl || ''
                                        const driveId = extractGoogleDriveId(source)
                                        if (!source) return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                        if (driveId) {
                                          const src = `/api/drive-proxy?id=${driveId}&quality=low&size=small`
                                          return (
                                            <Image
                                              src={src}
                                              alt={`Capa ${index + 1}`}
                                              width={64}
                                              height={64}
                                              className="w-full h-full object-cover"
                                            />
                                          )
                                        }

                                        const s3KeyFromUrl = extractS3KeyFromUrl(source)
                                        if (s3KeyFromUrl) {
                                          const cached = presignedMap[s3KeyFromUrl]
                                          if (cached) {
                                            return <img src={cached} alt={`Capa ${index + 1}`} width={64} height={64} className="w-full h-full object-cover" />
                                          }

                                          ; (async () => {
                                            try {
                                              const res = await fetch('/api/uploads/presigned-get', {
                                                method: 'POST',
                                                headers: { 'Content-Type': 'application/json' },
                                                body: JSON.stringify({ key: s3KeyFromUrl }),
                                              })
                                              if (!res.ok) return
                                              const data = await res.json().catch(() => null)
                                              if (data?.url) {
                                                setPresignedMap((prev) => ({ ...prev, [s3KeyFromUrl]: data.url }))
                                              }
                                            } catch { }
                                          })()

                                          return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                        }

                                        if (/^(https?:)?\/\//i.test(source) || source.startsWith('data:')) {
                                          return <img src={source} alt={`Capa ${index + 1}`} width={64} height={64} className="w-full h-full object-cover" />
                                        }

                                        const cached = presignedMap[source]
                                        if (cached) {
                                          return <img src={cached} alt={`Capa ${index + 1}`} width={64} height={64} className="w-full h-full object-cover" />
                                        }

                                        ; (async () => {
                                          try {
                                            const res = await fetch('/api/uploads/presigned-get', {
                                              method: 'POST',
                                              headers: { 'Content-Type': 'application/json' },
                                              body: JSON.stringify({ key: source }),
                                            })
                                            if (!res.ok) return
                                            const data = await res.json().catch(() => null)
                                            if (data?.url) {
                                              setPresignedMap((prev) => ({ ...prev, [source]: data.url }))
                                            }
                                          } catch { }
                                        })()

                                        return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                      })()}
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end p-4">
                  <Button
                    variant="outline"
                    onClick={addNewUrl}
                    size="sm"
                  >
                    <Plus size={16} />
                    Adicionar
                  </Button>
                </CardFooter>
              </Card>
            </div>

            <div className="flex flex-col overflow-hidden">
              <Card className="flex-1 overflow-hidden">
                <CardContent className="p-4 h-full flex flex-col">
                  <div className="flex items-center justify-between mb-4">
                    <Label className="text-sm font-medium">
                      Visualização de todos os criativos ({validUrls.length})
                    </Label>
                    <div className="flex items-center gap-2">
                      <Button
                        variant={viewMode === "grid" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setViewMode("grid")}
                      >
                        <Grid3X3 size={16} />
                      </Button>
                      <Button
                        variant={viewMode === "list" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setViewMode("list")}
                      >
                        <List size={16} />
                      </Button>
                    </div>
                  </div>

                  <div className="flex-1 overflow-y-auto">
                    {validUrls.length === 0 ? (
                      <div className="h-full flex items-center justify-center bg-zinc-50 dark:bg-zinc-900 rounded-lg">
                        <div className="text-center text-gray-400 p-4">
                          <Eye size={28} className="mx-auto mb-2" />
                          <p className="text-sm">Adicione URLs ou arquivos para visualizar os criativos</p>
                        </div>
                      </div>
                    ) : (
                      <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
                        <TabsList className="grid w-full grid-cols-3">
                          <TabsTrigger value="all">Todos ({validUrls.length})</TabsTrigger>
                          <TabsTrigger value="feed">
                            Feed ({validUrls.filter((u) => u.type === "feed").length})
                          </TabsTrigger>
                          <TabsTrigger value="story">
                            Story ({validUrls.filter((u) => u.type === "story").length})
                          </TabsTrigger>
                        </TabsList>

                        <TabsContent value="all" className="flex-1 mt-4">
                          <div className={viewMode === "grid" ? "grid grid-cols-2 xl:grid-cols-3 gap-4" : "space-y-4"}>
                            {validUrls.map((urlItem, index) => (
                              <Card key={urlItem.id} className="overflow-hidden">
                                <CardContent className="p-0">
                                  <div className="aspect-auto">
                                    {(() => {
                                      const source = urlItem.mediaType === 'video' && urlItem.thumbnailUrl ? urlItem.thumbnailUrl : urlItem.url || ''
                                      const driveId = extractGoogleDriveId(source)
                                      if (!source) return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                      if (driveId) {
                                        const src = `/api/drive-proxy?id=${driveId}&quality=medium&size=medium`
                                        return (
                                          <div className="w-full h-full relative">
                                            <Image
                                              src={src}
                                              alt={`Criativo ${index + 1}`}
                                              width={viewMode === "grid" ? 300 : 500}
                                              height={viewMode === "grid" ? 300 : 300}
                                              className="w-full h-full object-cover cursor-pointer"
                                              onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))}
                                            />
                                            <div className="absolute inset-0 flex items-end justify-end p-2 pointer-events-none">
                                              <Pointer size={18} className="text-white bg-black/40 rounded p-0.5" />
                                            </div>
                                          </div>
                                        )
                                      }

                                      const s3KeyFromUrl = extractS3KeyFromUrl(source)
                                      if (s3KeyFromUrl) {
                                        const cached = presignedMap[s3KeyFromUrl]
                                        if (cached) {
                                          return (
                                            <div className="w-full h-full relative">
                                              <img src={cached} alt={`Criativo ${index + 1}`} width={viewMode === "grid" ? 300 : 500} height={viewMode === "grid" ? 300 : 300} className="w-full h-full object-cover cursor-pointer" onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))} />
                                              <div className="absolute inset-0 flex items-end justify-end p-2 pointer-events-none">
                                                <Pointer size={18} className="text-white bg-black/40 rounded p-0.5" />
                                              </div>
                                            </div>
                                          )
                                        }

                                        ; (async () => {
                                          try {
                                            const res = await fetch('/api/uploads/presigned-get', {
                                              method: 'POST',
                                              headers: { 'Content-Type': 'application/json' },
                                              body: JSON.stringify({ key: s3KeyFromUrl }),
                                            })
                                            if (!res.ok) return
                                            const data = await res.json().catch(() => null)
                                            if (data?.url) {
                                              setPresignedMap((prev) => ({ ...prev, [s3KeyFromUrl]: data.url }))
                                            }
                                          } catch { }
                                        })()

                                        return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                      }

                                      if (/^(https?:)?\/\//i.test(source) || source.startsWith('data:')) {
                                        return (
                                          <div className="w-full h-full relative">
                                            <img src={source} alt={`Criativo ${index + 1}`} width={viewMode === "grid" ? 300 : 500} height={viewMode === "grid" ? 300 : 300} className="w-full h-full object-cover cursor-pointer" onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))} />
                                            <div className="absolute inset-0 flex items-end justify-end p-2 pointer-events-none">
                                              <Pointer size={18} className="text-white bg-black/40 rounded p-0.5" />
                                            </div>
                                          </div>
                                        )
                                      }

                                      const cached = presignedMap[source]
                                      if (cached) {
                                        return <img src={cached} alt={`Criativo ${index + 1}`} width={viewMode === "grid" ? 300 : 500} height={viewMode === "grid" ? 300 : 300} className="w-full h-full object-cover" />
                                      }

                                      ; (async () => {
                                        try {
                                          const res = await fetch('/api/uploads/presigned-get', {
                                            method: 'POST',
                                            headers: { 'Content-Type': 'application/json' },
                                            body: JSON.stringify({ key: source }),
                                          })
                                          if (!res.ok) return
                                          const data = await res.json().catch(() => null)
                                          if (data?.url) {
                                            setPresignedMap((prev) => ({ ...prev, [source]: data.url }))
                                          }
                                        } catch { }
                                      })()

                                      return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                    })()}
                                  </div>
                                  <div className="p-3">
                                    <div className="flex items-center justify-center flex-wrap gap-2">
                                      <Badge
                                        variant={urlItem.type === "feed" ? "default" : "secondary"}
                                        className="text-xs border-none"
                                      >
                                        {urlItem.type === "feed" ? "Feed" : "Story"}
                                      </Badge>
                                      <Badge variant="outline" className="text-xs">
                                        {urlItem.mediaType === "foto" ? "Foto" : "Vídeo"}
                                      </Badge>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        </TabsContent>

                        <TabsContent value="feed" className="flex-1 mt-4">
                          <div className={viewMode === "grid" ? "grid grid-cols-2 xl:grid-cols-3 gap-4" : "space-y-4"}>
                            {validUrls
                              .filter((u) => u.type === "feed")
                              .map((urlItem, index) => (
                                <Card key={urlItem.id} className="overflow-hidden">
                                  <CardContent className="p-0">
                                    <div className="aspect-auto">
                                      {(() => {
                                        const source = urlItem.mediaType === 'video' && urlItem.thumbnailUrl ? urlItem.thumbnailUrl : urlItem.url || ''
                                        const driveId = extractGoogleDriveId(source)
                                        if (!source) return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                        if (driveId) {
                                          const src = `/api/drive-proxy?id=${driveId}&quality=medium&size=medium`
                                          return (
                                            <Image
                                              src={src}
                                              alt={`Feed ${index + 1}`}
                                              width={viewMode === "grid" ? 300 : 500}
                                              height={viewMode === "grid" ? 300 : 300}
                                              className="w-full h-full object-cover cursor-pointer"
                                              onClick={() => openFullscreenFromList(validUrls.map(u => ({ id: u.id, url: u.url, type: u.type, mediaType: u.mediaType, thumbnailUrl: u.thumbnailUrl })), validUrls.findIndex(v => v.url === urlItem.url))}
                                            />
                                          )
                                        }

                                        const s3KeyFromUrl = extractS3KeyFromUrl(source)
                                        if (s3KeyFromUrl) {
                                          const cached = presignedMap[s3KeyFromUrl]
                                          if (cached) {
                                            return (
                                              <div className="w-full h-full relative">
                                                <img src={cached} alt={`Feed ${index + 1}`} width={viewMode === "grid" ? 300 : 500} height={viewMode === "grid" ? 300 : 300} className="w-full h-full object-cover cursor-pointer" onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))} />
                                                <div className="absolute inset-0 flex items-end justify-end p-2 pointer-events-none">
                                                  <Pointer size={18} className="text-white bg-black/40 rounded p-0.5" />
                                                </div>
                                              </div>
                                            )
                                          }

                                          ; (async () => {
                                            try {
                                              const res = await fetch('/api/uploads/presigned-get', {
                                                method: 'POST',
                                                headers: { 'Content-Type': 'application/json' },
                                                body: JSON.stringify({ key: s3KeyFromUrl }),
                                              })
                                              if (!res.ok) return
                                              const data = await res.json().catch(() => null)
                                              if (data?.url) {
                                                setPresignedMap((prev) => ({ ...prev, [s3KeyFromUrl]: data.url }))
                                              }
                                            } catch { }
                                          })()

                                          return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                        }

                                        if (/^(https?:)?\/\//i.test(source) || source.startsWith('data:')) {
                                          return (
                                            <div className="w-full h-full relative">
                                              <img src={source} alt={`Feed ${index + 1}`} width={viewMode === "grid" ? 300 : 500} height={viewMode === "grid" ? 300 : 300} className="w-full h-full object-cover cursor-pointer" onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))} />
                                              <div className="absolute inset-0 flex items-end justify-end p-2 pointer-events-none">
                                                <Pointer size={18} className="text-white bg-black/40 rounded p-0.5" />
                                              </div>
                                            </div>
                                          )
                                        }

                                        const cached = presignedMap[source]
                                        if (cached) {
                                          return <img src={cached} alt={`Feed ${index + 1}`} width={viewMode === "grid" ? 300 : 500} height={viewMode === "grid" ? 300 : 300} className="w-full h-full object-cover" />
                                        }

                                        ; (async () => {
                                          try {
                                            const res = await fetch('/api/uploads/presigned-get', {
                                              method: 'POST',
                                              headers: { 'Content-Type': 'application/json' },
                                              body: JSON.stringify({ key: source }),
                                            })
                                            if (!res.ok) return
                                            const data = await res.json().catch(() => null)
                                            if (data?.url) {
                                              setPresignedMap((prev) => ({ ...prev, [source]: data.url }))
                                            }
                                          } catch { }
                                        })()

                                        return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                      })()}
                                    </div>
                                    <div className="p-3">
                                      <Badge variant="outline" className="text-xs">
                                        {urlItem.mediaType === "foto" ? "Foto" : "Vídeo"}
                                      </Badge>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}
                          </div>
                        </TabsContent>

                        <TabsContent value="story" className="flex-1 mt-4">
                          <div className={viewMode === "grid" ? "grid grid-cols-2 xl:grid-cols-3 gap-4" : "space-y-4"}>
                            {validUrls
                              .filter((u) => u.type === "story")
                              .map((urlItem, index) => (
                                <Card key={urlItem.id} className="overflow-hidden">
                                  <CardContent className="p-0">
                                    <div className="aspect-auto">
                                      {(() => {
                                        const source = urlItem.mediaType === 'video' && urlItem.thumbnailUrl ? urlItem.thumbnailUrl : urlItem.url || ''
                                        const driveId = extractGoogleDriveId(source)
                                        if (!source) return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                        if (driveId) {
                                          const src = `/api/drive-proxy?id=${driveId}&quality=medium&size=medium`
                                          return (
                                            <div className="w-full h-full relative">
                                              <Image
                                                src={src}
                                                alt={`Story ${index + 1}`}
                                                width={viewMode === "grid" ? 200 : 300}
                                                height={viewMode === "grid" ? 356 : 533}
                                                className="w-full h-full object-cover cursor-pointer"
                                                onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))}
                                              />
                                              <div className="absolute inset-0 flex items-end justify-end p-2 pointer-events-none">
                                                <Pointer size={16} className="text-white bg-black/40 rounded p-0.5" />
                                              </div>
                                            </div>
                                          )
                                        }

                                        const s3KeyFromUrl = extractS3KeyFromUrl(source)
                                        if (s3KeyFromUrl) {
                                          const cached = presignedMap[s3KeyFromUrl]
                                          if (cached) {
                                            return (
                                              <div className="w-full h-full relative">
                                                <img src={cached} alt={`Story ${index + 1}`} width={viewMode === "grid" ? 200 : 300} height={viewMode === "grid" ? 356 : 533} className="w-full h-full object-cover cursor-pointer" onClick={() => openFullscreenFromList(validUrls, validUrls.findIndex(v => v.id === urlItem.id))} />
                                                <div className="absolute inset-0 flex items-end justify-end p-2 pointer-events-none">
                                                  <Pointer size={16} className="text-white bg-black/40 rounded p-0.5" />
                                                </div>
                                              </div>
                                            )
                                          }

                                          ; (async () => {
                                            try {
                                              const res = await fetch('/api/uploads/presigned-get', {
                                                method: 'POST',
                                                headers: { 'Content-Type': 'application/json' },
                                                body: JSON.stringify({ key: s3KeyFromUrl }),
                                              })
                                              if (!res.ok) return
                                              const data = await res.json().catch(() => null)
                                              if (data?.url) {
                                                setPresignedMap((prev) => ({ ...prev, [s3KeyFromUrl]: data.url }))
                                              }
                                            } catch { }
                                          })()

                                          return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                        }

                                        if (/^(https?:)?\/\//i.test(source) || source.startsWith('data:')) {
                                          return <img src={source} alt={`Story ${index + 1}`} width={viewMode === "grid" ? 200 : 300} height={viewMode === "grid" ? 356 : 533} className="w-full h-full object-cover cursor-pointer" onClick={() => openFullscreenFromList(validUrls.map(u => ({ id: u.id, url: u.url, type: u.type, mediaType: u.mediaType, thumbnailUrl: u.thumbnailUrl })), validUrls.findIndex(v => v.url === urlItem.url))} />
                                        }

                                        const cached = presignedMap[source]
                                        if (cached) {
                                          return <img src={cached} alt={`Story ${index + 1}`} width={viewMode === "grid" ? 200 : 300} height={viewMode === "grid" ? 356 : 533} className="w-full h-full object-cover" />
                                        }

                                        ; (async () => {
                                          try {
                                            const res = await fetch('/api/uploads/presigned-get', {
                                              method: 'POST',
                                              headers: { 'Content-Type': 'application/json' },
                                              body: JSON.stringify({ key: source }),
                                            })
                                            if (!res.ok) return
                                            const data = await res.json().catch(() => null)
                                            if (data?.url) {
                                              setPresignedMap((prev) => ({ ...prev, [source]: data.url }))
                                            }
                                          } catch { }
                                        })()

                                        return <div className="w-full h-full bg-gray-100 dark:bg-gray-800" />
                                      })()}
                                    </div>
                                    <div className="p-3">
                                      <Badge variant="outline" className="text-xs">
                                        {urlItem.mediaType === "foto" ? "Foto" : "Vídeo"}
                                      </Badge>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}
                          </div>
                        </TabsContent>
                      </Tabs>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <DialogFooter className="flex-shrink-0">
            <Button variant="secondary" onClick={handleCloseModal}>
              Cancelar
            </Button>
            <Button onClick={handleUpdateUrl} disabled={isUpdating}>
              {isUpdating ? (
                <>
                  <Ellipsis className="animate-pulse" />
                  Salvando
                </>
              ) : (
                <>Salvar</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* Fullscreen viewer dialog */}
      <Dialog open={fullscreenOpen} onOpenChange={(_open) => { if (!_open) closeFullscreen() }}>
        <DialogTitle></DialogTitle>
        <DialogContent className="max-w-7xl border-zinc-500 w-full h-[95vh] p-0 bg-black/90 flex items-center justify-center">
          <div className="relative w-full h-full flex items-center justify-center">
            <button
              aria-label="Fechar visualização"
              onClick={closeFullscreen}
              className="absolute top-4 right-4 z-5 p-2 rounded"
            >
            </button>

            <button className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 rounded-full p-2" onClick={prevFullscreen} aria-label="Anterior"><ChevronLeft /></button>
            <button className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 rounded-full p-2" onClick={nextFullscreen} aria-label="Próximo"><ChevronRight /></button>

            {fullscreenGallery.length ? (
              <img
                src={fullscreenGallery[fullscreenIndex]}
                alt={`Visualização ${fullscreenIndex + 1}`}
                className="max-w-full max-h-[90vh] object-contain"
              />
            ) : null}

            {fullscreenGallery.length > 0 && (
              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-50 text-white text-sm bg-black/40 px-3 py-1 rounded">
                {fullscreenIndex + 1} / {fullscreenGallery.length}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
