"use client";

import React, { useState } from "react";
import { Button } from "@/app/components/ui/button";
import { Check, Ellipsis } from "lucide-react";
import { toast } from "sonner";

interface Props {
    contentId: string;
    currentStatus?: string;
    onSuccess?: () => void;
    className?: string;
    size?: 'sm' | 'default';
    disabled?: boolean;
}

export const ApproveDemandButton = ({ contentId, currentStatus, onSuccess, className, size = 'default', disabled }: Props) => {
    const [processing, setProcessing] = useState(false);

    const handleClick = async () => {
        if (processing) return;
        setProcessing(true);
        try {
            const resp = await fetch(`/api/contents/${contentId}`, {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ status: 'aprovado' }),
            });
            if (!resp.ok) {
                const err = await resp.json().catch(() => null);
                throw new Error(err?.message || 'Erro ao atualizar status');
            }

            toast.success('Marcado como aprovado!');
            onSuccess?.();
        } catch (e) {
            console.error('Erro ao marcar como aprovado', e);
            toast.error(e instanceof Error ? e.message : 'Erro ao marcar como aprovado');
        } finally {
            setProcessing(false);
        }
    };

    return (
        <Button
            size={size}
            onClick={handleClick}
            disabled={processing || currentStatus === 'aprovado' || currentStatus === 'concluído' || disabled}
            className={`bg-green-500 hover:bg-green-600 w-full ${className}`}
        >
            {processing ? <Ellipsis /> : (currentStatus === 'aprovado' || currentStatus === 'concluído' ? 'Aprovado' : <span className="flex items-center gap-1"><Check /> Aprovar</span>)}
        </Button>
    );
}

export default ApproveDemandButton;
