"use client";

import { useState } from "react";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/app/components/ui/alert-dialog";
import { Button } from "@/app/components/ui/button";
import { Archive, ArchiveRestore, Ellipsis } from "lucide-react";
import { toast } from "sonner";

interface ArchiveDemandProps {
    id: string;
    type: 'content' | 'general';
    title: string;
    isArchived: boolean;
    onSuccess: () => void;
    onLocalUpdate?: (demandId: string, isArchiving: boolean) => void;
}

export function ArchiveDemand({ id, type, title, isArchived, onSuccess, onLocalUpdate }: ArchiveDemandProps) {
    const [open, setOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const handleArchiveToggle = async () => {
        setIsLoading(true);
        try {
            const endpoint = type === 'content'
                ? `/api/contents/${id}/archive`
                : `/api/general-demands/${id}/archive`;

            const response = await fetch(endpoint, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ archived: !isArchived }),
            });

            if (!response.ok) {
                throw new Error(isArchived ? "Erro ao desarquivar demanda" : "Erro ao arquivar demanda");
            }

            toast.success(
                `${type === 'content' ? 'Conteúdo' : 'Demanda pontual'} ${isArchived ? 'desarquivado' : 'arquivado'
                } com sucesso!`
            );
            
            if (onLocalUpdate) {
                onLocalUpdate(id, !isArchived);
            }
            
            onSuccess();
        } catch (error) {
            console.error("Erro ao modificar arquivamento:", error);
            toast.error(isArchived ? "Erro ao desarquivar demanda" : "Erro ao arquivar demanda");
        } finally {
            setIsLoading(false);
            setOpen(false);
        }
    };

    return (
        <>
            <Button
                variant="outline"
                size="icon"
                title={isArchived ? "Desarquivar demanda" : "Arquivar demanda"}
                onClick={() => setOpen(true)}
            >
                {isArchived ? (
                    <ArchiveRestore className="h-4 w-4" />
                ) : (
                    <Archive className="h-4 w-4" />
                )}
            </Button>

            <AlertDialog open={open} onOpenChange={setOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>
                            {isArchived ? "Desarquivar" : "Arquivar"} {type === 'content' ? 'conteúdo' : 'demanda pontual'}?
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                            {isArchived
                                ? `Este ${type === 'content' ? 'conteúdo' : 'demanda pontual'} será desarquivado e voltará a aparecer na lista de demandas pendentes.`
                                : `Este ${type === 'content' ? 'conteúdo' : 'demanda pontual'} será arquivado e não aparecerá mais na lista de demandas pendentes.`
                            }
                            <br />
                            <br />
                            <span className="font-bold">Item:</span> {title}
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={isLoading}>Cancelar</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={(e) => {
                                e.preventDefault();
                                handleArchiveToggle();
                            }}
                            disabled={isLoading}
                            className={isArchived ? "bg-green-600 hover:bg-green-700" : "bg-red-600 hover:bg-red-700"}
                        >
                            {isLoading
                                ? (<Ellipsis />)
                                : (isArchived ? "Desarquivar" : "Arquivar")
                            }
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}