"use client"

import { useState, useEffect } from 'react';
import { Ellipsis, UserPlus } from 'lucide-react';
import { toast } from 'sonner';
import { Avatar, AvatarFallback, AvatarImage } from '@/app/components/ui/avatar';
import { Button } from '@/app/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/app/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import { Badge } from '@/app/components/ui/badge';

interface UserData {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  role?: string;
}

interface AssignedUser {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
}

interface AssignBulkDemandsProps {
  contentIds: string[];
  generalDemandIds: string[];
  onSuccess?: () => void;
  onLocalUpdate?: (
    contentIds: string[],
    generalDemandIds: string[],
    userId: string | null,
    user: AssignedUser | null
  ) => void;
}

export function AssignBulkDemands({
  contentIds,
  generalDemandIds,
  onSuccess,
  onLocalUpdate,
}: AssignBulkDemandsProps) {
  const [users, setUsers] = useState<UserData[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const usersWithRole = users.filter(user => user.role !== "DEVELOPER" && user.role !== "VIEWER");
  const totalDemands = contentIds.length + generalDemandIds.length;

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen]);

  const fetchUsers = async () => {
    try {
      const response = await fetch("/api/users");
      if (response.ok) {
        const data = await response.json();
        const filteredUsers = data.filter(
          (user: UserData) => user.role !== "DEVELOPER"
        );
        setUsers(filteredUsers);
      } else {
        toast.error("Erro ao carregar usuários");
      }
    } catch (error) {
      console.error("Erro ao buscar usuários:", error);
      toast.error("Erro ao carregar usuários");
    }
  };

  const handleAssign = async () => {
    if (totalDemands === 0) {
      toast.error("Selecione pelo menos uma demanda");
      return;
    }

    setIsLoading(true);

    const selectedUser = selectedUserId
      ? users.find((user) => user.id === selectedUserId)
      : null;
    const userForLocalUpdate = selectedUser
      ? {
        id: selectedUser.id,
        name: selectedUser.name,
        email: selectedUser.email,
        image: selectedUser.image,
      }
      : null;

    if (onLocalUpdate) {
      onLocalUpdate(contentIds, generalDemandIds, selectedUserId, userForLocalUpdate);
    }

    try {
      if (contentIds.length > 0) {
        const contentResponse = await fetch(`/api/contents/assign-bulk`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ contentIds, userId: selectedUserId }),
        });

        if (!contentResponse.ok) {
          const error = await contentResponse.json();
          toast.error(error.message || "Erro ao atribuir demandas de conteúdo");
        }
      }

      if (generalDemandIds.length > 0) {
        const generalResponse = await fetch(`/api/general-demands/assign-bulk`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ demandIds: generalDemandIds, userId: selectedUserId }),
        });

        if (!generalResponse.ok) {
          const error = await generalResponse.json();
          toast.error(error.message || "Erro ao atribuir demandas pontuais");
        }
      }

      toast.success(`Demandas atribuídas com sucesso`);
      setIsOpen(false);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Erro ao atribuir demandas:", error);
      toast.error("Erro ao atribuir demandas");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className='flex flex-col items-end gap-2'>
          <Button
            variant="outline"
            className="gap-1 whitespace-nowrap w-full"
            disabled={totalDemands === 0}
          >
            <UserPlus className="h-4 w-4" />
            Atribuir responsável
            {totalDemands > 0 && (
              <Badge variant="secondary" className="ml-1">
                {totalDemands}
              </Badge>
            )}
          </Button>
          {totalDemands > 0 && (
            <p className='text-xs text-muted-foreground max-w-[200px]'>
              Ao atribuir dessa maneira, as demandas que contém múltiplos responsáveis, terão 1 responsável a mais. Para gerenciar múltiplos responsáveis, clique no botão para editar a demanda.
            </p>
          )}
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Atribuir pessoa responsável</DialogTitle>
          <DialogDescription>
            {totalDemands > 0
              ? `Selecione um membro da equipe para atribuir às ${totalDemands} demandas selecionadas`
              : "Selecione pelo menos uma demanda para atribuir"}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Select
            value={selectedUserId || "none"}
            onValueChange={(value) =>
              setSelectedUserId(value === "none" ? null : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione um responsável" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">Nenhum responsável</SelectItem>
              {usersWithRole
                .sort((a, b) => {
                  const nameA = (a.name || a.email).toLowerCase();
                  const nameB = (b.name || b.email).toLowerCase();
                  return nameA.localeCompare(nameB);
                })
                .map((user) => (
                  <SelectItem
                    key={user.id}
                    value={user.id}
                    className="flex items-center gap-2"
                  >
                    <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6 border-2 border-zinc-200">
                      <AvatarImage
                        src={user.image || undefined}
                        alt={user.name || user.email}
                      />
                      <AvatarFallback className="text-xs">
                        {user.name?.[0] || user.email[0]}
                      </AvatarFallback>
                    </Avatar>
                    <span>
                      {(user.name)?.split(' ')[0] || user.email}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            className="whitespace-nowrap"
          >
            Cancelar
          </Button>
          <Button
            onClick={handleAssign}
            disabled={isLoading}
            className="whitespace-nowrap"
          >
            {isLoading ? <Ellipsis /> : "Atribuir"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
