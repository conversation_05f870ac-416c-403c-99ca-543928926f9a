"use client"

import { useState, useEffect, useCallback } from 'react';
import { Ellipsis, Users } from 'lucide-react';
import { toast } from 'sonner';
import { Avatar, AvatarFallback, AvatarImage } from '@/app/components/ui/avatar';
import { Button } from '@/app/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/app/components/ui/dialog';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/app/components/ui/select';
import { Badge } from '@/app/components/ui/badge';
import { Label } from '@/app/components/ui/label';

interface UserData {
    id: string;
    name: string | null;
    email: string;
    image: string | null;
}

interface Step {
    type: string;
    assignedToId: string;
    assignedTo?: {
        id: string;
        name: string | null;
        email: string;
        image: string | null;
    };
}

const stepTypeOptions = [
    { value: "CAPTACAO", label: "Capta<PERSON>" },
    { value: "DESIG<PERSON>", label: "Design" },
    { value: "EDICAO", label: "Edi<PERSON>" },
    { value: "TRAFEGO", label: "Tr<PERSON><PERSON><PERSON>" },
];

interface AssignContentStepsProps {
    contentId: string;
    initialSteps?: Step[];
    onSuccess?: () => void;
    onLocalUpdate?: (steps: Step[]) => void;
}

export function AssignContentSteps({ contentId, initialSteps = [], onSuccess, onLocalUpdate }: AssignContentStepsProps) {
    const [users, setUsers] = useState<UserData[]>([]);
    const [steps, setSteps] = useState<Step[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        if (initialSteps.length > 0) {
            setSteps(initialSteps);
        } else {
            setSteps(stepTypeOptions.map(opt => ({
                type: opt.value,
                assignedToId: ''
            })));
        }
    }, [initialSteps]);

    const fetchUsers = useCallback(async () => {
        try {
            const response = await fetch('/api/users');
            if (response.ok) {
                const data = await response.json();
                setUsers(data);
            } else {
                toast.error('Erro ao carregar usuários');
            }
        } catch (error) {
            console.error('Error fetching users:', error);
            toast.error('Erro ao carregar usuários');
        }
    }, []);

    const fetchCurrentSteps = useCallback(async () => {
        try {
            const response = await fetch(`/api/content-steps?contentId=${contentId}`);
            if (response.ok) {
                const data = await response.json();
                if (data.length > 0) {
                    setSteps(data);
                }
            }
        } catch (error) {
            console.error('Error fetching content steps:', error);
        }
    }, [contentId]);
    
    useEffect(() => {
        if (isOpen) {
            fetchUsers();
            if (initialSteps.length === 0) {
                fetchCurrentSteps();
            }
        }
    }, [isOpen, initialSteps, fetchUsers, fetchCurrentSteps]);

    const handleAssignSteps = async () => {
        const invalidSteps = steps.filter(step => !step.assignedToId);
        if (invalidSteps.length > 0) {
            toast.error('Todas as etapas precisam ter um responsável atribuído');
            return;
        }

        setIsLoading(true);

        if (onLocalUpdate) {
            const updatedSteps = steps.map(step => {
                const assignedUser = users.find(user => user.id === step.assignedToId);
                return {
                    ...step,
                    assignedTo: assignedUser ? {
                        id: assignedUser.id,
                        name: assignedUser.name,
                        email: assignedUser.email,
                        image: assignedUser.image
                    } : undefined
                };
            });

            onLocalUpdate(updatedSteps);
        }

        try {
            const response = await fetch('/api/content-steps', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contentId,
                    steps: steps.map(({ type, assignedToId }) => ({ type, assignedToId }))
                }),
            });

            if (response.ok) {
                toast.success('Etapas atribuídas com sucesso');
                setIsOpen(false);

                if (onSuccess) {
                    onSuccess();
                }
            } else {
                const error = await response.json();
                throw new Error(error.message || 'Erro ao atribuir etapas');
            }
        } catch (error) {
            console.error('Erro ao atribuir etapas:', error);
            toast.error(error instanceof Error ? error.message : 'Erro ao atribuir etapas');
        } finally {
            setIsLoading(false);
        }
    };

    const updateStepAssignee = (type: string, userId: string) => {
        setSteps(current =>
            current.map(step =>
                step.type === type ? { ...step, assignedToId: userId } : step
            )
        );
    };

    const getStepLabel = (type: string) => {
        const option = stepTypeOptions.find(opt => opt.value === type);
        return option ? option.label : type;
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="icon">
                    <Users size={16} />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        Atribuir Etapas do Conteúdo
                        <Badge variant="outline">ID: {contentId.substring(0, 8)}</Badge>
                    </DialogTitle>
                </DialogHeader>

                <div className="space-y-4">
                    {steps.map((step) => (
                        <div key={step.type} className="space-y-2">
                            <Label htmlFor={`step-${step.type}`}>{getStepLabel(step.type)}</Label>
                            <Select
                                value={step.assignedToId}
                                onValueChange={(value) => updateStepAssignee(step.type, value)}
                            >
                                <SelectTrigger className="w-full" id={`step-${step.type}`}>
                                    <SelectValue placeholder="Selecione um responsável" />
                                </SelectTrigger>
                                <SelectContent>
                                    {users.map((user) => (
                                        <SelectItem key={user.id} value={user.id}>
                                            <div className="flex items-center gap-2">
                                                <Avatar className="h-6 w-6">
                                                    <AvatarImage src={user.image || ''} alt={user.name || ''} />
                                                    <AvatarFallback>{user.name?.substring(0, 2).toUpperCase()}</AvatarFallback>
                                                </Avatar>
                                                <span>{user.name}</span>
                                            </div>
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    ))}
                </div>

                <div className="flex justify-end gap-2 mt-4">
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancelar
                    </Button>
                    <Button onClick={handleAssignSteps} disabled={isLoading}>
                        {isLoading ? <Ellipsis className="mr-2 h-4 w-4 animate-spin" /> : null}
                        Salvar
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}
