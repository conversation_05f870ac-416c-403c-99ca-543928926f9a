"use client"

import { useState, useEffect } from 'react';
import { Ellipsis, UserPlus } from 'lucide-react';
import { toast } from 'sonner';
import { Avatar, AvatarFallback, AvatarImage } from '@/app/components/ui/avatar';
import { Button } from '@/app/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/app/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';

interface UserData {
  id: string;
  name: string | null;
  email: string;
  role: string;
  image: string | null;
}

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
}

interface AssignContentProps {
  contentId: string;
  currentAssigneeId: string | null | undefined;
  onSuccess?: () => void;
  onLocalUpdate?: (userId: string | null, user?: User | null) => void;
}

export function AssignContent({ contentId, currentAssigneeId, onSuccess, onLocalUpdate }: AssignContentProps) {
  const [users, setUsers] = useState<UserData[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(currentAssigneeId || null);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const usersWithRole = users.filter(user => user.role !== "DEVELOPER" && user.role !== "VIEWER");

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen]);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      } else {
        toast.error('Erro ao carregar usuários');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Erro ao carregar usuários');
    }
  };

  const handleAssign = async () => {
    setIsLoading(true);

    const selectedUser = selectedUserId ? users.find(user => user.id === selectedUserId) : null;
    const userForLocalUpdate = selectedUser ? {
      id: selectedUser.id,
      name: selectedUser.name,
      email: selectedUser.email,
      image: selectedUser.image
    } : null;

    if (onLocalUpdate) {
      onLocalUpdate(selectedUserId, userForLocalUpdate);
    }

    try {
      const response = await fetch(`/api/contents/${contentId}/assign`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: selectedUserId }),
      });

      if (response.ok) {
        const updatedContent = await response.json();
        toast.success('Conteúdo atribuído com sucesso');
        setIsOpen(false);

        if (onLocalUpdate) {
          onLocalUpdate(updatedContent.assignedToId, updatedContent.assignedTo);
        }

        if (onSuccess && !onLocalUpdate) {
          onSuccess();
        }
      } else {
        const error = await response.json();
        toast.error(error.message || 'Erro ao atribuir conteúdo');

        if (onLocalUpdate) {
          onLocalUpdate(currentAssigneeId || null);
        }
      }
    } catch (error) {
      console.error('Error assigning content:', error);
      toast.error('Erro ao atribuir conteúdo');

      if (onLocalUpdate) {
        onLocalUpdate(currentAssigneeId || null);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" title='Atribuir responsável'>
          <UserPlus className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Atribuir pessoa responsável</DialogTitle>
          <DialogDescription>
            Selecione um membro da equipe para atribuir
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Select
            value={selectedUserId || 'none'}
            onValueChange={(value) => setSelectedUserId(value === 'none' ? null : value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione um responsável" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">Nenhum responsável</SelectItem>
              {usersWithRole.map((user) => (
                <SelectItem key={user.id} value={user.id} className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6 border-2 border-zinc-200">
                      <AvatarImage src={user.image || undefined} alt={user.name || user.email}/>
                      <AvatarFallback className="text-xs">
                        {user.name?.[0] || user.email[0]}
                      </AvatarFallback>
                    </Avatar>
                    <span>{user.name || user.email}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancelar
          </Button>
          <Button onClick={handleAssign} disabled={isLoading}>
            {isLoading ? <Ellipsis /> : 'Atribuir'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
