import { Bread<PERSON>rum<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Bread<PERSON>rumbItem, Bread<PERSON>rumbLink, BreadcrumbSeparator, BreadcrumbPage } from "@/app/components/ui/breadcrumb";
import Link from "next/link";

interface CalendarBreadcrumbProps {
    clientId: string;
    clientName: string;
    calendarName?: string;
}

export function CalendarBreadcrumb({ clientId, calendarName }: CalendarBreadcrumbProps) {
    return (
        <div className="mb-6">
            <Breadcrumb>
                <BreadcrumbList>
                    <BreadcrumbItem>
                        <BreadcrumbLink asChild>
                            <Link href="/clients">Clientes</Link>
                        </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                        <BreadcrumbLink asChild>
                            <Link href={`/clients/${clientId}`}>Perfil do cliente</Link>
                        </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    {calendarName ? (
                        <>
                            <BreadcrumbItem>
                                <BreadcrumbLink asChild>
                                    <Link href={`/clients/${clientId}/calendars`}>Calendários</Link>
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>{calendarName}</BreadcrumbPage>
                            </BreadcrumbItem>
                        </>
                    ) : (
                        <BreadcrumbItem>
                            <BreadcrumbPage>Calendários</BreadcrumbPage>
                        </BreadcrumbItem>
                    )}
                </BreadcrumbList>
            </Breadcrumb>
        </div>
    );
}
