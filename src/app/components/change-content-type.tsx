"use client"

import { useState } from 'react';
import { Button } from './ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Edit } from 'lucide-react';
import { toast } from 'sonner';

interface ChangeContentTypeProps {
    contentId: string;
    currentType: string;
    onSuccess: () => void;
}

const contentTypes = [
    { value: 'estático', label: 'Estático' },
    { value: 'vídeo', label: 'Vídeo' },
    { value: 'carrossel', label: 'Carross<PERSON>' },
    { value: 'animação', label: 'Animação' },
];

export const ChangeContentType = ({ contentId, currentType, onSuccess }: ChangeContentTypeProps) => {
    const [open, setOpen] = useState(false);
    const [selectedType, setSelectedType] = useState(currentType);
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async () => {
        if (selectedType === currentType) {
            setOpen(false);
            return;
        }

        setIsLoading(true);
        try {
            const response = await fetch(`/api/contents/${contentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ contentType: selectedType }),
            });

            if (!response.ok) {
                throw new Error('Erro ao alterar tipo');
            }

            toast.success('Tipo alterado com sucesso!');
            setOpen(false);
            onSuccess();
        } catch (error) {
            console.error('Erro:', error);
            toast.error('Erro ao alterar tipo');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="icon" title="Editar tipo de conteúdo">
                    <Edit size={14} />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle>Alterar tipo de conteúdo</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                    <Select value={selectedType} onValueChange={setSelectedType}>
                        <SelectTrigger>
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            {contentTypes.map(type => (
                                <SelectItem key={type.value} value={type.value}>
                                    {type.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setOpen(false)}>
                            Cancelar
                        </Button>
                        <Button onClick={handleSubmit} disabled={isLoading}>
                            {isLoading ? 'Alterando' : 'Alterar'}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};