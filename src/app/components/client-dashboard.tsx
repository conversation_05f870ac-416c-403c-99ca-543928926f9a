"use client"

import React from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle, CardDescription } from "./ui/card";
import { Badge } from "./ui/badge";
import { User, Grid3x3, FileChartColumn, PackagePlus, Calendar, PackageSearch } from "lucide-react";

type ClientInfoProps = {
    client?: { id: string; name?: string } | null;
};

export default function ClientDashboard({ client }: ClientInfoProps) {
    const router = useRouter();

    if (!client) {
        return null;
    }

    const clientId = client.id;

    const items = [
        { key: 'profile', title: 'Meu perfil', desc: 'Ver seu perfil', icon: <User className="w-5 h-5" />, to: `/clients/${clientId}`, },
        { key: 'calendars', title: 'Calendários de conteúdos', desc: 'Visualizar planejamentos mensais', icon: <Calendar className="w-5 h-5" />, to: `/clients/${clientId}/calendars` },
        { key: 'social-media', title: 'Redes sociais', desc: 'Veja os conteúdos para suas redes sociais', icon: <Grid3x3 className="w-5 h-5" />, to: `/deliveries`, disabled: false },
        { key: 'results', title: 'Resultados', desc: 'Ver relatórios e métricas', icon: <FileChartColumn className="w-5 h-5" />, to: `/results-report/${clientId}`, disabled: true },
        { key: 'demands', title: 'Demandas', desc: 'Solicitar demandas', icon: <PackagePlus className="w-5 h-5" />, to: `/demands/${clientId}`, disabled: true },
        { key: 'my-materials', title: 'Meus materiais', desc: 'Visualizar materiais', icon: <PackageSearch className="w-5 h-5" />, to: `/demands/${clientId}`, disabled: true },
    ];

    return (
        <div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {items.map((it) => (
                    <Card
                        key={it.key}
                        className={`${it.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-lg'}`}
                        onClick={() => {
                            if (it.disabled) return;
                            router.push(it.to);
                        }}
                    >
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2 text-sm">
                                <span className="p-1 rounded bg-zinc-100 dark:bg-zinc-800">{it.icon}</span>
                                <span className="flex items-center gap-2">
                                    <span>{it.title}</span>
                                    {it.disabled && <Badge variant="secondary" className="text-xs">Em breve</Badge>}
                                </span>
                            </CardTitle>
                            <CardDescription className="text-xs text-muted-foreground">{it.desc}</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm text-muted-foreground">{it.disabled ? 'Em breve' : 'Clique para abrir'}</p>
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
}
