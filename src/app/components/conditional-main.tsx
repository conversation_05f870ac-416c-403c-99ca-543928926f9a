"use client";

import { usePathname } from "next/navigation";
import React from "react";

export default function ConditionalMain({ children }: { children: React.ReactNode }) {
    const pathname = usePathname() || "/";

    // Remove padding for the login and signup page so it can be perfectly centered.
    const isLogin = pathname === "/login" || pathname === "/login/" || pathname === "/signup" || pathname === "/signup/";

    return (
        <main className={isLogin ? "h-screen" : "pb-24 pt-20 md:pb-0 md:pt-0"}>
            {children}
        </main>
    );
}
