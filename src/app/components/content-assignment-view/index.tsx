"use client"

import React from 'react';
import { ArrowRight, Users } from 'lucide-react';
import { Badge } from "@/app/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from '@/app/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { Separator } from '../ui/separator';
import { useSession } from 'next-auth/react';

interface User {
    id: string;
    name: string | null;
    email: string;
    image?: string | null;
}

interface Step {
    id: string;
    type: string;
    assignedTo: User;
}

interface ContentAssignmentViewProps {
    assignedTo?: User | null;
    steps?: Step[];
}

export function ContentAssignmentView({ assignedTo, steps }: ContentAssignmentViewProps) {
    const { data: session } = useSession();

    const isCurrentUser = assignedTo?.id === session?.user?.id;
    const hasSteps = steps && steps.length > 0;

    if (!assignedTo && !hasSteps) {
        return (
            <div className="mt-3">
                <span className="text-sm text-muted-foreground">
                    Não atribuído
                </span>
            </div>
        );
    }

    const hasMultipleAssignees = hasSteps;

    const getInitials = (name: string | null) => {
        if (!name) return '??';
        return name
            .split(' ')
            .slice(0, 2)
            .map(n => n[0])
            .join('')
            .toUpperCase();
    };

    const getStepLabel = (type: string) => {
        const stepTypeMap: Record<string, string> = {
            'CAPTACAO': 'Captação',
            'DESIGN': 'Design',
            'EDICAO': 'Edição',
            'TRAFEGO': 'Tráfego',
        };
        return stepTypeMap[type] || type;
    };

    return (
        <div className="mt-3">
            <div className="flex flex-wrap gap-2 items-center">
                {assignedTo && (
                    <Badge variant="outline" className={isCurrentUser ? 'hidden' : 'flex items-center gap-2 px-2 py-1'}>
                        {isCurrentUser ? (
                            null
                        ) : (
                            <div className="flex items-center gap-1">
                                <Avatar className="h-4 w-4 border border-zinc-800 dark:border-zinc-200">
                                    <AvatarImage src={assignedTo.image || ''} alt={assignedTo.name || ''} />
                                    <AvatarFallback className="text-xs">{getInitials(assignedTo.name)}</AvatarFallback>
                                </Avatar>
                                <span className="text-xs">{assignedTo.name?.split(' ')[0]}</span>
                            </div>
                        )}
                    </Badge>
                )}

                {hasMultipleAssignees && (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <Badge variant="outline" className="flex items-start gap-1.5 bg-amber-50 text-amber-800 dark:bg-amber-950 dark:text-amber-300">
                                    <Users className="h-3 w-3" />
                                    <span className="text-xs">
                                        {assignedTo ? "+" : ""}{steps.length} {steps.length > 1 ? "responsáveis" : "responsável"}
                                    </span>
                                </Badge>
                            </TooltipTrigger>
                            <TooltipContent className="p-0 mx-2">
                                <div className="rounded-md p-4 shadow-md w-60">
                                    <h4 className="text-xs font-medium mb-2">
                                        {assignedTo ? "Responsáveis por etapas adicionais" : "Responsáveis por etapas"}
                                    </h4>
                                    <Separator className="my-2" />
                                    <div className="">
                                        {steps.map(step => (
                                            <div key={step.id} className="flex items-center gap-2">
                                                <span className="text-xs font-medium">{getStepLabel(step.type)}</span>
                                                <ArrowRight size={14} />
                                                <div className="flex items-center gap-1">
                                                    <span className="text-xs">{step.assignedTo?.name || 'Não atribuído'}</span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                )}
            </div>
        </div>
    );
}
