"use client"

import { useState, useEffect, useCallback } from 'react';
import { Ellipsis, Users } from 'lucide-react';
import { toast } from 'sonner';
import { Button } from '@/app/components/ui/button';
import {
    <PERSON><PERSON>,
    DialogContent,
    Di<PERSON>Header,
    Di<PERSON>Title,
    DialogTrigger,
} from '@/app/components/ui/dialog';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/app/components/ui/select';
import { Switch } from '@/app/components/ui/switch';
import { Label } from '@/app/components/ui/label';

interface UserData {
    id: string;
    name: string | null;
    email: string;
    role: string;
    image: string | null;
}

interface Step {
    type: string;
    assignedToId: string;
    assignedTo?: {
        id: string;
        name: string | null;
        email: string;
        image: string | null;
    };
}

const stepTypeOptions = [
    { value: "CAPTACAO", label: "Captação" },
    { value: "DESIGN", label: "Design" },
    { value: "EDICAO", label: "Ed<PERSON><PERSON>" },
    { value: "TRAFEGO", label: "Trá<PERSON>go" },
];

interface ContentStepsProps {
    contentId: string;
    onSuccess?: () => void;
}

export function ContentSteps({ contentId, onSuccess }: ContentStepsProps) {
    const [users, setUsers] = useState<UserData[]>([]);
    const [steps, setSteps] = useState<Step[]>([]);
    const [useMultipleSteps, setUseMultipleSteps] = useState<boolean>(false);
    const [selectedUserId, setSelectedUserId] = useState<string>('');
    const [isLoading, setIsLoading] = useState(false);
    const [isOpen, setIsOpen] = useState(false);

    const usersWithRole = users.filter(user => user.role !== "DEVELOPER" && user.role !== "VIEWER");

    const fetchUsers = useCallback(async () => {
        try {
            const response = await fetch('/api/users');
            if (response.ok) {
                const data = await response.json();
                setUsers(data);
            } else {
                toast.error('Erro ao carregar usuários');
            }
        } catch (error) {
            console.error('Error fetching users:', error);
            toast.error('Erro ao carregar usuários');
        }
    }, []);

    const fetchContentSteps = useCallback(async () => {
        try {
            const contentResponse = await fetch(`/api/contents/${contentId}`);
            if (contentResponse.ok) {
                const content = await contentResponse.json();
                
                const stepsResponse = await fetch(`/api/content-steps?contentId=${contentId}`);
                if (stepsResponse.ok) {
                    const stepsData = await stepsResponse.json();
                    if (stepsData && stepsData.length > 0) {
                        setSteps(stepsData);
                        setUseMultipleSteps(true);
                        setSelectedUserId('');
                    } else if (content.assignedToId) {
                        setUseMultipleSteps(false);
                        setSelectedUserId(content.assignedToId);
                        setSteps(stepTypeOptions.map(opt => ({
                            type: opt.value,
                            assignedToId: ''
                        })));
                    } else {
                        setSteps(stepTypeOptions.map(opt => ({
                            type: opt.value,
                            assignedToId: ''
                        })));
                        setUseMultipleSteps(false);
                        setSelectedUserId('');
                    }
                }
            }
        } catch (error) {
            console.error('Erro ao carregar informações do conteúdo:', error);
            toast.error('Erro ao carregar dados do conteúdo');
        }
    }, [contentId]);

    useEffect(() => {
        if (isOpen) {
            fetchUsers();
            fetchContentSteps();
        }
    }, [isOpen, fetchUsers, fetchContentSteps]);

    const handleSave = async () => {
        setIsLoading(true);

        try {
            if (useMultipleSteps) {
                const validSteps = steps.filter(step => step.assignedToId);
                if (validSteps.length === 0) {
                    toast.error('Atribua responsáveis às etapas');
                    setIsLoading(false);
                    return;
                }

                await fetch(`/api/contents/${contentId}/assign`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ userId: null }),
                });
                
                const stepsResponse = await fetch('/api/content-steps', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contentId,
                        steps: validSteps
                    }),
                });
                
                if (!stepsResponse.ok) {
                    const errorData = await stepsResponse.json();
                    throw new Error(errorData.message || 'Erro ao salvar etapas');
                }
            } else {
                if (!selectedUserId) {
                    toast.error('Selecione um responsável');
                    setIsLoading(false);
                    return;
                }

                await fetch('/api/content-steps', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contentId,
                        steps: []
                    }),
                });

                const assignResponse = await fetch(`/api/contents/${contentId}/assign`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ userId: selectedUserId }),
                });
                
                if (!assignResponse.ok) {
                    const errorData = await assignResponse.json();
                    throw new Error(errorData.message || 'Erro ao atribuir responsável único');
                }
            }

            toast.success('Responsáveis atualizados com sucesso');
            setIsOpen(false);
            if (onSuccess) {
                onSuccess();
            }
        } catch (error) {
            console.error('Erro ao atualizar responsáveis:', error);
            toast.error('Erro ao atualizar responsáveis');
        } finally {
            setIsLoading(false);
        }
    };

    const updateStepAssignee = (type: string, userId: string) => {
        setSteps(current =>
            current.map(step =>
                step.type === type ? { ...step, assignedToId: userId } : step
            )
        );
    };

    const getStepLabel = (type: string) => {
        const option = stepTypeOptions.find(opt => opt.value === type);
        return option ? option.label : type;
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="icon">
                    <Users size={16} />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
                <DialogHeader>
                    <DialogTitle>Gerenciar responsáveis</DialogTitle>
                </DialogHeader>

                <div className="space-y-4 py-4">
                    <div className="flex items-center justify-between">
                        <Label htmlFor="useMultipleSteps" className="font-medium">
                            Atribuir por etapas
                        </Label>
                        <Switch
                            id="useMultipleSteps"
                            checked={useMultipleSteps}
                            onCheckedChange={setUseMultipleSteps}
                        />
                    </div>

                    {!useMultipleSteps ? (
                        <div className="space-y-2">
                            <Label htmlFor="assignedTo">Responsável único</Label>
                            <Select
                                value={selectedUserId}
                                onValueChange={setSelectedUserId}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Selecione um responsável" />
                                </SelectTrigger>
                                <SelectContent>
                                    {usersWithRole.map((user) => (
                                        <SelectItem key={user.id} value={user.id}>
                                            {user.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    ) : (
                        <div className="space-y-3">
                            {steps.map((step) => (
                                <div key={step.type} className="space-y-2">
                                    <Label htmlFor={`step-${step.type}`}>{getStepLabel(step.type)}</Label>
                                    <Select
                                        value={step.assignedToId}
                                        onValueChange={(value) => updateStepAssignee(step.type, value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Selecione um responsável" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {usersWithRole.map((user) => (
                                                <SelectItem key={user.id} value={user.id}>
                                                    {user.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancelar
                    </Button>
                    <Button onClick={handleSave} disabled={isLoading}>
                        {isLoading ? <Ellipsis /> : null}
                        Salvar
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}
