import { useState, useEffect } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Client } from "@prisma/client";
import { toast } from "sonner";
import { Ellipsis } from "lucide-react";
import { useRouter } from "next/navigation";

interface CreatePlanningProps {
    clientId?: string;
    clientName?: string;
    selectedMonth?: string | null;
    selectedMonthName?: string;
    onClose?: () => void;
    isShowTitle?: boolean;
    onSuccess?: () => void;
}

export const CreatePlanning = ({
    clientId: initialClientId,
    clientName,
    selectedMonth,
    selectedMonthName,
    onClose,
    isShowTitle,
    onSuccess: onSuccess
}: CreatePlanningProps) => {
    const [clientId, setClientId] = useState(initialClientId || "");
    const [month, setMonth] = useState(selectedMonth ?
        (Array.from({ length: 12 }, (_, i) => new Date(0, i).toLocaleString("pt-BR", { month: "long" }).toLowerCase())
            .findIndex(m => m === selectedMonth) + 1).toString()
        : "");
    const [year, setYear] = useState(new Date().getFullYear().toString());
    const [clients, setClients] = useState<Client[]>();
    const [isLoading, setIsLoading] = useState(true);
    const router = useRouter();

    const isClientDisabled = !!initialClientId;
    const isMonthDisabled = !!selectedMonth;

    useEffect(() => {
        const fetchClients = async () => {
            const res = await fetch("/api/clients");
            const data = await res.json();
            setClients(data);
            setIsLoading(false);
        };
        fetchClients();
    }, []);

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();

        if (clientId === "" || month === "" || year === "") {
            toast.error("Preencha todos os campos!");
            return;
        }

        try {
            setIsLoading(true);

            const checkResponse = await fetch(`/api/plannings?clientId=${clientId}&month=${month}&year=${year}`);
            const existingData = await checkResponse.json();

            if (existingData && existingData.length > 0) {
                toast.error("Já existe um planejamento para este cliente neste mês e ano");
                return;
            }

            const payload = { clientId, month: Number(month), year: Number(year) };
            const response = await fetch("/api/plannings", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload),
            });

            if (response.ok) {
                toast("Planejamento criado com sucesso!", {
                    description: "Clique no botão para visualizar o planejamento.",
                    duration: 5000,
                    action: {
                        label: "Ver cliente",
                        onClick: () => {
                            router.push(`/monthly-planning/${clientId}`);
                        },
                    },
                });
                onSuccess?.();
                onClose?.();
            } else {
                const errorData = await response.json().catch(() => null);
                const errorMessage = errorData?.message || "Erro ao criar planejamento!";
                toast.error(errorMessage);
            }
        } catch (error) {
            console.error("Erro ao criar planejamento:", error);
            toast.error("Erro ao criar planejamento!");
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        onClose?.();
    };

    return (
        <div className="mt-2 max-w-4xl">
            {isShowTitle && (
                <p className="underline underline-offset-4 mb-4 flex items-end gap-2">
                    Novo planejamento
                </p>
            )}
            <div>
                <form onSubmit={handleSubmit} className="space-y-4">
                    {isClientDisabled ? (
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Cliente</label>
                            <div className="p-2 border rounded-md bg-zinc-100 text-zinc-800 dark:bg-zinc-900 dark:text-zinc-500">{clientName}</div>
                            <Input type="hidden" value={clientId} />
                        </div>
                    ) : (
                        <Select onValueChange={setClientId} value={clientId} disabled={isClientDisabled}>
                            <SelectTrigger>
                                <SelectValue placeholder={isLoading ? <Ellipsis /> : "Selecione um cliente"} />
                            </SelectTrigger>
                            <SelectContent>
                                {clients?.map((client) => (
                                    <SelectItem
                                        key={client.id}
                                        value={client.id}
                                    >
                                        {client.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    )}

                    {isMonthDisabled ? (
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Mês</label>
                            <div className="p-2 border rounded-md bg-zinc-100 text-zinc-800 dark:bg-zinc-900 dark:text-zinc-500">
                                {selectedMonthName || selectedMonth}
                            </div>
                            <Input type="hidden" value={month} />
                        </div>
                    ) : (
                        <Select onValueChange={setMonth} value={month} disabled={isMonthDisabled}>
                            <SelectTrigger>
                                <SelectValue placeholder="Selecione um mês" />
                            </SelectTrigger>
                            <SelectContent>
                                {Array.from({ length: 12 }, (_, i) => (
                                    <SelectItem key={i + 1} value={(i + 1).toString()}>
                                        {new Date(0, i).toLocaleString("pt-BR", { month: "long" })}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    )}

                    <Input
                        type="number"
                        placeholder="Ano"
                        value={year}
                        onChange={(e) => setYear(e.target.value)}
                    />
                    <div className="flex flex-col xs:flex-row items-center gap-4 xsgap-2 p-0">
                        <Button type="button" variant="secondary" className="w-full flex-1" onClick={handleCancel}>
                            Cancelar
                        </Button>
                        <Button
                            type="submit"
                            className="w-full flex-1"
                            disabled={isLoading}
                        >
                            {isLoading ? <Ellipsis /> : "Criar"}
                        </Button>
                    </div>
                </form>
            </div>
        </div>
    );
};
