import { CirclePlus } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { DialogTrigger } from "../ui/dialog";

export const OpenDialogButton = ({ onClick }: { onClick?: () => void }) => {
    return (
        <DialogTrigger asChild>
            <Button variant="outline" size="icon" onClick={onClick}>
                <CirclePlus size={16} />
            </Button>
        </DialogTrigger>
    );
};
