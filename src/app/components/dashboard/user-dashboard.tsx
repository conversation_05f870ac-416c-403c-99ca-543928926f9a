"use client";

import { Card, CardContent } from "../ui/card";
import { ChartNoAxesGantt, Grid3x3, ListTodo } from "lucide-react";
import { UserDemandsStats } from "../user-demands-stats";
import { UserLatestDemands } from "../user-latest-demands";
import { FeedStructuringTasks } from "../feed-structuring-tasks";

export function UserDashboard() {
  return (
    <div className="space-y-6 mt-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <Card className="px-6 py-5">
          <CardContent className="m-0 p-0">
            <div className="flex gap-2 items-center mb-2">
              <span className="p-1 rounded bg-orange-100 dark:bg-orange-950">
                <ChartNoAxesGantt size={18} className="text-primary2" />
              </span>
              <p className="text-sm font-medium">Estatísticas de demandas do mês</p>
            </div>
            <UserDemandsStats />
          </CardContent>
        </Card>

        <Card className="px-6 py-5">
          <CardContent className="m-0 p-0">
            <div className="flex gap-2 items-center">
              <span className="p-1 rounded bg-orange-100 dark:bg-orange-950">
                <Grid3x3 size={18} className="text-primary2" />
              </span>
              <p className="text-sm font-medium">Estruturação do Feed</p>
            </div>
            <FeedStructuringTasks />
          </CardContent>
        </Card>
      </div>

      <Card className="px-6 py-5">
        <CardContent className="m-0 p-0">
          <div className="flex gap-2 items-center">
            <span className="p-1 rounded bg-orange-100 dark:bg-orange-950">
              <ListTodo size={18} className="text-primary2" />
            </span>
            <p className="text-sm font-medium">Minhas demandas recentes</p>
          </div>
          <UserLatestDemands />
        </CardContent>
      </Card>
    </div>
  );
}

