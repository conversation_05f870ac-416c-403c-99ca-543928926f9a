"use client";

import { useEffect, useState } from "react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import { Check } from 'lucide-react';
import { Toggle } from "@/app/components/ui/toggle";
import { Checkbox } from "@/app/components/ui/checkbox";
import { Ellipsis } from "lucide-react";
import { toast } from "sonner";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from "@/app/components/ui/select";
import RenderDeliveryItem from "./render-delivery-item";
import { ActivityInfo } from "../activity-info";
import { AddFeedUrlModal } from "../add-feed-url-modal";
import { Separator } from "../ui/separator";
import { Label } from "../ui/label";
import { useSession } from "next-auth/react";
import EditCaption from "../edit-caption";
import { AddContentReview } from "../add-content-review";


interface Content {
    id: string;
    activityDate: string;
    contentType: string;
    channel: string;
    details: string;
    destination: string;
    caption?: string;
    status?: string;
    copywriting?: string;
    reference?: string;
    urlStructuringFeed?: string | string[];
    urlTypes?: string[];
    urlMediaTypes?: string[];
    urlThumbnails?: string[];
    review?: string;
    reviewedBy?: { id?: string; name?: string; email?: string; image?: string };

    urlFolder?: string;
    approved?: boolean;
}

interface WeeklyActivity {
    id: string;
    description: string;
    week: number;
    contents: Content[];
}

interface MonthlyPlanning {
    status: string;
    id: string;
    month: number;
    year: number;
    activities: WeeklyActivity[];
}

interface ClientProps {
    id: string;
    name: string;
    instagramUsername?: string | null;
    [key: string]: unknown;
}

interface DeliveriesContentProps {
    clientId: string;
    refreshTrigger?: number;
    client?: ClientProps | null;
    initialMonth?: string;
    compactView?: boolean;
    initialCaptionFilter?: "todos" | "com-legenda" | "sem-legenda";
    onSelectionChange?: (selectedIds: string[]) => void;
}

export const DeliveriesContent = ({
    clientId,
    refreshTrigger = 0,
    initialMonth,
    compactView = false,
    initialCaptionFilter = "todos",
    onSelectionChange,
}: DeliveriesContentProps) => {
    const [plannedContents, setPlannedContents] = useState<Array<Content & { planningId: string, week: number, month: number, year: number }>>([]);
    const [filteredContents, setFilteredContents] = useState<Array<Content & { planningId: string, week: number, month: number, year: number }>>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [availableMonths, setAvailableMonths] = useState<string[]>([]);
    const [selectedMonth, setSelectedMonth] = useState<string | null>(initialMonth || null);
    const [captionFilter, setCaptionFilter] = useState<"todos" | "com-legenda" | "sem-legenda">(initialCaptionFilter);
    const [photoFilter, setPhotoFilter] = useState<"todos" | "com-foto" | "sem-foto">("todos");
    const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
    const [isBatchApproving, setIsBatchApproving] = useState(false);
    const { data: session } = useSession();

    const isAdmin = session?.user.role === "ADMIN" || session?.user.role === "DEVELOPER" || session?.user.role === "GENERAL_ASSISTANT";

    const toggleItemSelection = (itemId: string) => {
        setSelectedItems(prev => {
            const newSet = new Set(prev);
            if (newSet.has(itemId)) {
                newSet.delete(itemId);
            } else {
                newSet.add(itemId);
            }
            return newSet;
        });
    };

    useEffect(() => {
        if (onSelectionChange) {
            try {
                onSelectionChange(Array.from(selectedItems));
            } catch (e) {
                console.error('onSelectionChange error', e);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [selectedItems]);

    const toggleSelectAll = () => {
        if (selectedItems.size === filteredContents.length) {
            setSelectedItems(new Set());
        } else {
            setSelectedItems(new Set(filteredContents.map(content => content.id)));
        }
    };

    const clearSelection = () => {
        setSelectedItems(new Set());
    };

    const approveSelected = async () => {
        if (selectedItems.size === 0) return;
        if (!isAdmin) {
            toast.error('Não autorizado');
            return;
        }

        setIsBatchApproving(true);
        const ids = Array.from(selectedItems);
        try {
            const results = await Promise.all(ids.map(async (id) => {
                const resp = await fetch(`/api/contents/${id}`, {
                    method: 'PATCH',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ approved: true }),
                });
                const body = await resp.json().catch(() => null);
                return { id, ok: resp.ok, status: resp.status, body };
            }));

            const succeeded = results.filter(r => r.ok).map(r => r.id);
            const failed = results.filter(r => !r.ok);

            if (succeeded.length > 0) {
                setPlannedContents(prev => prev.map(c => succeeded.includes(c.id) ? { ...c, approved: true } : c));
                setFilteredContents(prev => prev.map(c => succeeded.includes(c.id) ? { ...c, approved: true } : c));
                toast.success(`${succeeded.length} conteúdo(s) marcado(s) como aprovado(s)`);
            }

            if (failed.length > 0) {
                console.error('Falha ao aprovar alguns conteúdos', failed);
                toast.error(`${failed.length} falha(ram) ao aprovar`);
            }

            setSelectedItems(prev => {
                const newSet = new Set(prev);
                succeeded.forEach(id => newSet.delete(id));
                return newSet;
            });
        } catch (e) {
            console.error('Erro ao aprovar selecionados', e);
            toast.error('Erro ao aprovar selecionados');
        } finally {
            setIsBatchApproving(false);
        }
    };

    useEffect(() => {
        if (!clientId) return;

        const fetchData = async () => {
            try {
                setIsLoading(true);
                const response = await fetch(`/api/clients/${clientId}?include=monthlyPlannings.activities.contents`);

                if (!response.ok) {
                    throw new Error(`Erro ao buscar dados do cliente: ${response.status}`);
                }

                const data = await response.json();

                const allPlannedContents: Array<Content & { planningId: string, week: number, month: number, year: number }> = [];
                const monthsSet = new Set<string>();

                if (data.monthlyPlannings && Array.isArray(data.monthlyPlannings)) {
                    data.monthlyPlannings.forEach((planning: MonthlyPlanning) => {
                        let hasPlannedContent = false;
                        const monthKey = `${planning.month}-${planning.year}`;

                        if (planning.status === "aprovado") {
                            planning.activities.forEach((activity: WeeklyActivity) => {
                                if (activity.contents && Array.isArray(activity.contents)) {
                                    activity.contents.forEach((content: Content) => {
                                        allPlannedContents.push({
                                            ...content,
                                            planningId: planning.id,
                                            week: activity.week,
                                            month: planning.month,
                                            year: planning.year
                                        });
                                        hasPlannedContent = true;
                                    });
                                }
                            });

                            if (hasPlannedContent) {
                                monthsSet.add(monthKey);
                            }
                        }
                    });
                }

                const sortedMonths = Array.from(monthsSet).sort((a, b) => {
                    const [monthA, yearA] = a.split('-').map(Number);
                    const [monthB, yearB] = b.split('-').map(Number);

                    if (yearA !== yearB) return yearB - yearA;
                    return monthB - monthA;
                });

                setAvailableMonths(sortedMonths);
                setPlannedContents(allPlannedContents);

                if (!selectedMonth && sortedMonths.length > 0) {
                    setSelectedMonth(sortedMonths[0]);
                }

                if (selectedMonth && !sortedMonths.includes(selectedMonth) && sortedMonths.length > 0) {
                    setSelectedMonth(sortedMonths[0]);
                }
            } catch (error) {
                console.error("Erro ao buscar dados do cliente:", error);
                toast.error("Erro ao carregar dados do cliente");
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [clientId, refreshTrigger, selectedMonth]);

    useEffect(() => {
        if (selectedMonth) {
            const [month, year] = selectedMonth.split('-').map(Number);

            let filtered = plannedContents.filter(content =>
                content.month === month && content.year === year
            );

            if (captionFilter === "com-legenda") {
                filtered = filtered.filter(content => !!content.caption);
            } else if (captionFilter === "sem-legenda") {
                filtered = filtered.filter(content => !content.caption);
            }

            if (photoFilter === "com-foto") {
                filtered = filtered.filter(content => content.urlStructuringFeed && content.urlStructuringFeed.length > 0);
            } else if (photoFilter === "sem-foto") {
                filtered = filtered.filter(content => !content.urlStructuringFeed || content.urlStructuringFeed.length === 0);
            }

            const sorted = [...filtered].sort((a, b) => {
                return new Date(a.activityDate).getTime() - new Date(b.activityDate).getTime();
            });

            setFilteredContents(sorted);
            setSelectedItems(new Set());
        } else {
            setFilteredContents([]);
            setSelectedItems(new Set());
        }
    }, [selectedMonth, plannedContents, captionFilter, photoFilter]);

    const handleUrlUpdate = (contentId: string, newUrls: string[], newUrlTypes: string[], newMediaTypes?: string[], newUrlFolder?: string, newUrlThumbnails?: string[]) => {
        setPlannedContents(prevContents =>
            prevContents.map(content =>
                content.id === contentId
                    ? {
                        ...content,
                        urlStructuringFeed: newUrls.length > 0 ? newUrls : undefined,
                        urlTypes: newUrlTypes.length > 0 ? newUrlTypes : undefined,
                        urlMediaTypes: newMediaTypes && newMediaTypes.length > 0 ? newMediaTypes : undefined,
                        urlThumbnails: newUrlThumbnails && newUrlThumbnails.length > 0 ? newUrlThumbnails : undefined,
                        urlFolder: newUrlFolder
                    }
                    : content
            )
        );

        setFilteredContents(prevContents =>
            prevContents.map(content =>
                content.id === contentId
                    ? {
                        ...content,
                        urlStructuringFeed: newUrls.length > 0 ? newUrls : undefined,
                        urlTypes: newUrlTypes.length > 0 ? newUrlTypes : undefined,
                        urlMediaTypes: newMediaTypes && newMediaTypes.length > 0 ? newMediaTypes : undefined,
                        urlThumbnails: newUrlThumbnails && newUrlThumbnails.length > 0 ? newUrlThumbnails : undefined,
                        urlFolder: newUrlFolder
                    }
                    : content
            )
        );
    };
    const handleReviewUpdate = (contentId: string, newReview: string) => {
        setPlannedContents(prev => prev.map(c => c.id === contentId ? { ...c, review: newReview } : c));
        setFilteredContents(prev => prev.map(c => c.id === contentId ? { ...c, review: newReview } : c));
    };

    const handleStatusUpdate = (contentId: string, newStatus: string) => {
        setPlannedContents(prev => prev.map(c => c.id === contentId ? { ...c, status: newStatus } : c));
        setFilteredContents(prev => prev.map(c => c.id === contentId ? { ...c, status: newStatus } : c));
    };


    const renderContent = (content: Content & { planningId: string, week: number, month: number, year: number }) => {
        const formattedDate = format(new Date(content.activityDate), 'dd/MM/yyyy', { locale: ptBR });

        return (
            <Card key={content.id} className="flex flex-col h-full relative">
                <div className="px-3 mt-2 flex items-center justify-between">
                    <Checkbox
                        checked={selectedItems.has(content.id)}
                        onCheckedChange={() => toggleItemSelection(content.id)}
                        className="hidden lg:block"
                    />
                    <span className="font-medium text-xs text-muted-foreground">#{content.id.substring(0, 8).toUpperCase()}</span>
                </div>
                <CardHeader className="p-3">
                    <div className="flex flex-col justify-between gap-2">
                        <div className="flex items-center gap-2">
                            <h3 className="font-medium text-base">{content.contentType}</h3>
                            <Badge variant="outline" className="font-medium text-xs px-2.5 py-1">
                                {formattedDate}
                            </Badge>
                            <Badge variant="secondary" className="font-medium">{content.destination}</Badge>
                        </div>
                        <div className="flex items-center justify-between gap-2">
                            <div className="flex gap-2 items-center flex-wrap">
                                <ActivityInfo
                                    activity={{
                                        ...content,
                                        url: Array.isArray(content.urlStructuringFeed)
                                            ? content.urlStructuringFeed
                                            : content.urlStructuringFeed
                                                ? [content.urlStructuringFeed]
                                                : undefined
                                    }}
                                    localCaption={content.caption}
                                    localCopywriting={content.copywriting}
                                    localReference={content.reference}
                                />
                                <AddFeedUrlModal
                                    contentId={content.id}
                                    currentUrl={content.urlStructuringFeed}
                                    currentUrlTypes={content.urlTypes}
                                    currentMediaTypes={content.urlMediaTypes}
                                    currentUrlThumbnails={content.urlThumbnails}
                                    currentUrlFolder={content.urlFolder}
                                    activityDate={content.activityDate}
                                    onUrlUpdated={handleUrlUpdate}
                                    type="content"
                                />
                                <EditCaption
                                    contentId={content.id}
                                    initialCaption={content.caption}
                                    onSaved={(newCaption) => {
                                        setPlannedContents(prev => prev.map(p => p.id === content.id ? { ...p, caption: newCaption } : p));
                                        setFilteredContents(prev => prev.map(p => p.id === content.id ? { ...p, caption: newCaption } : p));
                                    }}
                                    size="icon"
                                />
                                <AddContentReview
                                    size="icon"
                                    witdh="w-9"
                                    contentId={content.id}
                                    disabled={content.status === 'aprovado' || content.status === 'concluído'}
                                    isClient
                                    currentReview={content.review || ''}
                                    reviewedBy={content.reviewedBy}
                                    onReviewUpdate={(newReview) => handleReviewUpdate(content.id, newReview)}
                                    type="content"
                                    onStatusUpdate={(newStatus) => handleStatusUpdate(content.id, newStatus)}
                                />
                            </div>
                            <Toggle
                                disabled={!isAdmin}
                                className={content.approved ? '' : 'bg-zinc-200 text-zinc-700 border border-zinc-400 dark:bg-zinc-700 dark:text-gray-300'}
                                pressed={!!content.approved}
                                onPressedChange={async (pressed) => {
                                    try {
                                        const target = Boolean(pressed);
                                        const resp = await fetch(`/api/contents/${content.id}`, {
                                            method: 'PATCH',
                                            headers: { 'Content-Type': 'application/json' },
                                            body: JSON.stringify({ approved: target }),
                                        });
                                        if (!resp.ok) {
                                            const err = await resp.json().catch(() => null);
                                            throw new Error(err?.message || 'Erro ao atualizar aprovação do conteúdo');
                                        }

                                        setPlannedContents(prev => prev.map(c => c.id === content.id ? { ...c, approved: target } : c));
                                        setFilteredContents(prev => prev.map(c => c.id === content.id ? { ...c, approved: target } : c));
                                        toast.success(target ? 'Conteúdo aprovado' : 'Aprovação removida');
                                    } catch (e) {
                                        console.error('Erro ao atualizar aprovação do conteúdo', e);
                                        toast.error(e instanceof Error ? e.message : 'Erro ao atualizar aprovação do conteúdo');
                                    }
                                }}
                                aria-label={content.approved ? 'Desaprovar conteúdo' : 'Aprovar conteúdo'}
                                title={content.approved ? 'Aprovado' : 'Aprovar conteúdo'}
                            >
                                {content.approved ? (
                                    <div className="text-green-400 bg-green-400/10 dark:bg-green-400/20 rounded-full">
                                        <Check />
                                    </div>
                                ) : (
                                    <span className="text-xs">
                                        Aprovar
                                    </span>
                                )}
                            </Toggle>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="py-2 px-3 flex-grow">
                    <RenderDeliveryItem content={content} compactView={compactView} />
                </CardContent>
                <CardFooter className="px-3 py-3 border-t">
                    <div className="w-full">
                        <h4 className="text-sm font-semibold text-gray-800 dark:text-white mb-2">Legenda:</h4>
                        {content.caption ? (
                            <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line break-words leading-relaxed">{content.caption}</p>
                        ) : (
                            <p className="text-sm text-gray-500 dark:text-gray-400 italic">Sem legenda disponível</p>
                        )}
                    </div>
                </CardFooter>
            </Card>
        );
    };

    return (
        <Card className="w-full">
            <CardHeader className="px-6 py-3 sticky top-0 z-10 bg-background dark:bg-background rounded-t-xl">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
                    <div className="flex flex-1 w-full">
                        <div className="flex flex-wrap gap-2 items-start sm:items-center w-full">
                            {availableMonths.length > 0 && (
                                <Select
                                    value={selectedMonth || undefined}
                                    onValueChange={setSelectedMonth}
                                >
                                    <SelectTrigger className="w-full sm:w-[180px]">
                                        <SelectValue placeholder="Selecione o mês" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {availableMonths.map(month => {
                                            const [m, y] = month.split('-');
                                            const monthName = format(new Date(Number(y), Number(m) - 1, 1), 'MMMM', { locale: ptBR });
                                            const formattedMonth = `${monthName.charAt(0).toUpperCase() + monthName.slice(1)}/${y}`;

                                            return (
                                                <SelectItem key={month} value={month}>
                                                    {formattedMonth}
                                                </SelectItem>
                                            );
                                        })}
                                    </SelectContent>
                                </Select>
                            )}

                            <Select
                                value={photoFilter}
                                onValueChange={(value) => setPhotoFilter(value as "todos" | "com-foto" | "sem-foto")}
                            >
                                <SelectTrigger className="w-full sm:w-[180px]">
                                    <SelectValue placeholder="Filtrar por foto" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="todos">Com e sem foto</SelectItem>
                                    <SelectItem value="com-foto">Com foto</SelectItem>
                                    <SelectItem value="sem-foto">Sem foto</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select
                                value={captionFilter}
                                onValueChange={(value) => setCaptionFilter(value as "todos" | "com-legenda" | "sem-legenda")}
                            >
                                <SelectTrigger className="w-full sm:w-[180px]">
                                    <SelectValue placeholder="Filtrar por legenda" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="todos">Com e sem legenda</SelectItem>
                                    <SelectItem value="com-legenda">Com legenda</SelectItem>
                                    <SelectItem value="sem-legenda">Sem legenda</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="hidden lg:flex items-center gap-2 mt-2 md:mt-0">
                        {filteredContents.length > 0 && (
                            <div className="flex flex-col sm:flex-row items-center gap-2">
                                <div className="flex items-center space-x-2">
                                    <Checkbox
                                        id="select-all"
                                        checked={selectedItems.size === filteredContents.length && filteredContents.length > 0}
                                        onCheckedChange={toggleSelectAll}
                                    />
                                    <Label
                                        htmlFor="select-all"
                                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                    >
                                        Selecionar todos ({selectedItems.size}/{filteredContents.length})
                                    </Label>
                                </div>
                                {selectedItems.size > 0 && (
                                    <>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={clearSelection}
                                        >
                                            Limpar seleção
                                        </Button>
                                        <Button
                                            variant="default"
                                            size="sm"
                                            onClick={approveSelected}
                                            disabled={!isAdmin || isBatchApproving}
                                        >
                                            {isBatchApproving ? 'Aprovando' : 'Marcar como aprovado'}
                                        </Button>
                                    </>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </CardHeader>

            <CardContent className="pt-4">
                {filteredContents.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {filteredContents.map(content => renderContent(content))}
                    </div>
                ) : (
                    <div className="text-center text-sm">
                        {isLoading ? (
                            <Ellipsis />
                        ) : (
                            <div className="pb-8">
                                <Separator className="my-6" />
                                <p className="mt-8">Nenhuma entrega encontrada. Altere os filtros de busca.</p>
                            </div>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
    );
};
