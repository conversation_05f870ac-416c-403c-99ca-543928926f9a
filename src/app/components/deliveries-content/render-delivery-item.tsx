"use client";

import Image from 'next/image';
import { useEffect, useMemo, useState } from 'react';
import { Button } from '@/app/components/ui/button';
import { Download } from 'lucide-react';
import DeliveryDownloadModal from '@/app/components/delivery-download-modal';

interface DeliveryContent {
    id: string;
    contentType?: string;
    details?: string;
    urlStructuringFeed?: string | string[];
    urlTypes?: string[];
    urlMediaTypes?: string[];
    urlThumbnails?: string[];
    activityDate: string | Date;
    week: number;
    month?: number;
    year?: number;
    status?: string;
    planningId?: string;
    caption?: string;
    currentUrlIndex?: number;
}

export const extractGoogleDriveId = (url: string): string | null => {
    if (!url) return null;
    try {
        const u = new URL(url);
        const host = u.hostname.toLowerCase();
        if (!host.includes('drive.google.com') && !host.includes('docs.google.com') && !host.includes('googleusercontent.com')) {
            return null;
        }
        const match1 = url.match(/\/d\/([^/]+)/);
        if (match1) return match1[1];
        const match2 = url.match(/id=([^&]+)/);
        if (match2) return match2[1];
        return null;
    } catch {
        return null;
    }
};

const encodeKeySegments = (key: string) => key.split('/').map(encodeURIComponent).join('/')

const getDisplaySrc = (source?: string) => {
    if (!source) return '/images/placeholder.png'
    try {
        if (source.startsWith('data:')) return source
        if (/^(https?:)?\/\//i.test(source)) return source

        const publicBase = process.env.NEXT_PUBLIC_S3_BASE_URL
        if (publicBase) {
            return `${publicBase.replace(/\/$/, '')}/${encodeKeySegments(source)}`
        }

        const bucket = process.env.NEXT_PUBLIC_S3_BUCKET
        if (bucket) {
            return `https://${bucket}.s3.amazonaws.com/${encodeKeySegments(source)}`
        }

        return source
    } catch {
        return source
    }
}

const presignedCache = new Map<string, string>();

export const RenderDeliveryItem = ({
    content,
    compactView = false,
}: {
    content: DeliveryContent;
    compactView?: boolean;
}) => {
    const getAllUrls = (urls: string | string[] | undefined): string[] => {
        if (!urls) return [];
        return Array.isArray(urls) ? urls.filter(url => url && typeof url === 'string' && url.trim() !== '') : [urls];
    };

    const allUrls = useMemo(() => getAllUrls(content.urlStructuringFeed), [content.urlStructuringFeed]);

    const getSourceAt = (index: number): string | undefined => {
        try {
            const thumbnails = content.urlThumbnails;
            const mediaTypes = content.urlMediaTypes;
            if (Array.isArray(thumbnails) && thumbnails[index]) return thumbnails[index];
            if (Array.isArray(mediaTypes) && mediaTypes[index] === 'video' && Array.isArray(thumbnails) && thumbnails[index]) return thumbnails[index];
        } catch { }

        return allUrls[index];
    };

    const [localPresignedMap, setLocalPresignedMap] = useState<Record<string, string>>({});
    const [downloadModalOpen, setDownloadModalOpen] = useState(false);


    useEffect(() => {
        const keysToFetch: string[] = [];
        // include thumbnails in the prefetch list so posters (thumbnails) resolve quickly
        const thumbs = Array.isArray(content.urlThumbnails) ? content.urlThumbnails.filter(Boolean) as string[] : [];
        const candidates = allUrls.concat(thumbs);

        candidates.forEach((src) => {
            if (!src) return;
            if (src.startsWith('data:') || /^(https?:)?\/\//i.test(src)) return;
            if (extractGoogleDriveId(src)) return;
            const key = src;
            if (!presignedCache.has(key)) keysToFetch.push(key);
            else {
                setLocalPresignedMap(prev => ({ ...prev, [key]: presignedCache.get(key)! }));
            }
        });

        if (keysToFetch.length === 0) return;

        keysToFetch.forEach(async (key) => {
            try {
                const res = await fetch('/api/uploads/presigned-get', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ key }),
                });
                if (!res.ok) {
                    const txt = await res.text().catch(() => null);
                    console.debug('presigned-get non-ok', { key, status: res.status, text: txt });
                    return;
                }
                const data = await res.json().catch(() => null);
                if (!data?.url) {
                    console.debug('presigned-get missing url', { key, data });
                    return;
                }
                const presignedUrl: string = String(data.url);
                presignedCache.set(key, presignedUrl);
                setLocalPresignedMap(prev => ({ ...prev, [key]: presignedUrl }));
            } catch (e) {
                console.debug('presign fetch failed for', key, e);
            }
        });
    }, [allUrls, content.urlThumbnails]);

    const selectSrc = (source?: string) => {
        if (!source) return null;
        if (source.startsWith('data:')) return source;
        const driveId = extractGoogleDriveId(source);
        if (driveId) return `/api/drive-proxy?id=${driveId}&quality=high&size=large`;
        if (/^(https?:)?\/\//i.test(source)) return source;
        const cached = localPresignedMap[source] || presignedCache.get(source);
        if (cached) return cached;

        const publicBase = process.env.NEXT_PUBLIC_S3_BASE_URL;
        const bucket = process.env.NEXT_PUBLIC_S3_BUCKET;
        if (publicBase || bucket) {
            return getDisplaySrc(source);
        }

        // don't return raw relative key if we don't have a presigned/public url
        return null;
    };

    const isStoryVideoAt = (index?: number) => {
        if (index === undefined || index === null) return false;
        try {
            const mediaTypes = Array.isArray(content.urlMediaTypes) ? content.urlMediaTypes : undefined;
            const types = Array.isArray(content.urlTypes) ? content.urlTypes : undefined;
            const media = mediaTypes ? mediaTypes[index] : undefined;
            const type = types ? types[index] : undefined;
            if (!media || !type) return false;
            if (String(media).toLowerCase() !== 'video') return false;
            return /story/i.test(String(type));
        } catch {
            return false;
        }
    };

    // download handled by DeliveryDownloadModal; keep presign cache for previews

    if (content.currentUrlIndex !== undefined) {
        const currentUrl = getSourceAt(content.currentUrlIndex) || allUrls[content.currentUrlIndex];
        if (!currentUrl) {
            return (
                <div className={`text-amber-800 border border-dashed p-1 rounded text-xs text-center ${compactView ? 'h-[140px]' : 'h-[180px]'} flex items-center justify-center`}>
                    * URL pendente ou não é necessária
                </div>
            );
        }

    const src = isStoryVideoAt(content.currentUrlIndex) ? '/images/placeholder.jpg' : (selectSrc(currentUrl) || '/images/placeholder.png');
        try { console.debug('[RenderDeliveryItem] currentUrl resolved', { id: content.id, currentUrl, resolved: src, thumbnails: content.urlThumbnails }); } catch { }

        return (
            <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                <a href={currentUrl} target="_blank" rel="noopener noreferrer" style={{ display: 'block', width: '100%', height: '100%' }}>
                    <Image
                        src={src}
                        alt="Visualização da entrega"
                        width={1000}
                        height={1500}
                        className="object-contain max-w-full max-h-full"
                        loading="eager"
                        unoptimized={true}
                        style={{
                            width: 'auto',
                            height: 'auto',
                            maxWidth: '100%',
                            maxHeight: '100%',
                            objectPosition: 'center'
                        }}
                    />
                </a>
                <div style={{ position: 'absolute', top: 8, right: 8 }}>
                    <Button size="sm" variant="outline" onClick={() => setDownloadModalOpen(true)} title='Visualizar/baixar arquivos'>
                        <Download size={14} />
                    </Button>
                </div>
                <DeliveryDownloadModal open={downloadModalOpen} onOpenChange={setDownloadModalOpen} items={(() => {
                    // build items with keys and preview thumbs
                    const thumbs = Array.isArray(content.urlThumbnails) ? content.urlThumbnails : [];
                    const urls = Array.isArray(content.urlStructuringFeed) ? content.urlStructuringFeed : (content.urlStructuringFeed ? [content.urlStructuringFeed] : []);
                    const list: { key: string; label?: string; thumb?: string | null }[] = [];
                    urls.forEach((u, i) => {
                        const key = String(u);
                        const thumb = (thumbs && thumbs[i]) ? (selectSrc(thumbs[i]) || null) : (selectSrc(u) || null);
                        list.push({ key, label: undefined, thumb });
                    });
                    return list;
                })()} />
            </div>
        );
    }

    const firstUrl = getSourceAt(0) || allUrls[0];
    if (!firstUrl) {
        return (
            <div className="h-[400px] flex items-center justify-center bg-gray-100 dark:bg-zinc-800 text-gray-500 dark:text-zinc-400 text-sm p-4 rounded">
                Nenhuma imagem encontrada
            </div>
        );
    }

    const src = isStoryVideoAt(0) ? '/images/placeholder.jpg' : (selectSrc(firstUrl) || '/images/placeholder.png');
    try { console.debug('[RenderDeliveryItem] firstUrl resolved', { id: content.id, firstUrl, resolved: src, thumbnails: content.urlThumbnails }); } catch { }

    return (
        <div className={`flex flex-col ${compactView ? 'h-[350px]' : 'h-[400px]'}`}>
            <div className={`flex-1 bg-gray-100 rounded relative ${compactView ? 'h-[350px]' : 'h-[400px]'} flex items-center justify-center overflow-hidden`}>
                <Image
                    src={src}
                    alt="Visualização da entrega"
                    width={500}
                    height={625}
                    className="max-w-full max-h-full object-contain"
                    loading="lazy"
                    unoptimized={true}
                    style={{
                        width: 'auto',
                        height: 'auto',
                        maxWidth: '100%',
                        maxHeight: '100%',
                        objectPosition: 'center'
                    }}
                    onError={(e) => {
                        const firstCandidate = getSourceAt(0) || (Array.isArray(content.urlStructuringFeed) ? content.urlStructuringFeed[0] : content.urlStructuringFeed);
                        const driveId = firstCandidate ? extractGoogleDriveId(String(firstCandidate)) : null;
                        const diagnostic = {
                            firstCandidate: firstCandidate ? String(firstCandidate) : null,
                            driveId,
                            computedSrc: src ? String(src) : null,
                            contentUrlStructuringFeed: content.urlStructuringFeed,
                            imgElementSrc: (e?.currentTarget as HTMLImageElement)?.src ?? null,
                        };

                        try {
                            console.error('Erro ao carregar imagem normal:', JSON.stringify(diagnostic));
                        } catch {
                            console.error('Erro ao carregar imagem normal (fallback):', diagnostic);
                        }

                        void (async () => {
                            if (driveId) {
                                try {
                                    const proxyUrl = `/api/drive-proxy?id=${driveId}&quality=high&size=large`;
                                    const controller2 = new AbortController();
                                    const timer2 = setTimeout(() => controller2.abort(), 5000);
                                    const resp2 = await fetch(proxyUrl, { method: 'GET', signal: controller2.signal });
                                    clearTimeout(timer2);
                                    console.debug('diagnostic drive-proxy fetch', {
                                        proxyUrl,
                                        ok: resp2.ok,
                                        status: resp2.status,
                                        statusText: resp2.statusText,
                                        headers: {
                                            'content-type': resp2.headers.get('content-type'),
                                            'x-cache': resp2.headers.get('x-cache')
                                        }
                                    });
                                } catch (fetchErr2) {
                                    console.debug('diagnostic drive-proxy fetch error', { driveId, error: String(fetchErr2) });
                                }
                            }

                            try {
                                const rawKey = firstCandidate ? String(firstCandidate) : null;
                                const isAbsolute = src && (/^(https?:)?\/\//i.test(String(src)) || String(src).startsWith('/'));
                                const isData = src && String(src).startsWith('data:');
                                if (rawKey && !driveId && !isAbsolute && !isData) {
                                    const cachedPresigned = localPresignedMap[rawKey] || presignedCache.get(rawKey);
                                    if (cachedPresigned) {
                                        try {
                                            const imgEl = e.currentTarget as HTMLImageElement;
                                            imgEl.src = cachedPresigned;
                                            return;
                                        } catch { }
                                    }

                                    try {
                                        const resp = await fetch('/api/uploads/presigned-get', {
                                            method: 'POST',
                                            headers: { 'Content-Type': 'application/json' },
                                            body: JSON.stringify({ key: rawKey }),
                                        });
                                        if (resp.ok) {
                                            const data = await resp.json().catch(() => null);
                                            if (data?.url) {
                                                const presignedUrl = String(data.url);
                                                presignedCache.set(rawKey, presignedUrl);
                                                setLocalPresignedMap(prev => ({ ...prev, [rawKey]: presignedUrl }));
                                                const imgEl = e.currentTarget as HTMLImageElement;
                                                imgEl.src = presignedUrl;
                                                return;
                                            }
                                        } else {
                                            const text = await resp.text().catch(() => null);
                                            console.debug('presigned-get onError non-ok', { rawKey, status: resp.status, text });
                                        }
                                    } catch (errPresign) {
                                        console.debug('presigned-get onError fetch failed', { rawKey, error: String(errPresign) });
                                    }
                                }
                            } catch { }
                        })();

                        const imgElement = e.currentTarget as HTMLImageElement;
                        if (!imgElement.src.includes('placeholder.png')) {
                            imgElement.src = '/images/placeholder.png';
                            return;
                        }
                        imgElement.onerror = null;
                        imgElement.style.display = 'none';

                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'flex items-center justify-center h-full bg-red-50 text-red-500 text-sm p-4 rounded';
                        errorDiv.textContent = 'Erro ao carregar imagem!';
                        if (imgElement.parentNode) {
                            imgElement.parentNode.appendChild(errorDiv);
                        }
                    }}
                />
                <div style={{ position: 'absolute', top: 8, right: 8 }}>
                    <Button size="sm" onClick={() => setDownloadModalOpen(true)}>
                        <Download size={14} />
                    </Button>
                </div>
                <DeliveryDownloadModal open={downloadModalOpen} onOpenChange={setDownloadModalOpen} items={(() => {
                    const thumbs = Array.isArray(content.urlThumbnails) ? content.urlThumbnails : [];
                    const urls = Array.isArray(content.urlStructuringFeed) ? content.urlStructuringFeed : (content.urlStructuringFeed ? [content.urlStructuringFeed] : []);
                    const list: { key: string; label?: string; thumb?: string | null }[] = [];
                    urls.forEach((u, i) => {
                        const key = String(u);
                        const thumb = (thumbs && thumbs[i]) ? (selectSrc(thumbs[i]) || null) : (selectSrc(u) || null);
                        list.push({ key, label: undefined, thumb });
                    });
                    return list;
                })()} />
            </div>
        </div>
    );
};

export default RenderDeliveryItem;
