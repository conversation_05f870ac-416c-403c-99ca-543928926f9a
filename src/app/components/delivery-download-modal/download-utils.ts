"use client";

export async function downloadKey(key: string) {
    if (!key) return;
    if (key.startsWith('data:') || /^(https?:)?\/\//i.test(key)) {
        // not supported by this util
        return Promise.reject(new Error('Download somente disponível para arquivos upados.'));
    }

    try {
        const res = await fetch('/api/uploads/presigned-get', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ key }) });
        if (!res.ok) throw new Error('Não foi possível gerar URL de download');
        const data = await res.json().catch(() => null);
        if (!data?.url) throw new Error('Não foi possível gerar URL de download');
        const presigned = String(data.url);

        const resp = await fetch(presigned);
        if (!resp.ok) throw new Error('Falha ao baixar arquivo');
        const blob = await resp.blob();
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = key.split('/').pop() || 'download';
        document.body.appendChild(a);
        a.click();
        a.remove();
        URL.revokeObjectURL(url);
        return Promise.resolve();
    } catch (e) {
        return Promise.reject(e instanceof Error ? e : new Error('Erro ao baixar arquivo'));
    }
}

export default downloadKey;
