"use client";

import React, { useMemo, useState } from 'react';
// Image not required; previews use native <img> to support external URLs
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/app/components/ui/dialog';
import { Button } from '@/app/components/ui/button';
import { Checkbox } from '@/app/components/ui/checkbox';
import { Download } from 'lucide-react';
import { toast } from 'sonner';
import { Label } from '../ui/label';
import { Switch } from '../ui/switch';

interface Item {
    key: string; // original key or url
    label?: string;
    thumb?: string | null;
}

interface Props {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    items: Item[]; // list of files to preview/download
}

const presignedCache = new Map<string, string>();

const getFilenameFromKey = (k: string) => k.split('/').pop() || 'download';

export const DeliveryDownloadModal: React.FC<Props> = ({ open, onOpenChange, items }) => {
    const [selected, setSelected] = useState<Record<string, boolean>>(() => {
        const m: Record<string, boolean> = {};
        items.forEach(it => { m[it.key] = false; });
        return m;
    });
    const [processing, setProcessing] = useState(false);
    const [selectedCovers, setSelectedCovers] = useState<Record<string, boolean>>(() => {
        const m: Record<string, boolean> = {};
        items.forEach(it => { m[it.key] = true; });
        return m;
    });

    // keep selection synced when items change
    React.useEffect(() => {
        const map: Record<string, boolean> = {};
        items.forEach(it => { map[it.key] = selected[it.key] ?? false; });
        setSelected(map);
        const covers: Record<string, boolean> = {};
        items.forEach(it => { covers[it.key] = selectedCovers[it.key] ?? true; });
        setSelectedCovers(covers);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [items.map(i => i.key).join('|')]);

    const anySelected = useMemo(() => Object.values(selected).some(Boolean), [selected]);

    const downloadOne = async (key: string) => {
        try {
            // Support three kinds of sources:
            // - data URIs and external URLs: try to fetch directly
            // - upload keys: request a presigned URL from the API
            let resp: Response | null = null;
            let blob: Blob | null = null;
            const isDirect = key.startsWith('data:') || /^(https?:)?\/\//i.test(key);
            if (isDirect) {
                // normalize protocol-relative URLs
                const url = key.startsWith('//') ? `${location.protocol}${key}` : key;
                resp = await fetch(url);
                if (!resp.ok) {
                    toast.error('Falha ao baixar arquivo (URL externa)');
                    return;
                }
                blob = await resp.blob();
            } else {
                let presigned = presignedCache.get(key);
                if (!presigned) {
                    const res = await fetch('/api/uploads/presigned-get', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ key }) });
                    if (!res.ok) {
                        toast.error('Não foi possível gerar URL de download');
                        return;
                    }
                    const data = await res.json().catch(() => null);
                    if (!data?.url) {
                        toast.error('Não foi possível gerar URL de download');
                        return;
                    }
                    presigned = String(data.url);
                    presignedCache.set(key, presigned);
                }
                resp = await fetch(presigned);
                if (!resp.ok) {
                    toast.error('Falha ao baixar arquivo');
                    return;
                }
                blob = await resp.blob();
            }
            if (!blob) {
                toast.error('Falha ao processar arquivo');
                return;
            }
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = getFilenameFromKey(key);
            document.body.appendChild(a);
            a.click();
            a.remove();
            URL.revokeObjectURL(url);
            toast.success('Download iniciado');
        } catch (e) {
            console.error('download failed', e);
            toast.error('Erro ao baixar arquivo');
        }
    };

    const downloadSelected = async () => {
        if (!anySelected) return toast.error('Selecione ao menos um arquivo');
        setProcessing(true);
        try {
            const keys = Object.keys(selected).filter(k => selected[k]);
            // sequentially download to avoid too many parallel requests
            for (const k of keys) {
                await downloadOne(k);
                if (selectedCovers[k]) {
                    // find the item to get its thumb
                    const it = items.find(x => x.key === k);
                    if (it?.thumb) {
                        try {
                            await downloadOne(it.thumb);
                        } catch (e) {
                            // individual cover failure shouldn't stop the rest
                            console.warn('cover download failed', e);
                        }
                    }
                }
            }
        } finally {
            setProcessing(false);
        }
    };

    const toggle = (key: string) => setSelected(prev => ({ ...prev, [key]: !prev[key] }));
    const toggleCover = (key: string) => setSelectedCovers(prev => ({ ...prev, [key]: !prev[key] }));

    const isVideo = (k?: string | null) => {
        if (!k) return false;
        // data URI with video MIME
        if (k.startsWith('data:') && k.includes('video/')) return true;
        // strip query params
        const p = k.split('?')[0];
        const ext = p.split('.').pop()?.toLowerCase() || '';
        if (['mp4', 'mov', 'webm', 'mkv', 'avi', 'm4v'].includes(ext)) return true;
        // sometimes URLs include a path segment like /video/ but no extension
        if (/\bvideo\b/i.test(p)) return true;
        return false;
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-4xl">
                <DialogHeader>
                    <DialogTitle>Visualizar e baixar arquivos</DialogTitle>
                </DialogHeader>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-h-[60vh] overflow-auto py-2">
                    {items.map(it => (
                        <div key={it.key} className='flex flex-col gap-2'>
                            <div className={`p-1 rounded ${selected[it.key] ? 'bg-primary dark:bg-primary' : ''}`}>
                                <div className="w-full h-40 bg-gray-100 dark:bg-zinc-800 flex items-center justify-center overflow-hidden">
                                    {it.thumb ? (
                                        // prefer using Image for optimization but allow external
                                        // eslint-disable-next-line @next/next/no-img-element
                                        <img src={it.thumb} alt={it.label || getFilenameFromKey(it.key)} className="max-w-full max-h-full object-contain" />
                                    ) : (
                                        <div className="text-xs text-muted-foreground">
                                            Prévia indisponível
                                        </div>
                                    )}
                                </div>
                            </div>
                            <div className='flex justify-between'>
                                {it.thumb && isVideo(it.key) && (
                                    <Label className="flex items-center gap-1 text-xs ml-2 select-none">
                                        <Switch checked={!!selectedCovers[it.key]} onCheckedChange={() => toggleCover(it.key)} />
                                        <span className="text-xs text-muted-foreground">
                                            Baixar capa do vídeo também
                                        </span>
                                    </Label>
                                )}
                                <Checkbox checked={!!selected[it.key]} onCheckedChange={() => toggle(it.key)} />
                            </div>
                        </div>
                    ))}
                </div>

                <DialogFooter>
                    <div className="flex flex-col xs:flex-row gap-2 w-full justify-between items-center">
                        <div className="text-sm text-muted-foreground">{items.length} arquivos</div>
                        <div className="flex flex-col xs:flex-row gap-2 w-full xs:w-auto">
                            <Button variant="ghost" onClick={() => onOpenChange(false)}>
                                Voltar
                            </Button>
                            <Button onClick={() => {
                                const keys = items.map(i => i.key);
                                const allSelected = keys.every(k => selected[k]);
                                const map: Record<string, boolean> = {};
                                if (allSelected) {
                                    keys.forEach(k => { map[k] = false; });
                                } else {
                                    keys.forEach(k => { map[k] = true; });
                                }
                                setSelected(map);
                            }}>
                                Selecionar todos
                            </Button>
                            <Button disabled={!anySelected || processing} onClick={() => void downloadSelected()}>
                                <Download /> Baixar selecionados
                            </Button>
                        </div>
                    </div>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};

export default DeliveryDownloadModal;
