"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Badge } from "@/app/components/ui/badge";
import { Check, Ellipsis, X } from "lucide-react";

interface DebugResult {
    originalUrl: string;
    extractedId: string;
    proxyUrl: string;
    strategies: Array<{
        strategy: string;
        url: string;
        status?: number;
        statusText?: string;
        contentType?: string;
        contentLength?: string;
        success: boolean;
        error?: string;
    }>;
    recommendations: string[];
}

export const DriveDebugger = () => {
    const [url, setUrl] = useState('');
    const [loading, setLoading] = useState(false);
    const [result, setResult] = useState<DebugResult | null>(null);
    const [error, setError] = useState<string | null>(null);

    const handleTest = async () => {
        if (!url.trim()) {
            setError('Por favor, forneça uma URL!');
            return;
        }

        setLoading(true);
        setError(null);
        setResult(null);

        try {
            const response = await fetch(`/api/debug-drive?url=${encodeURIComponent(url)}`);
            const data = await response.json();

            if (response.ok) {
                setResult(data);
            } else {
                setError(data.error || 'Erro ao testar URL');
            }
        } catch {
            setError('Erro de rede ao testar URL');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Card className="w-full max-w-4xl mx-auto">
            <CardHeader>
                <CardTitle>🔧 Debugger de URLs do Google Drive</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="flex flex-col xs:flex-row gap-2">
                    <Input
                        placeholder="Cole aqui a URL do Google Drive..."
                        value={url}
                        onChange={(e) => setUrl(e.target.value)}
                        className="flex-1 placeholder:text-sm"
                    />
                    <Button onClick={handleTest} disabled={loading}>
                        {loading ? <Ellipsis /> : 'Testar'}
                    </Button>
                </div>

                {result ? (
                    <>
                        <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h3 className="font-semibold text-sm mb-2">URL original:</h3>
                                    <p className="text-xs bg-gray-100 dark:bg-zinc-950 p-2 rounded break-all">
                                        {result.originalUrl}
                                    </p>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-sm mb-2">ID extraído:</h3>
                                    <p className="text-xs bg-gray-100 dark:bg-zinc-950 p-2 rounded font-mono">
                                        {result.extractedId}
                                    </p>
                                </div>
                            </div>

                            <div>
                                <h3 className="font-semibold text-sm mb-2">URL do proxy:</h3>
                                <p className="text-xs bg-blue-50 dark:bg-indigo-950 p-2 rounded break-all">
                                    {result.proxyUrl}
                                </p>
                            </div>

                            <div>
                                <h3 className="font-semibold text-sm mb-2">Teste de estratégias:</h3>
                                <div className="space-y-2">
                                    {result.strategies.map((strategy, index) => (
                                        <div key={index} className="border rounded p-3">
                                            <div className="flex items-center justify-between mb-2">
                                                <span className="font-medium">{strategy.strategy}</span>
                                                <Badge variant={strategy.success ? "default" : "destructive"}>
                                                    {strategy.success ? <div className="text-xs flex items-center gap-1"><Check size={16} color="green" /> SUCESSO</div> : <div className="text-xs flex items-center gap-1"><X size={16} /> FALHA</div>}
                                                </Badge>
                                            </div>
                                            <p className="text-xs text-gray-600 mb-1 break-all">
                                                {strategy.url}
                                            </p>
                                            {strategy.success ? (
                                                <div className="text-xs text-green-700">
                                                    Status: {strategy.status} |
                                                    Tipo: {strategy.contentType} |
                                                    Tamanho: {strategy.contentLength}
                                                </div>
                                            ) : (
                                                <div className="text-xs text-red-700">
                                                    {strategy.error || `Status: ${strategy.status}`}
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {result.recommendations.length > 0 && (
                                <div>
                                    <h3 className="font-semibold text-sm mb-2">Estratégias funcionais:</h3>
                                    <ul className="text-xs space-y-1">
                                        {result.recommendations.map((rec, index) => (
                                            <li key={index} className="text-green-700">• {rec}</li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                    </>
                ) : (
                    <div className="text-muted-foreground text-sm !mt-6 space-y-2">
                        <p>
                            Cole uma URL do Google Drive acima para analisar detalhadamente sua estrutura e testar diferentes métodos de acesso.
                        </p>
                        <p>
                            O debugger irá:
                        </p>
                        <ul className="list-disc pl-6 space-y-1">
                            <li>Extrair e validar o ID do documento</li>
                            <li>Gerar URLs alternativas de acesso</li>
                            <li>Testar múltiplas estratégias de download</li>
                            <li>Verificar permissões e disponibilidade</li>
                            <li>Recomendar os melhores métodos de acesso</li>
                        </ul>
                        <p>
                            Para saber mais, acesse a <a href="https://github.com/jonathafernandes/b4-desk/blob/main/docs/GOOGLE_DRIVE_TROUBLESHOOTING.md" target="_blank" className="text-blue-600 hover:underline">documentação de troubleshooting</a>, a <a href="https://github.com/jonathafernandes/b4-desk/blob/main/docs/GOOGLE_DRIVE_API_SETUP.md" target="_blank" className="text-blue-600 hover:underline">documentação de setup da API</a> ou a <a href="https://github.com/jonathafernandes/b4-desk/blob/main/docs/GOOGLE_DRIVE_IMAGES_DEBUG.md" target="_blank" className="text-blue-600 hover:underline">documentação de debug de imagens</a>. Todas estão no repositório no GitHub, na raiz da pasta <code>docs</code>.
                        </p>
                    </div>
                )}

                {error && (
                    <div className="p-4 bg-red-50 border border-red-200 rounded-md text-sm">
                        <p className="text-red-800">{error}</p>
                    </div>
                )}

            </CardContent>
        </Card>
    );
};
