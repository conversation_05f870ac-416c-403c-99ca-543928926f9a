"use client";

import React, { useState } from "react";
import { Button } from "@/app/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";
import { Textarea } from "@/app/components/ui/textarea";
import { Edit } from "lucide-react";

interface EditCaptionProps {
    contentId: string;
    initialCaption?: string;
    onSaved?: (newCaption: string) => void;
    size?: 'icon' | 'sm' | 'default';
}

export const EditCaption = ({ contentId, initialCaption = '', onSaved, size = 'default' }: EditCaptionProps) => {
    const [value, setValue] = useState(initialCaption);
    const [isSaving, setIsSaving] = useState(false);
    const [isOpen, setIsOpen] = useState(false);

    const handleSave = async () => {
        try {
            setIsSaving(true);
            const res = await fetch(`/api/contents/${contentId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ caption: value })
            });

            if (!res.ok) {
                const errorData = await res.json().catch(() => null);
                throw new Error(errorData?.message || `Erro ${res.status}`);
            }

            onSaved?.(value);
            setIsOpen(false);
        } catch (e) {
            console.error('Erro ao atualizar legenda', e);
        } finally {
            setIsSaving(false);
        }
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size={size} title="Editar legenda">
                    <Edit />
                    {size !== 'icon' && 'Editar legenda'}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[520px]">
                <DialogHeader>
                    <DialogTitle>Alterar legenda</DialogTitle>
                </DialogHeader>

                <div className="mt-2">
                    <Textarea value={value} onChange={(e) => setValue(e.target.value)} rows={6} />
                </div>

                <div className="mt-4 flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsOpen(false)}>Cancelar</Button>
                    <Button disabled={isSaving} onClick={handleSave}>
                        {isSaving ? 'Salvando' : 'Salvar'}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}

export default EditCaption;
