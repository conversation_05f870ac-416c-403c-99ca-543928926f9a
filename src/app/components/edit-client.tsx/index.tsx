import { Sheet, She<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Sheet<PERSON>rigger } from '../ui/sheet';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Archive, Ellipsis, SquarePen, Trash, TriangleA<PERSON>t, User<PERSON>en } from 'lucide-react';
import { Separator } from '../ui/separator';
import { useState } from 'react';
import { toast } from 'sonner';
import { Client } from '@/app/clients/page';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '../ui/alert-dialog';
import { formatPhoneOnInput, isValidPhone } from '@/lib/formatters';

interface EditClientProps {
    client?: Client;
    onClientUpdate?: () => void;
    disabled?: boolean;
}

export const EditClient = ({ client, onClientUpdate, disabled }: EditClientProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const [newClient, setNewClient] = useState<Omit<Client, 'id' | 'monthlyPlannings'>>({
        name: client?.name || '',
        phone: client?.phone || '',
        instagramUsername: client?.instagramUsername || '',
        monthlyPostsLimit: client?.monthlyPostsLimit || 0,
        monthlyStoriesLimit: client?.monthlyStoriesLimit || 0,
        additionalContent: client?.additionalContent || 0
    });

    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;

        if (name === 'phone') {
            setNewClient(prev => ({
                ...prev,
                [name]: formatPhoneOnInput(value)
            }));
        } else if (['monthlyPostsLimit', 'monthlyStoriesLimit', 'additionalContent'].includes(name)) {
            setNewClient(prev => ({
                ...prev,
                [name]: Number(value) || 0
            }));
        } else {
            setNewClient(prev => ({
                ...prev,
                [name]: value
            }));
        }
    };

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        setIsSubmitting(true);

        if (newClient.phone && !isValidPhone(newClient.phone)) {
            toast.error("O número de telefone deve ter 10 ou 11 dígitos, incluindo o DDD.");
            setIsSubmitting(false);
            return;
        }

        try {
            const response = await fetch(`/api/clients/${client?.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(newClient)
            });

            if (!response.ok) {
                throw new Error('Erro ao salvar cliente');
            }

            toast.success('Cliente salvo com sucesso!');
            setIsOpen(false);
            if (onClientUpdate) {
                onClientUpdate();
            }
        } catch (error) {
            console.error('Erro ao salvar cliente:', error);
            toast.error('Erro ao salvar cliente. Tente novamente mais tarde.');
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleDeleteClient = async () => {
        setIsSubmitting(true);
        if (!client) return;

        if (client?.monthlyPlannings && client.monthlyPlannings.length > 0) {
            toast.error('Não é possível excluir um cliente com planejamentos ativos.');
            setIsSubmitting(false);
            return;
        }

        try {
            const response = await fetch(`/api/clients/${client.id}`, {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error('Erro ao excluir cliente');
            }

            toast.success('Cliente excluído com sucesso!');
            setIsOpen(false);
            if (onClientUpdate) {
                onClientUpdate();
            }
        } catch (error) {
            console.error('Erro ao excluir cliente:', error);
            toast.error('Erro ao excluir cliente. Tente novamente mais tarde.');
        } finally {
            setIsSubmitting(false);
        }
    }

    const handleArchiveClient = async (action: 'archive' | 'unarchive') => {
        setIsSubmitting(true);
        if (!client) return;

        try {
            const response = await fetch(`/api/clients/${client.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ action })
            });

            if (!response.ok) {
                throw new Error(`Erro ao ${action === 'archive' ? 'arquivar' : 'desarquivar'} cliente`);
            }

            if (action === 'archive' && typeof window !== 'undefined') {
                const viewedClients = JSON.parse(localStorage.getItem("viewedClients") || "[]");
                const updatedClients = viewedClients.filter((c: { id: string }) => c.id !== client.id);
                localStorage.setItem("viewedClients", JSON.stringify(updatedClients));
            }

            if (action === 'archive') {
                toast.success(`Cliente arquivado com sucesso! Todas as demandas relacionadas também foram arquivadas.`);
            } else {
                toast.success(`Cliente desarquivado com sucesso!`);
            }
            setIsOpen(false);
            if (onClientUpdate) {
                onClientUpdate();
            }
        } catch (error) {
            console.error(`Erro ao ${action === 'archive' ? 'arquivar' : 'desarquivar'} cliente:`, error);
            toast.error(`Erro ao ${action === 'archive' ? 'arquivar' : 'desarquivar'} cliente. Tente novamente mais tarde.`);
        } finally {
            setIsSubmitting(false);
        }
    }

    return (
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
                <Button
                    variant="outline"
                    size="icon"
                    className="font-normal"
                    onClick={() => setIsOpen(true)}
                    disabled={disabled}
                >
                    <SquarePen />
                </Button>
            </SheetTrigger>
            <SheetContent className='w-full overflow-y-auto max-h-screen'>
                <SheetHeader>
                    <SheetTitle className='flex items-center gap-2'>
                        <UserPen size={17} />
                        Editar cliente
                    </SheetTitle>
                    <SheetDescription className='text-left !mt-8 !mb-2 p-0'>
                        Edite os dados básicos do cliente
                    </SheetDescription>
                </SheetHeader>
                <Separator />
                <form onSubmit={handleSubmit} className="space-y-2 mt-4">
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Nome
                        </label>
                        <Input type="text" id="name" name="name" placeholder='Nome' value={newClient.name} onChange={handleInputChange} required className="mt-1" />
                    </div>
                    <div className='grid xs:grid-cols-2 gap-2'>
                        <div>
                            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Telefone
                            </label>
                            <Input type="text" id="phone" name="phone" placeholder='Número' value={newClient.phone} onChange={handleInputChange} className="mt-1" />
                        </div>
                        <div>
                            <label htmlFor="instagramUsername" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Instagram
                            </label>
                            <Input type="text" id="instagramUsername" name="instagramUsername" placeholder='nome_de_usuario' value={newClient.instagramUsername} onChange={handleInputChange} className="mt-1" />
                        </div>
                    </div>
                    <div className='grid grid-cols-2 gap-2'>
                        <div>
                            <label htmlFor="monthlyPostsLimit" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Limite de posts
                            </label>
                            <Input type="number" id="monthlyPostsLimit" name="monthlyPostsLimit" value={newClient.monthlyPostsLimit} onChange={handleInputChange} className="mt-1" />
                        </div>
                        <div>
                            <label htmlFor="monthlyStoriesLimit" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                Limite de stories
                            </label>
                            <Input type="number" id="monthlyStoriesLimit" name="monthlyStoriesLimit" value={newClient.monthlyStoriesLimit} onChange={handleInputChange} className="mt-1" />
                        </div>
                    </div>
                    <div>
                        <label htmlFor="additionalContent" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Materiais extras
                        </label>
                        <Input type="number" id="additionalContent" name="additionalContent" value={newClient.additionalContent} onChange={handleInputChange} className="mt-1" />
                    </div>
                    <p className='text-sm text-muted-foreground'>
                        Para editar os outros dados ou adicionar mais informações, vá até a página de perfil do cliente.
                    </p>
                    <Button type="submit" className="w-full !mt-6" disabled={isSubmitting}>
                        {isSubmitting ? <Ellipsis /> : "Salvar"}
                    </Button>
                </form>

                <Separator className='my-4' />

                <div className="space-y-4">
                    <Card className='border border-amber-200 dark:border-amber-800 dark:bg-zinc-800/50'>
                        <CardHeader>
                            <CardTitle className="text-amber-600 flex items-center gap-2">
                                <Archive size={18} />
                                <span>Arquivar cliente</span>
                            </CardTitle>
                            <CardDescription>
                                {client?.archived
                                    ? "Cliente está arquivado. Você pode desarquivá-lo para torná-lo visível novamente."
                                    : "Arquive este cliente para ocultá-lo da lista principal. Todas as demandas pontuais e conteúdos relacionados a este cliente também serão arquivados."}
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <AlertDialog>
                                <AlertDialogTrigger asChild>
                                    <Button
                                        variant="outline"
                                        className={`w-full ${client?.archived ? 'text-green-600' : 'text-amber-600'}`}
                                    >
                                        {isSubmitting ? (
                                            <Ellipsis />
                                        ) : (
                                            <div className='flex items-center gap-1'>
                                                <Archive />
                                                {client?.archived ? "Desarquivar cliente" : "Arquivar cliente e demandas"}
                                            </div>)
                                        }
                                    </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                    <AlertDialogHeader>
                                        <AlertDialogTitle>Você tem certeza?</AlertDialogTitle>
                                        <AlertDialogDescription>
                                            {client?.archived
                                                ? "O cliente será desarquivado e voltará a aparecer na lista principal."
                                                : "O cliente será arquivado e não aparecerá mais na lista principal. Todas as demandas e conteúdos relacionados a este cliente também serão arquivados."}
                                        </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                        <AlertDialogAction
                                            onClick={() => handleArchiveClient(client?.archived ? 'unarchive' : 'archive')}
                                            disabled={isSubmitting}
                                        >
                                            Continuar
                                        </AlertDialogAction>
                                    </AlertDialogFooter>
                                </AlertDialogContent>
                            </AlertDialog>
                        </CardContent>
                    </Card>

                    <Card className='border border-red-200 dark:border-red-800 dark:bg-zinc-800/50'>
                        <CardHeader>
                            <CardTitle className="text-red-600 flex items-center gap-2">
                                <TriangleAlert size={18} />
                                <span>Zona de perigo</span>
                            </CardTitle>
                            <CardDescription>
                                As ações nesta área são irreversíveis
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <AlertDialog>
                                <AlertDialogTrigger asChild>
                                    <Button variant="outline" className="text-red-600 w-full !hover:brightness-125"
                                    >
                                        {isSubmitting ? (
                                            <Ellipsis />
                                        ) : (
                                            <div className='flex items-center gap-1'>
                                                <Trash />
                                                Excluir cliente
                                            </div>)
                                        }
                                    </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                    <AlertDialogHeader>
                                        <AlertDialogTitle>Você tem certeza?</AlertDialogTitle>
                                        <AlertDialogDescription>
                                            Essa ação remove permanentemente o cliente de nossa base de dados.
                                        </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                        <AlertDialogAction
                                            onClick={handleDeleteClient}
                                            disabled={isSubmitting}
                                        >
                                            Continuar
                                        </AlertDialogAction>
                                    </AlertDialogFooter>
                                </AlertDialogContent>
                            </AlertDialog>
                        </CardContent>
                    </Card>
                </div>
            </SheetContent>
        </Sheet>
    );
}