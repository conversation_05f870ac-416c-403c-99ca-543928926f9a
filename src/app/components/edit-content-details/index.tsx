"use client"

import { useState } from 'react';
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";
import { Label } from "@/app/components/ui/label";
import { Input } from "@/app/components/ui/input";
import { Textarea } from "@/app/components/ui/textarea";
import { toast } from "sonner";
import { Ellipsis, FileEdit } from "lucide-react";

interface EditContentDetailsProps {
  contentId: string;
  currentReference?: string;
  currentCopy?: string;
  currentCaption?: string;
  onSuccess?: () => void;
  onLocalUpdate?: (reference: string | null, copy: string | null, caption: string | null) => void;
}

export function EditContentDetails({
  contentId,
  currentReference = '',
  currentCopy = '',
  currentCaption = '',
  onSuccess,
  onLocalUpdate
}: EditContentDetailsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [reference, setReference] = useState(currentReference);
  const [copywriting, setCopywriting] = useState(currentCopy);
  const [caption, setCaption] = useState(currentCaption);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    if (onLocalUpdate) {
      onLocalUpdate(reference, copywriting, caption);
    }

    try {
      const response = await fetch(`/api/contents/${contentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reference,
          copywriting,
          caption,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao atualizar detalhes');
      }

      toast.success('Detalhes atualizados com sucesso');
      setIsOpen(false);

      if (onSuccess && !onLocalUpdate) {
        onSuccess();
      }
    } catch (error) {
      console.error('Erro ao atualizar detalhes:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao atualizar detalhes');

      if (onLocalUpdate) {
        onLocalUpdate(currentReference, currentCopy, currentCaption);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" title="Editar referência e copy">
          <FileEdit className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Editar detalhes do conteúdo</DialogTitle>
          <DialogDescription>
            Atualize a referência e o copy deste conteúdo
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="reference">Referência</Label>
            <Input
              id="reference"
              value={reference}
              onChange={(e) => setReference(e.target.value)}
              placeholder="URL ou descrição da referência"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="copywriting">Copy</Label>
            <Textarea
              id="copywriting"
              value={copywriting}
              onChange={(e) => setCopywriting(e.target.value)}
              placeholder="Copywriting para o conteúdo"
              rows={5}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="caption">Legenda</Label>
            <Textarea
              id="caption"
              value={caption}
              onChange={(e) => setCaption(e.target.value)}
              placeholder="Legenda para o conteúdo"
              rows={5}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="whitespace-nowrap"
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading} className="whitespace-nowrap">
              {isLoading ? <Ellipsis className="h-4 w-4" /> : 'Salvar'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
