"use client";

import { Button } from "@/app/components/ui/button";
import { FilePenLine } from "lucide-react";
import { GeneralDemandForm } from "@/app/components/general-demand-form";

interface Client {
  id: string;
  name: string;
}

interface GeneralDemand {
  id: string;
  title: string;
  description?: string | null;
  dueDate?: string | Date | null;
  status?: string;
  priority?: string;
  clientId: string;
  isLooseClient?: boolean;
  looseClientName?: string | null;
  assignedToId?: string | null;
  urlStructuringFeed?: string[] | null;
}

interface EditGeneralDemandProps {
  demand: GeneralDemand;
  clients: Client[];
  onSuccess: () => void;
}

export function EditGeneralDemand({ demand, clients, onSuccess }: EditGeneralDemandProps) {
  const getDueDate = (): Date | null => {
    if (!demand.dueDate) {
      return null;
    }

    if (demand.dueDate instanceof Date) {
      return demand.dueDate;
    }

    if (typeof demand.dueDate === 'string') {
      const dateParts = demand.dueDate.split('T')[0].split('-');
      if (dateParts.length === 3) {
        const [year, month, day] = dateParts.map(Number);
        return new Date(year, month - 1, day, 12, 0, 0);
      }
    }

    return null;
  };

  const formattedDemand = {
    ...demand,
    dueDate: getDueDate(),
    isLooseClient: demand.isLooseClient || false,
    looseClientName: demand.looseClientName || "",
  };

  return (
    <GeneralDemandForm
      initialData={formattedDemand}
      clients={clients}
      onSuccess={onSuccess}
      buttonLabel=""
      buttonVariant="outline"
      triggerComponent={
        <Button variant="outline" size="icon" title="Editar demanda">
          <FilePenLine className="h-4 w-4" />
        </Button>
      }
    />
  );
}
