"use client";

import { useState } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Check, Edit, Ellipsis } from "lucide-react";
import { toast } from "sonner";

interface EditWeekActivityProps {
    activityId: string;
    weekNum: string;
    description: string;
    onSuccess?: () => void;
    onEditingChange?: (weekNum: string, isEditing: boolean) => void;
    disabled?: boolean;
}

export function EditMonthlyWeek({
    activityId,
    weekNum,
    description,
    onSuccess,
    onEditingChange,
    disabled
}: EditWeekActivityProps) {
    const [isEditing, setIsEditing] = useState<boolean>(false);
    const [editDescription, setEditDescription] = useState<string>(description);
    const [loading, setLoading] = useState<boolean>(false);

    const toggleEditing = (newState: boolean) => {
        setIsEditing(newState);
        onEditingChange?.(weekNum, newState);
    };

    const saveChanges = async () => {
        setLoading(true);
        try {
            const response = await fetch(`/api/activities/${activityId}`, {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    description: editDescription,
                }),
            });

            if (!response.ok) {
                throw new Error("Erro ao atualizar semana");
            }

            toast.success("Semana atualizada com sucesso!");
            toggleEditing(false);
            onSuccess?.();
        } catch (error) {
            console.error("Erro ao atualizar semana:", error);
            toast.error("Erro ao atualizar semana");
        } finally {
            setLoading(false);
        }
    };

    return (
        <>
            <div className="flex-shrink-0">
                <Button
                    size="icon"
                    variant="outline"
                    onClick={() => {
                        setEditDescription(description);
                        toggleEditing(!isEditing);
                    }}
                    disabled={disabled}
                >
                    {isEditing ? <Check /> : <Edit />}
                </Button>
            </div>

            {isEditing && (
                <div className="w-full">
                    <div className="flex flex-col items-end gap-2 w-full">
                        <Input
                            id={`edit-description-${weekNum}`}
                            value={editDescription}
                            onChange={(e) => setEditDescription(e.target.value)}
                            className="flex-grow min-w-0"
                            placeholder="Objetivo da semana"
                        />

                        <div className="flex flex-col sm:flex-row gap-2 mt-2 sm:mt-0 w-full sm:w-auto">
                            <Button
                                variant="outline"
                                onClick={() => toggleEditing(false)}
                                className="whitespace-nowrap w-full sm:w-auto"
                            >
                                Cancelar
                            </Button>
                            <Button
                                onClick={() => {
                                    saveChanges();
                                }}
                                className="whitespace-nowrap w-full sm:w-auto"
                                disabled={loading}
                            >
                                {loading ? <Ellipsis /> : "Salvar"}
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
}