"use client"

import { useState } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { toast } from 'sonner';

interface PointRecord {
    id: string;
    userId: string;
    userName: string;
    userEmail: string;
    clockIn?: string;
    clockOut?: string;
    date: string;
    totalHours?: number;
}

interface EditPointRecordProps {
    record: PointRecord;
    onSuccess: () => void;
    onClose: () => void;
}

export const EditPointRecord = ({ record, onSuccess, onClose }: EditPointRecordProps) => {
    const [date, setDate] = useState(record.date);
    const [clockIn, setClockIn] = useState(record.clockIn ? new Date(record.clockIn).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit', hour12: false }) : '');
    const [clockOut, setClockOut] = useState(record.clockOut ? new Date(record.clockOut).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit', hour12: false }) : '');
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!date) {
            toast.error('Data é obrigatória');
            return;
        }

        setIsLoading(true);

        try {
            const response = await fetch(`/api/admin/point-record/${record.id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    date,
                    clockIn: clockIn ? `${date}T${clockIn}:00` : null,
                    clockOut: clockOut ? `${date}T${clockOut}:00` : null
                }),
            });

            if (!response.ok) {
                throw new Error('Erro ao atualizar registro');
            }

            toast.success('Registro atualizado com sucesso!');
            onClose();
            onSuccess();
        } catch (error) {
            console.error('Erro:', error);
            toast.error('Erro ao atualizar registro');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <DialogContent>
            <DialogHeader>
                <DialogTitle>Editar registro de ponto</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                    <Label>Usuário</Label>
                    <Input value={`${record.userName} (${record.userEmail})`} disabled />
                </div>
                <div>
                    <Label htmlFor="date">Data</Label>
                    <Input
                        id="date"
                        type="date"
                        value={date}
                        onChange={(e) => setDate(e.target.value)}
                    />
                </div>
                <div>
                    <Label htmlFor="clockIn">Horário de entrada</Label>
                    <Input
                        id="clockIn"
                        type="time"
                        value={clockIn}
                        onChange={(e) => setClockIn(e.target.value)}
                    />
                </div>
                <div>
                    <Label htmlFor="clockOut">Horário de saída</Label>
                    <Input
                        id="clockOut"
                        type="time"
                        value={clockOut}
                        onChange={(e) => setClockOut(e.target.value)}
                    />
                </div>
                <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={onClose}>
                        Cancelar
                    </Button>
                    <Button type="submit" disabled={isLoading}>
                        {isLoading ? 'Salvando' : 'Salvar'}
                    </Button>
                </div>
            </form>
        </DialogContent>
    );
};