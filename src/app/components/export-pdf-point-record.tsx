"use client"

import { useState } from 'react';
import { Button } from './ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { FileText } from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface PointRecord {
    id: string;
    userId: string;
    userName: string;
    userEmail: string;
    clockIn?: string;
    clockOut?: string;
    date: string;
    totalHours?: number;
    timeBank?: number;
    createdAt: string;
    createdBy?: string;
    createdByName?: string;
    hasEdits?: boolean;
}

interface ExportPDFPointRecordProps {
    records: PointRecord[];
    timeBankData?: { userId: string, userName: string, userEmail: string, totalTimeBank: number }[];
    excusedDays?: { id: string, userId: string, date: string, reason: string }[];
}

export function ExportPDFPointRecord({ records, timeBankData, excusedDays }: ExportPDFPointRecordProps) {
    const [selectedMonth, setSelectedMonth] = useState<string>('');
    const [selectedEmployee, setSelectedEmployee] = useState<string>('');
    const [isExporting, setIsExporting] = useState(false);
    const [isOpen, setIsOpen] = useState(false);

    const getAvailableEmployees = () => {
        const employees = new Map<string, string>();
        records.forEach(record => {
            employees.set(record.userId, record.userName);
        });
        return Array.from(employees.entries()).sort((a, b) => a[1].localeCompare(b[1]));
    };

    const getAvailableMonths = () => {
        const filteredRecords = selectedEmployee ? records.filter(r => r.userId === selectedEmployee) : records;
        const months = new Set<string>();
        filteredRecords.forEach(record => {
            const [year, month] = record.date.split('-');
            const monthKey = `${year}-${month}`;
            months.add(monthKey);
        });
        return Array.from(months).sort().reverse();
    };

    const getMonthName = (monthKey: string) => {
        const [year, month] = monthKey.split('-');
        const date = new Date(parseInt(year), parseInt(month) - 1);
        return date.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });
    };

    const formatTime = (timeString?: string) => {
        if (!timeString) return '-';
        return format(new Date(timeString), 'HH:mm', { locale: ptBR });
    };

    const formatDate = (dateString: string) => {
        if (!dateString || typeof dateString !== 'string') return '-';
        const parts = dateString.split('-');
        if (parts.length !== 3) return '-';
        const [year, month, day] = parts.map(Number);
        if (isNaN(year) || isNaN(month) || isNaN(day)) return '-';
        const date = new Date(year, month - 1, day);
        if (isNaN(date.getTime())) return '-';
        return format(date, 'dd/MM/yyyy', { locale: ptBR });
    };

    const formatHours = (hours?: number) => {
        if (hours === null || hours === undefined) return '-';
        const sign = hours < 0 ? '-' : '';
        const abs = Math.abs(hours);
        const h = Math.floor(abs);
        const m = Math.round((abs - h) * 60);
        return `${sign}${h}h ${m}m`;
    };

    const handleExport = async () => {
        if (!selectedEmployee) {
            toast.error('Selecione um colaborador');
            return;
        }
        if (!selectedMonth) {
            toast.error('Selecione um mês para exportar');
            return;
        }

        setIsExporting(true);

        const [year, month] = selectedMonth.split('-');
        const monthRecords = records.filter(record => {
            const [recordYear, recordMonth] = record.date.split('-');
            return record.userId === selectedEmployee &&
                recordYear === year &&
                recordMonth === month;
        });

        const groupedRecords = monthRecords.reduce((acc, record) => {
            if (!acc[record.date]) {
                acc[record.date] = [];
            }
            acc[record.date].push(record);
            return acc
        }, {} as Record<string, PointRecord[]>);

        const hasEditedRecords = monthRecords.some(record => record.hasEdits);

        const monthName = getMonthName(selectedMonth);
        const record = monthRecords[0];

        // Fetch latest time-bank for selected employee to ensure PDF shows same value as server
        let latestUserTimeBank: number | null = null;
        try {
            const resp = await fetch('/api/admin/time-bank');
            if (resp.ok) {
                const latest = await resp.json();
                const userTb = latest.find((t: { userId: string; totalTimeBank: number }) => t.userId === selectedEmployee);
                if (userTb) latestUserTimeBank = userTb.totalTimeBank;
            }
        } catch (e) {
            console.error('Erro ao buscar banco de horas mais recente:', e);
        }

        const bankDisplay = (() => {
            const userTimeBank = latestUserTimeBank !== null ? { totalTimeBank: latestUserTimeBank } : timeBankData?.find(tb => tb.userId === selectedEmployee);
            if (!userTimeBank) return '0h 0m';
            const sign = userTimeBank.totalTimeBank >= 0 ? '+' : '-';
            return `${sign}${formatHours(Math.abs(userTimeBank.totalTimeBank))}`;
        })();

        const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Registros de ponto - ${monthName} - ${record.userName}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.4;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #db5743;
        }
        .header .logo-b4 {
            max-width: 60px;
            height: auto;
        }
        .company {
            font-size: 20px;
            font-weight: bold;
            color: #db5743;
            margin-bottom: 8px;
        }
        .title {
            font-size: 20px;
            color: #555;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .info-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .period {
            font-size: 16px;
            font-weight: bold;
            color: #db5743;
        }
        .employee {
            font-size: 16px;
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            border-left: 1px solid #eee;
            border-right: 1px solid #eee;        
            overflow: hidden;
        }
        th {
            background: linear-gradient(135deg, #db5743, #c44a3a);
            color: white;
            padding: 15px 12px;
            text-align: center;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        tr:hover {
            background-color: #e9ecef;
        }
        .total-row {
            background: linear-gradient(135deg, #28a745, #20a039) !important;
            color: white;
            font-weight: bold;
            font-size: 15px;
        }
        .total-row td {
            border: none;
            padding: 15px 12px;
        }
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin: 50px 0 30px 0;
            padding: 30px 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .signature-box {
            text-align: center;
            flex: 1;
            margin: 0 20px;
        }
        .signature-line {
            width: 200px;
            height: 60px;
            border-bottom: 2px solid #333;
            margin: 0 auto 10px auto;
        }
        .signature-label {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .signature-name {
            font-size: 12px;
            color: #666;
        }
        .signature-date {
            font-size: 14px;
            color: #333;
            letter-spacing: 2px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        .footer .logo-b4desk {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }
        .summary {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            padding: 20px;
            background: #f1f3f4;
            border-radius: 8px;
        }
        .summary-item {
            text-align: center;
        }
        .summary-value {
            font-size: 24px;
            font-weight: bold;
            color: #db5743;
        }
        .summary-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        .time-badge {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            margin: 0 2px;
        }
        .time-separator {
            color: #999;
            margin: 0 4px;
        }
        .time-arrow {
            color: #666;
            margin: 0 6px;
            font-weight: bold;
        }
        .balance-positive {
            color: #28a745;
            font-weight: bold;
        }
        .balance-negative {
            color: #dc3545;
            font-weight: bold;
        }
        .balance-zero {
            color: #6c757d;
        }
        @media print {
            body { padding: 20px; }
            .header { page-break-after: avoid; }
            .info-section { page-break-after: avoid; }
            .summary { page-break-after: avoid; }
            table { page-break-inside: auto; }
            thead { display: table-header-group; }
            tbody { display: table-row-group; }
            tr { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="/logo-rodapé-17-b4(1).png" alt="Logo da B4" class="logo-b4">
        <div class="title">Relatório de registros de ponto</div>
    </div>
    
    <div class="info-section">
        <div class="period">Período: ${monthName}</div>
        <div class="employee">Colaborador: ${record.userName}</div>
    </div>
    
    <div class="summary">
        <div class="summary-item">
            <div class="summary-value">${monthRecords.length}</div>
            <div class="summary-label">Total de registros</div>
        </div>
        <div class="summary-item">
            <div class="summary-value">${(() => {
                const workedHours = monthRecords.reduce((acc, r) => acc + (r.totalHours || 0), 0);
                const [year, month] = selectedMonth.split('-');
                const monthExcusedDays = excusedDays?.filter(ed => {
                    const [edYear, edMonth] = ed.date.split('-');
                    return ed.userId === selectedEmployee && edYear === year && edMonth === month;
                }) || [];
                // Sum abonos as +8.8 and faltas (FALTA|) as -8.8
                const excusedHours = monthExcusedDays.reduce((sum, ed) => {
                    const isFalta = typeof ed.reason === 'string' && ed.reason.startsWith('FALTA|');
                    return sum + (isFalta ? -8.8 : 8.8);
                }, 0);
                const totalHours = workedHours + excusedHours;
                const sign = totalHours < 0 ? '-' : '';
                const abs = Math.abs(totalHours);
                const h = Math.floor(abs);
                const m = Math.round((abs - h) * 60);
                return `${sign}${h}h ${m}m`;
            })()}</div>
            <div class="summary-label">Horas trabalhadas</div>
        </div>
        <div class="summary-item">
            <div class="summary-value">${bankDisplay}</div>
            <div class="summary-label">Banco de horas total</div>
        </div>
    </div>
    
    <table>
        <thead>
            <tr>
                <th>Data</th>
                <th colspan="2">Horários</th>
                <th>Total de horas</th>
                <th>Criado por</th>
            </tr>
        </thead>
        <tbody>
            ${(() => {
                const [year, month] = selectedMonth.split('-');
                const monthExcusedDays = excusedDays?.filter(ed => {
                    const [edYear, edMonth] = ed.date.split('-');
                    return ed.userId === selectedEmployee && edYear === year && edMonth === month;
                }) || [];

                const allDates = [...Object.keys(groupedRecords), ...monthExcusedDays.map(ed => ed.date)];
                const uniqueDates = [...new Set(allDates)].sort();

                return uniqueDates.map(date => {
                    const dayRecords = groupedRecords[date];
                    const excusedDay = monthExcusedDays.find(ed => ed.date === date);
                    const isFalta = excusedDay && typeof excusedDay.reason === 'string' && excusedDay.reason.startsWith('FALTA|');

                    if (excusedDay && !dayRecords) {
                        if (isFalta) {
                            return `
                            <tr>
                                <td>${formatDate(date)}</td>
                                <td colspan="2"><span style="color: #dc3545; font-weight: bold;">Falta</span></td>
                                <td>-8h 48m</td>
                                <td>Admin</td>
                            </tr>
                            `;
                        }

                        return `
                        <tr>
                            <td>${formatDate(date)}</td>
                            <td colspan="2"><span style="color: #28a745; font-weight: bold;">Dia abonado</span></td>
                            <td>8h 48m</td>
                            <td>Admin</td>
                        </tr>
                        `;
                    }

                    if (dayRecords) {
                        const hasEdits = dayRecords.some(r => r.hasEdits);

                        const timeSlots = dayRecords.map(r => {
                            const entry = formatTime(r.clockIn);
                            const exit = formatTime(r.clockOut);
                            if (entry !== '-' && exit !== '-') {
                                return `<span class="time-badge">${entry}</span><span class="time-arrow">→</span><span class="time-badge">${exit}</span>`;
                            } else if (entry !== '-') {
                                return `<span class="time-badge">${entry}</span>`;
                            } else if (exit !== '-') {
                                return `<span class="time-badge">${exit}</span>`;
                            }
                            return '';
                        }).filter(slot => slot !== '').join('<span class="time-separator"> | </span>');

                        const totalHours = dayRecords.reduce((sum, r) => sum + (r.totalHours || 0), 0);
                        const createdBy = dayRecords.some(r => r.createdBy) ? 'Admin' : 'Usuário';

                        return `
                        <tr>
                            <td>${formatDate(date)}${hasEdits ? ' *' : ''}${excusedDay ? (isFalta ? ' (Falta)' : ' (Abonado)') : ''}</td>
                            <td colspan="2">${timeSlots || '-'}</td>
                            <td>${formatHours(totalHours)}</td>
                            <td>${createdBy}</td>
                        </tr>
                        `;
                    }

                    return '';
                }).filter(row => row !== '').join('');
            })()}
        </tbody>
    </table>
    
    ${hasEditedRecords ? `
    <div style="margin: 20px 0; padding: 15px; border: 1px solid #f1f1f1; border-radius: 8px; font-size: 14px;">
        <strong>Observação:</strong> Os registros marcados com (*) foram editados por um administrador. Para mais detalhes, consulte o histórico de alterações.
    </div>
    ` : ''}
    
    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-label">Assinatura do colaborador</div>
            <div class="signature-name">${record.userName}</div>
        </div>
        <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-label">Data</div>
            <div class="signature-date">____/____/________</div>
        </div>
    </div>
    
    <div class="footer">
        Relatório gerado automaticamente em ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}<br>
        <div class="logo-b4desk">
            <img src="/icon-b4desk.png" alt="B4Desk" width="15" height="15">
            <span>
                B4Desk
            </span>
        </div>
    </div>
    
    <script>
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>`;

        const printWindow = window.open('', '_blank');
        if (printWindow) {
            printWindow.document.write(htmlContent);
            printWindow.document.close();
        }

        setIsExporting(false);
        setIsOpen(false);
        toast.success('PDF gerado com sucesso!');
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" className="flex gap-2">
                    <FileText size={16} />
                    Exportar PDF
                </Button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Exportar registros em PDF</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                    <div>
                        <label className="text-sm font-medium">Selecione o colaborador:</label>
                        <Select value={selectedEmployee} onValueChange={(value) => {
                            setSelectedEmployee(value);
                            setSelectedMonth('');
                        }}>
                            <SelectTrigger className="mt-2">
                                <SelectValue placeholder="Escolha um colaborador" />
                            </SelectTrigger>
                            <SelectContent>
                                {getAvailableEmployees().map(([userId, userName]) => (
                                    <SelectItem key={userId} value={userId}>
                                        {userName}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    {selectedEmployee && (
                        <div>
                            <label className="text-sm font-medium">Selecione o mês:</label>
                            <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                                <SelectTrigger className="mt-2">
                                    <SelectValue placeholder="Escolha um mês" />
                                </SelectTrigger>
                                <SelectContent>
                                    {getAvailableMonths().map((month) => (
                                        <SelectItem key={month} value={month}>
                                            {getMonthName(month)}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    )}
                    <div className="flex gap-2 justify-end">
                        <Button variant="outline" onClick={() => setIsOpen(false)}>
                            Cancelar
                        </Button>
                        <Button onClick={handleExport} disabled={isExporting || !selectedEmployee || !selectedMonth}>
                            {isExporting ? 'Gerando' : 'Gerar PDF'}
                        </Button>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
}