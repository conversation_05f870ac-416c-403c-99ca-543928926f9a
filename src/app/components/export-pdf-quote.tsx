"use client"

import { Button } from './ui/button';
import { FileText } from 'lucide-react';
import { toast } from 'sonner';

const getServiceLabelFromMaps = (code?: string | null) => (code ? code : undefined);

type QuoteService = { Service?: { code?: string; name?: string }; BudgetItem?: { code?: string; name?: string }; customLabel?: string; quantity?: string | null; customDescription?: string | null; notes?: string | null; include?: boolean | null };

function getServiceLabel(s: QuoteService, code: string) {
    if (s.Service?.name) return s.Service.name;
    if (s.BudgetItem?.name) return s.BudgetItem.name;

    const custom = s.customLabel ?? '';
    const mappedFromCustom = getServiceLabelFromMaps(custom || code);
    if (custom && mappedFromCustom && mappedFromCustom !== (custom || code)) return mappedFromCustom;

    if (custom) return custom;

    return getServiceLabelFromMaps(code) ?? (code || '—');
}

export function ExportPDFQuote({ quote }: { quote: { id: string; client: string; socialMedia?: string[]; clientPhoneNumber?: string | null; budgetType?: string | null; services?: QuoteService[]; totalPrice?: number | null; discount?: number | null; createdAt?: string } }) {
    const escapeHtml = (input?: string | null) => {
        if (!input) return '';
        return String(input)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    };

    const handleExport = async () => {
        const q = quote;
        const createdAt = q.createdAt ? new Date(q.createdAt).toLocaleString() : '';
        let budgetLabel = '—';
        if (q.budgetType) {
            try {
                const r = await fetch(`/api/budget-templates?id=${encodeURIComponent(q.budgetType)}`);
                if (r.ok) {
                    const tmpl = await r.json();
                    budgetLabel = tmpl?.name ?? String(q.budgetType);
                } else {
                    budgetLabel = String(q.budgetType);
                }
            } catch {
                budgetLabel = String(q.budgetType);
            }
        }
        const servicesHtml = (q.services || []).map(s => {
            const code = s.Service?.code ?? s.BudgetItem?.code ?? '';
            const label = getServiceLabel(s, code);
            const desc = s.customDescription ?? '';
            const qty = s.quantity ?? '';
            const notes = s.notes ?? '';
            return [
                '<tr>',
                `<td style="padding:8px;border-bottom:1px solid #eee">${escapeHtml(label)}</td>`,
                `<td style="text-align: center; padding:8px;border-bottom:1px solid #eee">${escapeHtml(qty)}</td>`,
                `<td style="padding:8px;border-bottom:1px solid #eee">${escapeHtml(desc)}</td>`,
                `<td style="text-align: center; padding:8px;border-bottom:1px solid #eee">${escapeHtml(notes)}</td>`,
                '</tr>'
            ].join('\n');
        }).join('\n');

    const discountLine = (q.discount !== null && q.discount !== undefined && Number(q.discount) > 0)
        ? `<strong>Desconto:</strong> R$ ${Number(q.discount).toFixed(2)}<br>`
        : '';

    const html = `<!doctype html>
                    <html>
                        <head>
                            <meta charset="utf-8">
                            <title>Orçamento - ${escapeHtml(q.client)}</title>
                            <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;700&family=Vidaloka&display=swap" rel="stylesheet">
                            <style>
                                /* force printing background/colors */
                                html, body, * { -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important; }

                                a { color: blue; text-decoration: none; }

                                body { font-family: 'Quicksand', Arial, sans-serif; color: #222; padding: 20px; max-width: 800px; margin: 0 auto; }

                                .header { background: #70a987; color: #fff; padding: 28px 20px; display: flex; flex-direction: column; align-items: center; text-align: center; min-height: 400px; }
                                .header h1 { margin: 0; font-family: 'Vidaloka', serif; font-size: 36px; max-width: 820px; line-height: 1.15; display: flex; flex-direction: column; justify-content: center; align-items: center; flex: 1 1 auto; }
                                .header h1 span { font-size: 18px; display: block; margin-top: 8px; font-weight: 400; }

                                .date-footer { font-size: 12px; background: transparent; color: #fff; display: flex; flex-direction: column; justify-content: center; align-items: center; padding-top: 8px; margin: 0; margin-top: auto; }
                                .date-footer .date-created { margin: 0; font-size: 14px; }
                                .date-footer .date-validity { margin: 0; font-size: 11px; opacity: 0.8; text-transform: uppercase; }

                                .main { padding: 20px; background: #fff; border-left: 1px solid #c9c9c9; border-right: 1px solid #c9c9c9; }

                                .intro { font-size: 22px; font-family: 'Vidaloka', serif; color: #db5743; text-align: center; line-height: 1.5; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 1px solid #eee; }

                                table { width: 100%; border-collapse: collapse; margin-top: 16px; }
                                th { background: #f3f4f6; padding: 10px; text-align: left; }
                                td { padding: 8px; border-bottom: 1px solid #eee; }

                                .summary { margin-top: 16px; padding: 12px; background: #f8f9fa; }

                                .closing { text-align: center; font-size: 81px; font-family: 'Vidaloka', serif; color: #db5743; }

                                .footer { padding: 20px; border-left: 1px solid #c9c9c9; border-right: 1px solid #c9c9c9; border-bottom: 1px solid #c9c9c9; display: flex; gap: 16px; align-items: flex-end; }
                                .footer > .left { flex: 1 1 0; font-size: 14px; }
                                .footer > .right { flex: 1 1 0; display:flex; flex-direction: column; align-items: flex-end; gap: 8px; }
                                .footer .right div { font-size: 12px; display: flex; align-items: center; gap: 5px; }
                                .footer .emails { display:flex; align-items: center; gap: 10px; }
                                .footer .date-footer { text-align: right; font-size: 12px; color: #333; }
                                .footer .date-footer .date-created { margin: 0; }
                                .footer .date-footer .date-validity { margin: 0; font-size: 11px; opacity: 0.8; text-transform: uppercase; }

                                @media print { .header { -webkit-print-color-adjust: exact; print-color-adjust: exact; } }
                            </style>
                        </head>
                        <body>
                            <div class="header">
                                <h1>“Não se trata de ideias. Trata-se de fazer as ideias acontecerem.” <span>- Scott Belsky</span></h1>
                                <div class="date-footer">
                                    <img src="/Logotipo-12.png" alt="Logo da B4" style="max-width: 60px; height: auto;">
                                    <div>
                                        <p class="date-created"><strong>Data:</strong> ${escapeHtml(createdAt)}</p>
                                        <p class="date-validity">Orçamento válido por 15 dias</p>
                                    </div>
                                </div>
                            </div>

                            <div class="main">
                                <p class="intro">
                                    Fique tranquilo, essa missão vai ser moleza com a nossa ajuda! :)
                                </p>

                                <div><strong>Cliente:</strong> ${escapeHtml(q.client)}</div>
                                <div><strong>Redes sociais:</strong> ${q.socialMedia && q.socialMedia.length > 0 ? escapeHtml(q.socialMedia.join(', ')) : '—'}</div>
                                <div><strong>Telefone:</strong> ${escapeHtml(q.clientPhoneNumber ?? '') || '—'}</div>
                                <div><strong>O que será desenvolvido:</strong> ${escapeHtml(budgetLabel)}</div>
                                <table>
                                    <thead>
                                        <tr>
                                            <th>Serviço</th>
                                            <th>Quantidade</th>
                                            <th>Descrição</th>
                                            <th>Horas</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${servicesHtml}
                                    </tbody>
                                </table>

                                <div class="summary">
                                    ${discountLine}
                                    <strong>Preço total:</strong> ${q.totalPrice !== null && q.totalPrice !== undefined ? `R$ ${Number(q.totalPrice).toFixed(2)}` : '—'} mês
                                </div>

                                <p class="closing">
                                    Obrigado!
                                </p>
                            </div>

                            <div class="footer">
                                <div class="left">
                                    <p>
                                        Nosso time está à disposição para fazer o seu negócio alavancar.
                                        Criatividade que conecta. Estratégias que convertem.
                                    </p>
                                    <div class="emails">
                                        <a href="mailto:<EMAIL>" target="_blank">
                                            <EMAIL>
                                        </a>
                                        |
                                        <a href="mailto:<EMAIL>" target="_blank">
                                            <EMAIL>
                                        </a>
                                    </div>
                                    <div>
                                        (54) 99954-4025 | (54) 99947-5327
                                        <br>
                                        <a href="https://www.b4comunicacao.com.br" target="_blank">
                                            b4comunicacao.com.br
                                        </a>
                                    </div>
                                </div>
                                <div class="right">
                                    <img src="/logo-rodapé-17-b4(1).png" alt="Logo da B4" style="max-width:60px; height: auto;">
                                    <div>
                                        Criado com B4Desk
                                        <img src="/icon-b4desk.png" alt="B4Desk" width="15" height="15">
                                    </div>
                                </div>
                            </div>
                        </body>
                    </html>`;

        const w = window.open('', '_blank');
        if (w) {
            w.document.write(html);
            w.document.close();
            try {
                w.focus();
                if (w.document.readyState === 'complete') {
                    w.print();
                } else {
                    w.onload = () => { try { w.focus(); w.print(); } catch { } };
                    setTimeout(() => { try { w.focus(); w.print(); } catch { } }, 600);
                }
            } catch {
            }
        }
        toast.success('PDF preparado — use a função de impressão do navegador para salvar como PDF');
    };

    return (
        <Button variant="outline" size="icon" title="Exportar para PDF" onClick={handleExport}>
            <FileText size={16} />
        </Button>
    );
}

export default ExportPDFQuote;
