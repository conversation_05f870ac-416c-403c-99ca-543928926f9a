"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { ExternalLink } from "lucide-react";
import { Badge } from "@/app/components/ui/badge";
import { Separator } from "../ui/separator";

type ClientPlanning = {
    clientId: string;
    clientName: string;
    count: number;
}

type ClientsForFeedApiItem = {
    clientId: string;
    clientName: string;
    planningsCount: number;
    plannings: { id: string; month: number; year: number }[];
}

export const FeedStructuringTasks = () => {
    const [clientsWithPlannings, setClientsWithPlannings] = useState<ClientPlanning[]>([]);
    useEffect(() => {
        const fetchForFeed = async () => {
            try {
                const now = new Date();
                const month = now.getMonth() + 1;
                const year = now.getFullYear();

                const res = await fetch(`/api/clients-for-feed-structuring?month=${month}&year=${year}`);
                if (!res.ok) {
                    console.error('Erro ao buscar clients for feed structuring:', res.status);
                    return;
                }

                const data = (await res.json()) as ClientsForFeedApiItem[];

                const mapped = data.map((d) => ({ clientId: d.clientId, clientName: d.clientName, count: d.planningsCount } as ClientPlanning));
                setClientsWithPlannings(mapped);
            } catch (error) {
                console.error('Erro ao buscar clientes para estruturação de feed:', error);
            }
        };

        fetchForFeed();
    }, []);

    if (clientsWithPlannings.length === 0) {
        return (
            <p className="flex justify-center items-center h-40 text-sm text-slate-500 dark:text-slate-400">
                Não há clientes com planejamentos aprovados para estruturação do Feed.
            </p>
        );
    }

    return (
        <div className="overflow-auto max-h-[400px]">
            <Separator className="my-2" />
            <p className="text-sm text-muted-foreground mb-2">
                Clientes com planejamento pronto para estruturação
            </p>
            <ul className="space-y-2">
                {clientsWithPlannings.slice(0, 6).map((c) => (
                    <li key={c.clientId} className="text-sm p-2 border rounded-md hover:bg-gray-50 dark:hover:bg-zinc-800 bg-gray-100 dark:bg-zinc-900">
                        <Link href={`/feed-structuring/${c.clientId}`} className="block">
                            <div className="flex items-center justify-between">
                                <div className="font-medium">{c.clientName}</div>
                                <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="text-xs">{c.count}</Badge>
                                    <span className="p-1 rounded bg-orange-100 dark:bg-orange-950">
                                        <ExternalLink size={16} className="text-primary2" />
                                    </span>
                                </div>
                            </div>
                        </Link>
                    </li>
                ))}

                {clientsWithPlannings.length > 6 && (
                    <li className="text-center py-2">
                        <Link href="/monthly-planning" className="text-xs text-blue-600 hover:underline">Ver todos os {clientsWithPlannings.length} clientes</Link>
                    </li>
                )}
            </ul>
        </div>
    );
};