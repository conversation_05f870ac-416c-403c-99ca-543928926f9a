"use client";

import { usePathname } from "next/navigation";
import { FeedbackButton } from "./feedback-button";

export function ConditionalFeedbackButton() {
    const pathname = usePathname();
    
    const excludedRoutes = ["/", "/login", "/signup"];
    
    if (excludedRoutes.includes(pathname) || pathname?.startsWith("/login") || pathname?.includes('/signup') || pathname?.includes("/auth/")) {
        return null;
    }

    return <FeedbackButton />;
}