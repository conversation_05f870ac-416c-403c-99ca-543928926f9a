"use client";

import dynamic from "next/dynamic";
import { usePathname } from "next/navigation";

const FeedbackButtonContainer = dynamic(
    () => import('./feedback-button-container'),
    { ssr: false }
);

export default function DynamicFeedbackButton() {
    const pathname = usePathname();
    
    const excludedRoutes = ["/", "/login", "/signup"];
    
    if (excludedRoutes.includes(pathname) || 
        pathname?.startsWith("/login") || 
        pathname?.includes('/signup') ||
        pathname?.includes("/auth/") || 
        pathname?.includes('/feed-structure-pdf')) {
        return null;
    }

    return <FeedbackButtonContainer />;
}