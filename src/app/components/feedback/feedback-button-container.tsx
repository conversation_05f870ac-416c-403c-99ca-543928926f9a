"use client";

import { usePathname } from "next/navigation";
import { FeedbackButton } from "./feedback-button";

export default function FeedbackButtonContainer() {
    const pathname = usePathname();
    
    const excludedRoutes = ["/", "/login", "/signup"];
    
    if (excludedRoutes.includes(pathname) || 
        pathname?.startsWith("/login") || 
        pathname?.includes('/signup') || 
        pathname?.includes("/auth/") ||
        pathname?.includes('/feed-structure-pdf')) {
        return null;
    }

    return <FeedbackButton />;
}