"use client";

import { useState, useEffect } from "react";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Ellipsis, MessageSquarePlusIcon, SendIcon } from "lucide-react";
import { toast } from "sonner";
import { useSession } from "next-auth/react";
import { FeedbackType } from "@/types/feedback";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

export function FeedbackButton() {
    const [open, setOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { data: session } = useSession();
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        const checkIfMobile = () => {
            setIsMobile(window.innerWidth < 640);
        };

        checkIfMobile();

        window.addEventListener('resize', checkIfMobile);

        return () => window.removeEventListener('resize', checkIfMobile);
    }, []);

    const [feedback, setFeedback] = useState({
        title: "",
        description: "",
        type: "SUGGESTION" as FeedbackType
    });

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!feedback.title || !feedback.description) {
            toast.error("Preencha todos os campos obrigatórios");
            return;
        }

        setIsSubmitting(true);

        try {
            const response = await fetch("/api/feedback", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({
                    ...feedback,
                    userEmail: session?.user?.email
                }),
            });

            if (response.ok) {
                toast.success("Feedback enviado com sucesso!");
                setFeedback({ title: "", description: "", type: "SUGGESTION" });
                setOpen(false);
            } else {
                toast.error("Erro ao enviar feedback");
            }
        } catch (error) {
            console.error("Erro ao enviar feedback:", error);
            toast.error("Erro ao enviar feedback");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="hidden md:block fixed bottom-4 md:bottom-10 right-4 z-50">
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <Button
                        size="icon"
                        className="h-12 w-12 rounded-full shadow-lg bg-primary hover:bg-primary/90"
                        title="Enviar feedback"
                    >
                        <MessageSquarePlusIcon size={20} />
                    </Button>
                </PopoverTrigger>
                <PopoverContent
                    className={`${isMobile ? 'w-[calc(100vw-32px)]' : 'mr-4 w-80'} p-4`}
                    side={isMobile ? "top" : "top"}
                    align={isMobile ? "end" : "center"}
                    alignOffset={0}
                    sideOffset={10}
                    avoidCollisions={true}
                >
                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="space-y-2 text-center">
                            <h3 className="text-lg font-medium">Envie seu feedback</h3>
                            <p className="text-sm text-muted-foreground">
                                Sua opinião é muito importante para melhorarmos o sistema.
                            </p>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="type">Tipo</Label>
                            <div className="flex flex-col space-y-1.5">
                                <Select
                                    value={feedback.type}
                                    onValueChange={(value) => setFeedback({ ...feedback, type: value as FeedbackType })}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Selecione o tipo de feedback" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="SUGGESTION">Sugestão</SelectItem>
                                        <SelectItem value="BUG">Bug</SelectItem>
                                        <SelectItem value="QUESTION">Dúvida</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="title">Título</Label>
                            <Input
                                id="title"
                                placeholder="Resumo do feedback"
                                value={feedback.title}
                                onChange={(e) => setFeedback({ ...feedback, title: e.target.value })}
                                required
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description">Descrição</Label>
                            <Textarea
                                id="description"
                                placeholder="Descreva com detalhes..."
                                rows={3}
                                value={feedback.description}
                                onChange={(e) => setFeedback({ ...feedback, description: e.target.value })}
                                required
                            />
                        </div>

                        <Button
                            type="submit"
                            className="w-full"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? (
                                <Ellipsis />
                            ) : (
                                <>
                                    <SendIcon />
                                    <span>Enviar feedback</span>
                                </>
                            )}
                        </Button>
                    </form>
                </PopoverContent>
            </Popover>
        </div>
    );
}