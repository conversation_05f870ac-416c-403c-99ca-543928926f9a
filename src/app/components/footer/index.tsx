import { useDbInfo } from "@/app/hooks/useDbInfo";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useEffect, useState } from "react";

export const Footer = () => {
    const { data: session, status } = useSession();
    const { dbInfo } = useDbInfo();
    const [isDev, setIsDev] = useState(false);

    const currentYear = new Date().getFullYear();

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        setIsDev(user?.role === "DEVELOPER");
                    } else {
                        console.error("Erro ao buscar função do usuário:", await response.json());
                    }
                }
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
            }
        };

        fetchUserRole();
    }, [status, session]);

    return (
        <footer className="hidden bg-zinc-50 dark:bg-zinc-900 border-t dark:border-zinc-800 p-4 text-sm text-zinc-600 dark:text-zinc-300 md:flex flex-col md:flex-row gap-2 items-center justify-between">
            <p>
                © {currentYear} B4Desk - B4 Comunicação. Todos os direitos reservados.
            </p>
            <div className="flex items-center gap-1">
                <div className="text-xs mt-1">
                    {dbInfo && isDev ? (
                        <span className={`px-2 py-0.5 rounded ${dbInfo.environment === 'local' ? 'bg-emerald-100 dark:bg-emerald-950 dark:text-emerald-100' : 'bg-amber-100 dark:bg-amber-950 text-amber-800 dark:text-amber-100'}`}
                        >
                            Ambiente: {dbInfo.environment} • Banco: {dbInfo.database}
                        </span>
                    ) : null}
                </div>
                <Link href="/updates" className="flex items-center gap-1">
                    <div className={`w-2 h-2 rounded-full ${dbInfo?.isConnected ? 'animate-pulse' : 'animate-none'} ${dbInfo?.isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    v1.0.2
                </Link>
            </div>
        </footer>
    );
};
