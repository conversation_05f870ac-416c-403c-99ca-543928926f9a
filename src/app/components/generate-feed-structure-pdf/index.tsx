"use client";

import { useState } from "react";
import { Button } from "@/app/components/ui/button";
import { Ellipsis, Download } from "lucide-react";
import { toast } from "sonner";


interface Content {
    id: string;
    activityDate: string;
    contentType: string;
    channel: string;
    details: string;
    destination: string;
    caption?: string;
    status?: string;
    copywriting?: string;
    reference?: string;
    urlStructuringFeed?: string | string[];
    urlTypes?: string[];
    urlMediaTypes?: string[];
    planningId: string;
    week: number;
    month: number;
    year: number;
}

interface GenerateFeedStructurePDFProps {
    clientId: string;
    clientName: string;
    month?: string;
    disabled?: boolean;
    contents?: Content[];
}

export const GenerateFeedStructurePDF = ({
    clientId,
    clientName,
    month,
    disabled = false,
    contents,
}: GenerateFeedStructurePDFProps) => {
    const [isGenerating, setIsGenerating] = useState(false);


    const handleGeneratePDF = async () => {
        if (!clientId || disabled) return;
        // Se a prop contents existir e for um array, filtrar os cards do PDF na rota/iframe usando localStorage
        if (Array.isArray(contents)) {
            try {
                localStorage.setItem(`pdf-contents-${clientId}`, JSON.stringify(contents.map(c => c.id)));
            } catch {}
        }

        setIsGenerating(true);


        try {
            const iframe = document.createElement("iframe");
            iframe.style.width = "1100px";
            iframe.style.height = "1px";
            iframe.style.position = "absolute";
            iframe.style.top = "-9999px";
            iframe.style.left = "-9999px";
            document.body.appendChild(iframe);

            const url = `/feed-structure-pdf?id=${clientId}${month ? `&month=${month}` : ""}`;
            iframe.src = url;


            iframe.onload = async () => {
                try {
                    const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
                    if (!iframeDocument) {
                        throw new Error("Não foi possível acessar o conteúdo do PDF");
                    }

                    // Garantir que o CSS do PDF está presente no iframe
                    const styleHref = '/app/feed-structure-pdf/pdf-styles.css';
                    if (iframeDocument.head && !iframeDocument.head.querySelector(`link[href*="${styleHref}"]`)) {
                        const link = iframeDocument.createElement('link');
                        link.rel = 'stylesheet';
                        link.href = styleHref;
                        iframeDocument.head.appendChild(link);
                    }

                    // Função para aguardar o carregamento dos elementos .pdf-page-break
                    async function waitForPdfPages(maxWaitMs = 15000, intervalMs = 100): Promise<NodeListOf<HTMLElement> | null> {
                        const start = Date.now();
                        return new Promise((resolve, reject) => {
                            function check() {
                                if (!iframeDocument) {
                                    reject(new Error('iframeDocument não disponível.'));
                                    return;
                                }
                                const pdfPages = iframeDocument.querySelectorAll('.pdf-page-break');
                                if (pdfPages && pdfPages.length > 0) {
                                    resolve(pdfPages as NodeListOf<HTMLElement>);
                                } else if (Date.now() - start > maxWaitMs) {
                                    // Log para depuração
                                    const allDivs = Array.from(iframeDocument.querySelectorAll('div'));
                                    const classList = allDivs.map(div => div.className).filter(Boolean);
                                    console.warn('Nenhuma .pdf-page-break encontrada. Classes de divs na página:', classList);
                                    // Fallback: tentar .pdf-grid
                                    const pdfGrids = iframeDocument.querySelectorAll('.pdf-grid');
                                    if (pdfGrids && pdfGrids.length > 0) {
                                        console.warn('Usando .pdf-grid como fallback para exportação do PDF.');
                                        resolve(pdfGrids as NodeListOf<HTMLElement>);
                                    } else {
                                        reject(new Error('Nenhuma página encontrada para exportar'));
                                    }
                                } else {
                                    setTimeout(check, intervalMs);
                                }
                            }
                            check();
                        });
                    }

                    // Importar jsPDF e html2canvas dinamicamente
                    const [{ jsPDF }, html2canvas] = await Promise.all([
                        import("jspdf"),
                        import("html2canvas")
                    ]);

                    // Esperar as páginas aparecerem

                    const pdfPages = await waitForPdfPages();

                    if (!pdfPages || pdfPages.length === 0) {
                        throw new Error('Nenhuma página encontrada para exportar (nem .pdf-page-break nem .pdf-grid)');
                    }

                    // Criar PDF em landscape A4
                    const pdf = new jsPDF({ orientation: 'landscape', unit: 'mm', format: 'a4' });
                    const pageWidth = pdf.internal.pageSize.getWidth();
                    const pageHeight = pdf.internal.pageSize.getHeight();

                    for (let i = 0; i < pdfPages.length; i++) {
                        const page = pdfPages[i];
                        // Capturar cada página como imagem
                        const canvas = await html2canvas.default(page, {
                            scale: 2,
                            useCORS: true
                        });
                        const imgData = canvas.toDataURL('image/jpeg', 0.98);

                        // Calcular proporção para caber na página SEM esticar
                        const canvasRatio = canvas.width / canvas.height;
                        const pageRatio = pageWidth / pageHeight;
                        let pdfWidth, pdfHeight;
                        if (canvasRatio > pageRatio) {
                            pdfWidth = pageWidth;
                            pdfHeight = pageWidth / canvasRatio;
                        } else {
                            pdfHeight = pageHeight;
                            pdfWidth = pageHeight * canvasRatio;
                        }
                        const x = (pageWidth - pdfWidth) / 2;
                        const y = (pageHeight - pdfHeight) / 2;

                        if (i > 0) pdf.addPage();
                        pdf.addImage(imgData, 'JPEG', x, y, pdfWidth, pdfHeight);
                    }

                    pdf.save(`estrutura-feed-${clientName.replace(/\s+/g, "_")}.pdf`);

                } catch (error) {
                    console.error("Erro ao gerar PDF:", error);
                    toast.error("Erro ao gerar PDF");
                } finally {
                    document.body.removeChild(iframe);
                    setIsGenerating(false);
                }
            };

            iframe.onerror = () => {
                document.body.removeChild(iframe);
                setIsGenerating(false);
                toast.error("Erro ao carregar conteúdo para o PDF");
            };
        } catch (error) {
            console.error("Erro ao iniciar geração do PDF:", error);
            toast.error("Erro ao gerar PDF");
            setIsGenerating(false);
        }
    };

    return (
        <Button
            title="Exportar para PDF"
            className="w-full sm:w-auto"
            onClick={handleGeneratePDF}
            disabled={disabled || isGenerating}
        >
            {isGenerating ? <Ellipsis /> : <Download />}
            Exportar PDF
        </Button>
    );
};
