import jsPDF from 'jspdf';
import { toast } from "sonner";
import { Client } from '@prisma/client';
import { geistFontBase64, geistBoldFontBase64, vidaLokaRegularFontBase64 } from './fonts';

interface Planning {
    id: string;
    month: string;
    year: number;
    activities: {
        id: string;
        week: number;
        description: string;
        contents: {
            id: string;
            activityDate: string;
            contentType: string;
            channel: string;
            details: string;
            destination: string;
        }[];
    }[];
}

interface ExtendedClient extends Client {
    monthlyPlannings: Planning[];
}

interface PlanningContent {
    id: string;
    activityDate: string;
    contentType: string;
    channel: string;
    details: string;
    destination: string;
}

const addCustomFonts = (doc: jsPDF) => {
    try {
        doc.addFileToVFS('Geist-Regular.ttf', geistFontBase64);
        doc.addFont('Geist-Regular.ttf', 'Geist', 'normal');
        doc.addFileToVFS('Geist-Bold.ttf', geistBoldFontBase64);
        doc.addFont('Geist-Bold.ttf', 'Geist', 'bold');
        doc.addFileToVFS('Vidaloka-Regular.ttf', vidaLokaRegularFontBase64);
        doc.addFont('Vidaloka-Regular.ttf', 'Vidaloka', 'normal');
    } catch (error) {
        console.error('Erro ao registrar fontes:', error);
    }
};

const generatePDF = async (
    client: ExtendedClient,
    planningId: string,
    monthName: (month: number) => string
) => {
    if (!client) return;

    const planning = client.monthlyPlannings.find(p => p.id === planningId);
    if (!planning) return;

    try {
        toast.info("Gerando PDF, por favor aguarde...");

        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });

        // Adicione todas as variáveis de página aqui
        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();
        const footerHeight = 50; // Aumentado para 50mm para garantir espaço suficiente
        const safeAreaHeight = pageHeight - footerHeight;

        addCustomFonts(pdf);

        const fontFamily = 'Geist';
        try {
            pdf.setFont(fontFamily);
        } catch (e) {
            console.log('Fonte personalizada não disponível, usando padrão', e);
        }

        // Define footer safe area

        // Cabeçalho
        pdf.setFontSize(11);
        pdf.setTextColor(44, 62, 80);

        // First text - "Etapa 2" with orange background and white text
        const firstTextX = 20;
        const firstTextY = 20;
        const firstTextWidth = 34;
        const firstTextHeight = 9;

        // Draw orange background rounded rectangle
        pdf.setFillColor(229, 71, 41);
        const radius = 4;
        pdf.roundedRect(firstTextX - 2, firstTextY - 5, firstTextWidth, firstTextHeight, radius, radius, 'F');

        // Add white text with proper vertical centering
        pdf.setTextColor(255, 255, 255);
        pdf.text("Etapa 2", firstTextX + 3, firstTextY + 1.5); // Adjusted vertical position

        // Second text - "Planejamento" with rounded border
        const secondTextX = 58;
        const secondTextY = 20;
        const secondTextWidth = 50;
        const secondTextHeight = 9;

        // Draw rounded border
        pdf.setDrawColor(229, 71, 41);
        pdf.setLineWidth(0.3);
        pdf.roundedRect(secondTextX - 2, secondTextY - 5, secondTextWidth, secondTextHeight, radius, radius, 'S');

        // Add orange text with proper vertical centering
        pdf.setTextColor(229, 71, 41);
        pdf.text("Planejamento", secondTextX + 3, secondTextY + 1.5); // Adjusted vertical position

        // Reset text color for rest of document
        pdf.setTextColor(44, 62, 80);

        pdf.setTextColor(229, 71, 41);
        pdf.setFontSize(24);
        pdf.text("ESTRATÉGIA DE CONTEÚDO", 20, 135);

        pdf.setFontSize(54);
        pdf.setTextColor(97, 174, 131);
        pdf.setFont(fontFamily, 'bold');
        pdf.text(`${monthName(Number(planning.month))} ${planning.year}`.toUpperCase(), 20, 155);
        pdf.setFont(fontFamily, 'normal');
        pdf.setFontSize(18);
        pdf.setTextColor(229, 71, 41);
        pdf.text(`${client.name}`, 20, 165);

        pdf.setFontSize(12);
        // Define text and styling properties
        const text = "Estruturar o planejamento de conteúdo para o mês com clareza e organização.";
        const textMaxWidth = 170;
        const boxPadding = 3; // Padding inside the box
        const boxRadius = 6; // Radius for rounded corners

        // Calculate dimensions
        const centerX = pageWidth / 2;
        const textY = 275;

        // Get wrapped text to calculate height
        const splitText = pdf.splitTextToSize(text, textMaxWidth);
        const lineHeight = 7;
        const textHeight = splitText.length * lineHeight;

        // Calculate box dimensions
        const boxWidth = textMaxWidth + (boxPadding * 2);
        const boxHeight = textHeight + (boxPadding * 2);
        const boxX = centerX - (boxWidth / 2);
        const boxY = textY - 5 - boxPadding;

        // Draw orange rounded rectangle border
        pdf.setDrawColor(229, 71, 41); // Orange color
        pdf.setLineWidth(0.3);
        pdf.roundedRect(boxX, boxY, boxWidth, boxHeight, boxRadius, boxRadius, 'S');

        // Add centered text with orange color
        pdf.setTextColor(229, 71, 41); // Alterando para laranja
        pdf.text(text, centerX, textY, {
            maxWidth: textMaxWidth,
            align: 'center'
        });

        pdf.setTextColor(44, 62, 80); // Reset text color back to default

        // Iniciar o conteúdo das semanas depois do título
        let yPosition = 300;

        // Agrupar atividades por semana
        const weeklyActivities = planning.activities.reduce((grouped, activity) => {
            const weekNum = activity.week;
            if (!grouped[weekNum]) {
                grouped[weekNum] = [];
            }
            grouped[weekNum].push(activity);
            return grouped;
        }, {} as Record<number, typeof planning.activities>);

        const sortedWeeks = Object.keys(weeklyActivities).sort((a, b) => parseInt(a) - parseInt(b));

        // Função que adiciona um item completo de forma atômica
        const addItemToPDF = (content: PlanningContent, index: number, isLastInWeek: boolean): boolean => {
            // 1. Calcular altura total que o item vai ocupar
            const typeText = content.contentType;
            const typeWidth = pdf.getStringUnitWidth(typeText) * 10 / pdf.internal.scaleFactor;
            const typeBoxWidth = typeWidth + 6;
            const typeBoxHeight = 5;

            const availableWidth = 140 - (35 + typeBoxWidth + 4) - 8;
            const channelMaxWidth = 20;
            const contentDetails = pdf.splitTextToSize(content.details, availableWidth);
            const channelText = pdf.splitTextToSize(content.channel, channelMaxWidth);

            const detailsHeight = contentDetails.length * 5;
            const channelHeight = channelText.length * 5;
            const itemHeight = Math.max(detailsHeight, channelHeight, 10) + 4;

            // 2. Verificar se o item cabe na página atual
            if (yPosition + itemHeight > pageHeight - footerHeight) {
                pdf.addPage();
                yPosition = 20;

                // Redesenhar cabeçalho da tabela na nova página
                pdf.setFontSize(11);
                pdf.text("data", 15, yPosition);
                pdf.text("conteúdo", 35, yPosition);
                pdf.text("destino", 140, yPosition);
                pdf.text("canal", 175, yPosition);

                yPosition += 5;
                pdf.line(15, yPosition, 195, yPosition);
                yPosition += 5;
            }

            // Posição vertical definida apenas uma vez para todos os componentes
            const contentY = yPosition + 2;

            // 3. SALVAR O ESTADO ATUAL DO PDF antes de adicionar o item
            const currentPageNumber = pdf.internal.pages.length - 1;

            try {
                // 4. Tentar adicionar o item completo
                // Data
                const formattedDate = new Date(content.activityDate).toLocaleDateString("pt-BR", { day: "2-digit", month: "2-digit" });
                pdf.setFontSize(14);
                pdf.setFont(fontFamily, 'bold');
                pdf.text(formattedDate, 15, contentY);
                pdf.setFont(fontFamily, 'normal');

                // Tipo (retângulo laranja)
                const contentX = 35;
                pdf.setFillColor(229, 71, 41);
                pdf.roundedRect(contentX, contentY - 4, typeBoxWidth, typeBoxHeight, 2, 2, 'F');

                // Texto do tipo
                pdf.setTextColor(255, 255, 255);
                pdf.setFontSize(8);
                pdf.text(typeText, contentX + 3, contentY - 0.5);

                // Detalhes, destino e canal
                pdf.setTextColor(44, 62, 80);
                pdf.setFontSize(10);

                const detailsX = contentX + typeBoxWidth + 4;
                pdf.text(contentDetails, detailsX, contentY);
                pdf.text(content.destination, 140, contentY);

                pdf.setTextColor(229, 71, 41);
                pdf.text(channelText, 175, contentY);
                pdf.setTextColor(44, 62, 80);

                // 5. Se chegamos aqui sem erros, atualizar posição
                yPosition = contentY + itemHeight;

                return true;
            } catch (e) {
                // 6. Se falhar, restaurar o estado anterior e forçar nova página
                console.error("Erro ao adicionar item:", e);

                // Restaurar para o estado anterior
                pdf.setPage(currentPageNumber);

                // Força uma nova página e tenta novamente
                pdf.addPage();
                yPosition = 20;

                // Redesenhar cabeçalho
                pdf.setFontSize(11);
                pdf.text("data", 15, yPosition);
                pdf.text("conteúdo", 35, yPosition);
                pdf.text("destino", 140, yPosition);
                pdf.text("canal", 175, yPosition);

                yPosition += 5;
                pdf.line(15, yPosition, 195, yPosition);
                yPosition += 5;

                // Chamar recursivamente para tentar adicionar na nova página
                return addItemToPDF(content, index, isLastInWeek);
            }
        };

        // No loop principal, usar a função auxiliar
        for (const weekNum of sortedWeeks) {
            const activities = weeklyActivities[parseInt(weekNum)];

            // Cada semana começa em nova página
            pdf.addPage();
            yPosition = 20;

            const weekTitle = `SEMANA ${weekNum}`;
            pdf.setFontSize(20);
            pdf.setFont(fontFamily, 'bold');

            // Cor verde vibrante (mesma do título principal)
            pdf.setTextColor(97, 174, 131);

            // Título da semana alinhado à esquerda
            pdf.text(weekTitle, 15, yPosition);

            // Linha decorativa abaixo do título
            const lineY = yPosition + 2;
            pdf.setDrawColor(229, 71, 41); // laranja da identidade
            pdf.setLineWidth(0.2);
            pdf.line(15, lineY, pageWidth - 15, lineY);

            // Espaço após o título
            yPosition = lineY + 8;

            // Reset estilos
            pdf.setFont(fontFamily, 'normal');
            pdf.setTextColor(44, 62, 80);

            // ADICIONADO: Descrição da semana (primeira atividade da semana)
            if (activities.length > 0 && activities[0].description) {
                pdf.setFontSize(11);

                // Quebrar o texto da descrição se for muito longo
                const descriptionText = pdf.splitTextToSize(activities[0].description, 180);
                pdf.text(descriptionText, 15, yPosition);
                
                pdf.setFont(fontFamily, 'normal'); // Reset font back to normal

                // Ajustar a posição Y baseado no número de linhas da descrição
                yPosition += descriptionText.length * 5 + 5;
            } else {
                yPosition += 10; // Espaço padrão se não houver descrição
            }

            // Cabeçalho da tabela
            pdf.setFontSize(11);
            pdf.setTextColor(44, 62, 80);
            pdf.text("data", 15, yPosition);
            pdf.text("conteúdo", 35, yPosition);
            pdf.text("destino", 140, yPosition);
            pdf.text("canal", 175, yPosition);

            yPosition += 5;
            pdf.line(15, yPosition, 195, yPosition);
            yPosition += 5;

            // Processar itens da semana com a função auxiliar
            const weekContents = activities.flatMap(activity => activity.contents);

            for (let index = 0; index < weekContents.length; index++) {
                const content = weekContents[index];
                const isLastInWeek = index === weekContents.length - 1;

                // Usar a função auxiliar que garante que o item fique completo em uma página
                addItemToPDF(content, index, isLastInWeek);
            }
        }

        yPosition += 20;

        // Verificar se há espaço suficiente para o rodapé completo
        // Valor mais preciso baseado nos elementos do rodapé
        const footerContentHeight = 10;

        if (yPosition + footerContentHeight > safeAreaHeight) {
            pdf.addPage();
            yPosition = 40;
        }

        // Antes de adicionar o rodapé, verificar novamente se há espaço suficiente
        // Valor mais preciso para o rodapé completo (texto + imagem + barra)
        const footerNeededSpace = 50; // Aumentado para considerar todo o conteúdo do rodapé

        if (yPosition + footerNeededSpace > pageHeight - 10) {
            pdf.addPage();
            yPosition = 20;
        }

        // Ajustar o posicionamento do "Obrigado!" para evitar sobreposição
        pdf.setFontSize(54);
        pdf.setTextColor(229, 71, 41); // Set to orange color
        pdf.setFont('Vidaloka', 'normal');

        const obrigadoSpacing = 30;
        const obrigadoY = Math.min(yPosition + obrigadoSpacing, pageHeight - 80);

        pdf.text("Obrigado!", 105, obrigadoY, { align: 'center' });
        pdf.setFont(fontFamily, 'normal');

        yPosition += 50;

        const barHeight = 9;

        const totalPages = pdf.internal.pages.length - 1;

        pdf.setPage(totalPages);

        pdf.setFillColor(229, 71, 41);
        pdf.rect(0, pageHeight - barHeight, pageWidth, barHeight, 'F');

        pdf.setPage(totalPages);

        const footerY = pageHeight - 40;

        pdf.setFillColor(229, 71, 41);
        pdf.rect(0, pageHeight - barHeight, pageWidth, barHeight, 'F');

        // Define left and right columns
        const leftColumnX = 15;
        const rightColumnX = 175;

        // Define image size
        const imageWidth = 12;
        const imageHeight = 14;

        try {
            const logoPath = '/logo-rodapé-17-b4(1).png';
            pdf.addImage(logoPath, 'PNG', rightColumnX, footerY - 0, imageWidth, imageHeight);
        } catch (error) {
            console.error('Error adding image:', error);
        }

        // Left column text
        pdf.setFontSize(9);
        pdf.setTextColor(44, 62, 80);
        pdf.text("Nosso time está à disposição para fazer o seu negócio alavancar.", leftColumnX, footerY);
        pdf.text("Criatividade que conecta. Estratégias que convertem.", leftColumnX, footerY + 5);

        // Set text color to link blue
        pdf.setTextColor(0, 0, 238);
        pdf.text("<EMAIL> | <EMAIL>", leftColumnX, footerY + 10);
        pdf.text("(54) 99954-4025 | (54) 99947-5327", leftColumnX, footerY + 15);
        pdf.text("b4comunicacao.com.br", leftColumnX, footerY + 20);

        // Finalize o PDF e salve
        pdf.save(`planejamento_${client.name}_${monthName(Number(planning.month))}_${planning.year}.pdf`);

        toast.success("PDF gerado com sucesso!");
    } catch (error) {
        console.error("Erro ao gerar PDF:", error);
        toast.error("Erro ao gerar PDF");
    }
};

export default generatePDF;
