import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { toast } from "sonner";
import { geistFontBase64, geistBoldFontBase64, vidaLokaRegularFontBase64 } from '../generate-planning-pdf/fonts';

interface Result {
    id: string;
    resultsReportId: string;
    createdAt: string;
    
    // Métricas básicas
    newFollowers?: number;
    totalPosts?: number;
    totalInteractions?: number;
    totalStories?: number;
    totalViews?: number;

    // Métricas de alcance e perfil
    reachNonFollowers?: number;
    profileActivity?: number;
    websiteClicks?: number;
    profileVisits?: number;
    percentageProfileActivity?: number;
    reachFollowers?: number;
    accountsReached?: number;

    // Aba Visualizações - novos campos
    percentageAds?: number;
    percentageFollowers?: number;
    percentageNonFollowers?: number;
    percentageAccountsReached?: number;
    percentageProfileVisits?: number;
    externalLinkTaps?: number;
    percentageExternalLinkTaps?: number;
    businessAddressTaps?: number;
    percentageBusinessAddressTaps?: number;

    // Aba Interações - novos campos
    interactionsPercentageAds?: number;
    interactionsPercentageFollowers?: number;
    interactionsPercentageNonFollowers?: number;
    
    // Reel
    reelInteractionPercentage?: number;
    reelLikes?: number;
    reelComments?: number;
    reelSaves?: number;
    reelShares?: number;
    
    // Publicações
    postInteractionPercentage?: number;
    postLikes?: number;
    postComments?: number;
    postSaves?: number;
    postShares?: number;
    
    // Story
    storyInteractionPercentage?: number;
    storyReplies?: number;
    
    // Vídeos ao vivo
    liveInteractionPercentage?: number;
    liveComments?: number;
    
    // Vídeos
    videoInteractionPercentage?: number;
    videoLikes?: number;
    videoComments?: number;
    videoSaves?: number;
    videoShares?: number;

    // Métricas de distribuição
    reachByPostType?: Record<string, number>;
    interactionsDistribution?: Record<string, number>;
    averageInteractionsPerPost?: number;
    averageStoryViews?: number;

    // Aba 4 - Seguidores (novos campos adicionados)
    totalFollowers?: number;
    unfollowCount?: number;
    followersLocation?: Record<string, number>;
    followersAgeRange?: Record<string, number>;
    followersGender?: Record<string, number>;
}

interface ResultsReport {
    id: string;
    clientId: string;
    month: number;
    year: number;
    createdAt: string;
    updatedAt: string;
    results: Result[];
}

const addCustomFonts = (doc: jsPDF) => {
    try {
        doc.addFileToVFS('Geist-Regular.ttf', geistFontBase64);
        doc.addFont('Geist-Regular.ttf', 'Geist', 'normal');
        doc.addFileToVFS('Geist-Bold.ttf', geistBoldFontBase64);
        doc.addFont('Geist-Bold.ttf', 'Geist', 'bold');
        doc.addFileToVFS('Vidaloka-Regular.ttf', vidaLokaRegularFontBase64);
        doc.addFont('Vidaloka-Regular.ttf', 'Vidaloka', 'normal');
    } catch (error) {
        console.error('Erro ao registrar fontes:', error);
    }
};

/**
 * Função principal para gerar PDF a partir de elementos do DOM
 */
const generateResultsPDF = async (
    client: {
        name: string;
        id: string;
    },
    report: ResultsReport,
    monthName: (month: number) => string,
    selectedCharts: {[key: string]: boolean} = {
        followers: true,
        content: true,
        interactions: true,
        detailedInteractions: true,
        visualizations: true
    },
    comparisonInfo?: {
        mode: 'previous' | 'custom';
        customCompareMonth?: string;
    }
): Promise<void> => {
    if (!client || !report) return;

    try {
        toast.info("Preparando o PDF, aguarde...");

        // Aumentar o tempo de espera para garantir que todos os gráficos estejam renderizados
        await new Promise(resolve => setTimeout(resolve, 3000)); // Aumentado de 2000 para 3000

        // Inicializa o PDF
        const pdf = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });

        // Configurações básicas - REDUZIR MARGENS
        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();
        const margin = 10; // Reduzido de 15 para 10 para dar mais espaço às imagens

        // Adicionar fontes personalizadas
        addCustomFonts(pdf);
        const fontFamily = 'Geist';
        pdf.setFont(fontFamily);

        // Função para capturar elementos DOM e transformar em imagem
        const captureElement = async (element: HTMLElement): Promise<HTMLCanvasElement> => {
            // Temporariamente aumentar o tamanho da fonte para captura
            const elements = element.querySelectorAll('*');

            // Aplicar estilos temporários para melhorar a captura
            elements.forEach(el => {
                if (el instanceof HTMLElement) {
                    // Aumentar tamanho do texto para seções específicas
                    if (el.classList.contains('text-sm')) {
                        el.style.fontSize = '1rem'; // Aumentar texto pequeno
                    }

                    // Garantir que cores e bordas sejam bem visíveis
                    if (el.classList.contains('border')) {
                        el.style.borderWidth = '1px';
                    }
                }
            });

            // Aguardar um pouco para garantir que todos os elementos estejam renderizados
            await new Promise(resolve => setTimeout(resolve, 500));

            // Capturar com configurações mais simples e confiáveis
            const canvas = await html2canvas(element, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                logging: false
            });

            // Restaurar estilos originais
            elements.forEach(el => {
                if (el instanceof HTMLElement) {
                    el.style.fontSize = '';
                    el.style.borderWidth = '';
                }
            });

            return canvas;
        };

        // Função para adicionar seção ao PDF
        const addSectionToPDF = async (title: string, element: HTMLElement) => {
            if (!element) {
                console.warn(`Elemento não encontrado para a seção: ${title}`);
                return;
            }

            try {
                const canvas = await captureElement(element);
                
                // Converter para JPEG com compressão para reduzir tamanho
                const imageData = canvas.toDataURL('image/jpeg', 0.8); // JPEG com 80% de qualidade

                // Usa 100% da largura disponível em vez de 97%
                const imgWidth = (pageWidth - 2 * margin);
                const aspectRatio = canvas.height / canvas.width;
                const imgHeight = imgWidth * aspectRatio;

                // Verifica se precisa adicionar uma nova página ANTES de adicionar a seção
                // Deixa mais espaço para seções grandes (especialmente métricas detalhadas)
                const isDetailedMetrics = title.includes("Métricas detalhadas de interação");
                const requiredSpace = isDetailedMetrics ? 150 : 100; // Mais espaço para métricas detalhadas
                
                if (yPosition > pageHeight - requiredSpace) {
                    pdf.addPage();
                    yPosition = 15;
                }

                // Centralizar a imagem com margens reduzidas
                const xPos = margin;

                if (imgHeight > pageHeight - 60) { 
                    // Se a imagem for muito grande, dividir em partes
                    const partsCount = Math.ceil(imgHeight / (pageHeight - 60));
                    const partHeight = canvas.height / partsCount;
                    
                    for (let i = 0; i < partsCount; i++) {
                        if (i > 0) {
                            pdf.addPage();
                            yPosition = 15;
                        }
                        
                        const partCanvas = document.createElement('canvas');
                        partCanvas.width = canvas.width;
                        partCanvas.height = partHeight;
                        
                        const ctx = partCanvas.getContext('2d');
                        if (ctx) {
                            ctx.drawImage(
                                canvas, 
                                0, i * partHeight, 
                                canvas.width, partHeight, 
                                0, 0, 
                                partCanvas.width, partCanvas.height
                            );
                            
                            const partImageData = partCanvas.toDataURL('image/jpeg', 0.8);
                            const partImgHeight = imgWidth * (partHeight / canvas.width);
                            
                            pdf.addImage(partImageData, 'JPEG', xPos, yPosition, imgWidth, partImgHeight);
                            yPosition += partImgHeight + 3;
                        }
                    }
                } else {
                    pdf.addImage(imageData, 'JPEG', xPos, yPosition, imgWidth, imgHeight);
                    yPosition += imgHeight + 3;
                }
            } catch (error) {
                console.error(`Erro ao capturar seção ${title}:`, error);
            }

            // Reduzir espaço entre seções
            yPosition += 5; 
        };

        // Reduzir o espaço do cabeçalho para dar mais espaço ao conteúdo
        const createHeader = () => {
            // Elementos visuais do cabeçalho - movidos mais para cima
            pdf.setFillColor(229, 71, 41);
            pdf.roundedRect(margin, 8, 38, 9, 4, 4, 'F'); // Movido de 15 para 8
            pdf.setTextColor(255, 255, 255);
            pdf.setFontSize(11);
            pdf.text("Etapa 10", margin + 3, 13.5); // Movido de 20.5 para 13.5

            pdf.setDrawColor(229, 71, 41);
            pdf.setLineWidth(0.3);
            pdf.roundedRect(margin + 42, 8, 48, 9, 4, 4, 'S'); // Movido de 15 para 8
            pdf.setTextColor(229, 71, 41);
            pdf.text("Resultados", margin + 45, 13.5); // Movido de 20.5 para 13.5

            // Título principal
            pdf.setTextColor(229, 71, 41);
            pdf.setFontSize(24);
            pdf.text("RELATÓRIO DE RESULTADOS", margin, 30); // Movido de 40 para 30

            // Mês e ano
            pdf.setFontSize(16);
            pdf.setTextColor(44, 62, 80);
            pdf.setFont(fontFamily, 'bold');
            pdf.text(`${monthName(report.month)} de ${report.year}`.toUpperCase(), margin, 40); // Movido de 50 para 40

            // Nome do cliente
            pdf.setFont(fontFamily, 'normal');
            pdf.setFontSize(18);
            pdf.setTextColor(229, 71, 41);
            pdf.text(`${client.name}`, margin, 50); // Movido de 60 para 50

            // Adicione informação sobre o modo de comparação se for personalizado
            if (comparisonInfo && comparisonInfo.mode === 'custom' && comparisonInfo.customCompareMonth) {
                const [compareMonth, compareYear] = comparisonInfo.customCompareMonth.split('-').map(Number);
                pdf.setFontSize(10);
                pdf.setTextColor(100, 100, 100);
                pdf.text(`Comparação personalizada com: ${monthName(compareMonth)} de ${compareYear}`, margin, 60); // Movido de 70 para 60
                return 65; // Reduzido de 75 para 65
            }

            // Reduzir ainda mais o espaçamento após o cabeçalho
            return 55; // Reduzido de 65 para 55
        };

        // Posição inicial após cabeçalho ajustada
        let yPosition = createHeader(); 

        // Identificar todas as seções de gráficos pelo título/cabeçalho
        const allSectionHeaders = document.querySelectorAll('h3.text-lg.font-semibold.mb-4.flex.items-center');
        const sections: { title: string, element: HTMLElement, key: string }[] = [];
        
        // Mapear os cabeçalhos de seção para seus elementos pai e categorias
        allSectionHeaders.forEach(header => {
            const title = header.textContent?.trim() || "";
            const parentSection = header.closest('.mb-6, .mb-8, .mb-11, .mb-12, .mb-24');
            
            if (parentSection instanceof HTMLElement) {
                let key = "";
                
                if (title.includes("Seguidores")) key = "followers";
                else if (title.includes("Conteúdo")) key = "content";
                else if (title.includes("Interações e alcance")) key = "interactions";
                else if (title.includes("Métricas detalhadas de interação")) key = "detailedInteractions";
                else if (title.includes("Visualizações")) key = "visualizations";
                
                sections.push({ title, element: parentSection, key });
            }
        });
        
        // Processar cada seção selecionada
        for (const section of sections) {
            // Verifica se a seção está selecionada
            if (selectedCharts[section.key] || 
                (section.key === "detailedInteractions" && selectedCharts["interactions"])) {
                await addSectionToPDF(section.title, section.element);
            }
        }

        // Se nenhuma seção foi encontrada, aviso ao usuário
        if (sections.length === 0) {
            toast.warning("Não foi possível encontrar as seções de gráficos. O PDF pode ficar incompleto.");
        }

        // Adicionar espaço antes do rodapé
        // Verifica se a posição atual está muito próxima do rodapé e adiciona uma nova página se necessário
        if (yPosition > pageHeight - 80) {
            pdf.addPage();
            yPosition = 15;
        } else {
            // Adiciona um espaço adequado entre a última seção e a mensagem
            yPosition += 10; 
        }

        // Adiciona a mensagem personalizada
        pdf.setTextColor(229, 71, 41); // Cor laranja da marca
        pdf.setFontSize(12);
        pdf.setFont(fontFamily, 'bold');
        pdf.text("Cliente B4: nosso foco é seu resultado, nos vemos lá! :)", 
            pageWidth / 2, yPosition, { align: 'center' });

        // Adiciona espaço antes do "Obrigado!"
        yPosition += 25;

        // Verifica se há espaço para o "Obrigado!"
        if (yPosition > pageHeight - 70) {
            pdf.addPage();
            yPosition = 30;
        }

        pdf.setFontSize(54);
        pdf.setTextColor(229, 71, 41); // Mantém a cor laranja
        pdf.setFont('Vidaloka', 'normal'); // Usa a fonte Vidaloka para "Obrigado!"
        pdf.text("Obrigado!", pageWidth / 2, yPosition, { align: 'center' });
        pdf.setFont(fontFamily, 'normal'); // Retorna à fonte normal

        // Avança a posição Y para o rodapé
        if (yPosition > pageHeight - 60) {
            pdf.addPage();
            yPosition = 15;
        } else {
            // Define uma posição fixa para o início do rodapé com espaço suficiente após o "Obrigado!"
            yPosition = pageHeight - 50; 
        }

        // Adicionar rodapé apenas na última página
        const totalPages = pdf.internal.pages.length;
        pdf.setPage(totalPages);

        // Barra laranja do rodapé
        const barHeight = 9;
        pdf.setFillColor(229, 71, 41);
        pdf.rect(0, pageHeight - barHeight, pageWidth, barHeight, 'F');

        // Adicionar informações de contato
        const footerY = pageHeight - 40;

        pdf.setFontSize(9);
        pdf.setTextColor(44, 62, 80);
        const leftColumnX = 15;
        pdf.text("Nosso time está à disposição para fazer o seu negócio alavancar.", leftColumnX, footerY);
        pdf.text("Criatividade que conecta. Estratégias que convertem.", leftColumnX, footerY + 5);

        pdf.setTextColor(0, 0, 238);
        pdf.text("<EMAIL> | <EMAIL>", leftColumnX, footerY + 10);
        pdf.text("(54) 99954-4025 | (54) 99947-5327", leftColumnX, footerY + 15);
        pdf.text("b4comunicacao.com.br", leftColumnX, footerY + 20);

        // Logo (se disponível)
        try {
            const rightColumnX = 175;
            const imageWidth = 12;
            const imageHeight = 14;
            const logoPath = '/logo-rodapé-17-b4(1).png';
            pdf.addImage(logoPath, 'PNG', rightColumnX, footerY - 0, imageWidth, imageHeight);
        } catch (error) {
            console.error('Erro ao adicionar logo:', error);
        }

        pdf.save(`resultados_${client.name}_${monthName(report.month)}_${report.year}.pdf`);
        toast.success("PDF gerado com sucesso!");
        return;
    } catch (error) {
        console.error("Erro ao gerar PDF:", error);
        toast.error(`Erro ao gerar PDF: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
        throw error;
    }
};

export default generateResultsPDF;