"use client";

import { useState } from 'react';
import { But<PERSON> } from "@/app/components/ui/button";
import { toast } from "sonner";
import { Check, Ellipsis } from 'lucide-react';

interface MarkAsCompletedProps {
  contentId: string;
  currentStatus?: string;
  onStatusUpdate?: (newStatus: string) => void;
  disabled?: boolean;
  type?: 'content' | 'general';
}

export const MarkAsCompleted = ({
  contentId,
  currentStatus = 'pendente',
  onStatusUpdate,
  disabled = false,
  type = 'content'
}: MarkAsCompletedProps) => {
  const [isUpdating, setIsUpdating] = useState(false);

  const completedStatuses = ['em revisão', 'concluído', 'captado', 'pend. captação', 'anúncio concluído'];

  const isCompleted = completedStatuses.includes(currentStatus);

  const updateStatus = async () => {
    if (disabled || isUpdating) return;

    const newStatus = isCompleted ? 'pendente' : 'em revisão';

    setIsUpdating(true);
    try {
      const apiUrl = type === 'general'
        ? `/api/general-demands/${contentId}/status`
        : `/api/contents/${contentId}`;

      const response = await fetch(apiUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao atualizar status');
      }

      const successMessage = newStatus === 'pendente'
        ? 'Demanda marcada como pendente'
        : 'Demanda marcada como concluída';

      toast.success(successMessage);

      if (onStatusUpdate) {
        onStatusUpdate(newStatus);
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao atualizar status');
    } finally {
      setIsUpdating(false);
    }
  };

  const buttonClass = isCompleted
    ? "bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-950 dark:text-green-300 dark:hover:bg-green-900"
    : "bg-gray-50 text-gray-800 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700";

  const buttonTitle = isCompleted
    ? "Marcar como pendente"
    : "Marcar como concluído";

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={updateStatus}
      disabled={disabled || isUpdating}
      className={buttonClass}
      title={buttonTitle}
    >
      {isUpdating ? (
        <Ellipsis className="h-4 w-4" />
      ) : (
        <Check className="h-4 w-4" />
      )}
    </Button>
  );
};
