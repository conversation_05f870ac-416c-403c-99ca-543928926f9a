"use client";

import React, { useEffect, useState } from 'react';

function formatHours(h: number) {
    const sign = h >= 0 ? '+' : '-';
    const abs = Math.abs(h);
    const hours = Math.floor(abs);
    const minutes = Math.round((abs - hours) * 60);
    return `${sign}${String(hours).padStart(2, '0')}h${String(minutes).padStart(2, '0')}`;
}

export default function MyTimeBank() {
    const [error, setError] = useState<string | null>(null);
    const [balance, setBalance] = useState<number | null>(null);

    useEffect(() => {
        let mounted = true;
        fetch('/api/time-bank/me')
            .then(res => {
                if (!res.ok) throw new Error('Falha ao buscar');
                return res.json();
            })
            .then(data => {
                if (!mounted) return;
                if (data?.totalTimeBank === undefined) {
                    setError('Resposta inválida');
                } else {
                    setBalance(data.totalTimeBank);
                }
            })
            .catch(err => {
                if (!mounted) return;
                setError(err.message ?? 'Erro');
            });
        return () => { mounted = false; };
    }, []);

    if (error) return <div>Erro: {error}</div>;
    if (balance === null) return (
        <div className="flex items-center justify-center gap-2">
            <p className="text-xs text-muted-foreground">Banco de horas</p>
            <p className="text-lg font-bold text-muted-foreground text-blue-600">
                00h00
            </p>
        </div>
    )

    return (
        <div className="flex items-center justify-center gap-2">
            <p className="text-xs text-muted-foreground">Banco de horas</p>
            <p className={`text-lg font-bold ${balance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatHours(balance)}
            </p>
        </div>
    );
}
