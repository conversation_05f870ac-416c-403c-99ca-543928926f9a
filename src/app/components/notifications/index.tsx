"use client"

import { <PERSON>, AlertCircle, MessageSquarePlus, ClipboardList, Link as LinkIcon, Ellipsis, CheckCircle } from "lucide-react";
import { Button } from "../ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "../ui/dropdown-menu";
import { useState, useEffect } from "react";
import Link from "next/link";
import { Badge } from "../ui/badge";
import { format } from "date-fns";
import { ActivityInfo } from "../activity-info";
import { Dialog, DialogContent, DialogTrigger, DialogTitle } from "../ui/dialog";
import { DialogDescription } from "@radix-ui/react-dialog";

interface Notification {
    id: string;
    content: string;
    type: string;
    entityId: string | null;
    entityType: string | null;
    reference?: string | null;
    isRead: boolean;
    createdAt: string;
    importance?: string;
}

interface NotificationWithActivityInfoProps {
    notification: Notification;
    getNotificationIcon: (type: string) => React.ReactNode;
    getNotificationTitle: (type: string) => string;
    markAsRead: (id: string) => Promise<void>;
}

const NotificationWithActivityInfo = ({ notification, getNotificationIcon, getNotificationTitle, markAsRead }: NotificationWithActivityInfoProps) => {
    const [demandData, setDemandData] = useState<{ url?: string, id?: string }>({});
    const [isLoadingDemand, setIsLoadingDemand] = useState(false);

    const fetchDemandData = async () => {
        if (!notification.entityId || isLoadingDemand) return;

        setIsLoadingDemand(true);
        try {
            const apiUrl = notification.entityType === "content"
                ? `/api/contents/${notification.entityId}`
                : `/api/general-demands/${notification.entityId}`;

            const response = await fetch(apiUrl);
            if (response.ok) {
                const data = await response.json();
                setDemandData({
                    url: data.urlStructuringFeed || '',
                    id: data.id,
                });
            }
        } catch (error) {
            console.error("Error fetching demand data:", error);
        } finally {
            setIsLoadingDemand(false);
        }
    };

    return (
        <Dialog onOpenChange={(open) => {
            if (open) {
                fetchDemandData();
                markAsRead(notification.id);
            }
        }}>
            <DialogTrigger asChild>
                <div
                    className={`block p-2 border-b border-zinc-200 dark:border-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-800 transition-colors cursor-pointer ${!notification.isRead ? 'bg-sky-50 dark:bg-sky-900/20' : 'opacity-80'}`}
                >
                    <div className="flex justify-between items-start mb-1">
                        <h6 className="text-xs font-semibold text-zinc-700 dark:text-zinc-200 flex items-center gap-1">
                            {getNotificationIcon(notification.type)}
                            {getNotificationTitle(notification.type)}
                        </h6>
                        <p className="text-[10px] text-zinc-500 dark:text-zinc-400">
                            {format(new Date(notification.createdAt), "dd/MM/yyyy HH:mm")}
                        </p>
                    </div>

                    <div className="flex justify-between items-start">
                        <p className="text-xs text-zinc-700 dark:text-zinc-300 line-clamp-2 flex-1 pr-2">
                            {notification.content}
                        </p>

                        {!notification.isRead && (
                            <Badge
                                variant="outline"
                                className={`flex-shrink-0 text-[.7rem] uppercase rounded-full ${notification.importance === 'high'
                                    ? 'border-red-300 text-red-800 bg-red-50 dark:bg-red-900/20 dark:border-red-900 dark:text-red-200'
                                    : 'border-sky-300 text-sky-800 bg-sky-50 dark:bg-sky-900/20 dark:border-sky-900 dark:text-sky-200'
                                    }`}
                            >
                                Nova
                            </Badge>
                        )}
                    </div>
                </div>
            </DialogTrigger>
            <DialogContent className="max-w-md">
                <DialogTitle>Detalhes da demanda</DialogTitle>
                <DialogDescription>
                    Clique no botão abaixo visualizar mais detalhes da demanda <span className="italic">{demandData.id?.substring(0, 8).toUpperCase()}</span>, incluindo a URL do Google Drive.
                </DialogDescription>
                {isLoadingDemand ? (
                    <div className="flex justify-center items-center animate-pulse">
                        <Ellipsis className="" />
                    </div>
                ) : (
                    <ActivityInfo activity={{ url: Array.isArray(demandData.url) ? demandData.url : demandData.url ? [demandData.url] : undefined }} />
                )}
            </DialogContent>
        </Dialog>
    );
};

export const Notifications = () => {
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [hasUnread, setHasUnread] = useState(false);

    const fetchNotifications = async () => {
        try {
            const response = await fetch("/api/notifications");
            if (response.ok) {
                const data: Notification[] = await response.json();
                const isVirtual = (n: Notification) => n.type === 'upcoming_birthday' || n.type === 'upcoming_hire';
                const byDateDesc = (a: Notification, b: Notification) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();

                const virtuals = data.filter(isVirtual).sort(byDateDesc);
                const others = data.filter(n => !isVirtual(n)).sort(byDateDesc);
                const latestNotifications = [...virtuals, ...others].slice(0, 10);

                setNotifications(latestNotifications);
                setHasUnread(latestNotifications.some((notification: Notification) => !notification.isRead));
            } else {
                console.error("Failed to fetch notifications. Status:", response.status);
            }
        } catch (error) {
            console.error("Error fetching notifications:", error);
        }
    };

    useEffect(() => {
        fetchNotifications();
        const interval = setInterval(fetchNotifications, 60000);
        return () => clearInterval(interval);
    }, []);

    const markAsRead = async (id: string) => {
        try {
            if (id.startsWith('virtual-')) {
                setNotifications(notifications.map(notification =>
                    notification.id === id ? { ...notification, isRead: true } : notification
                ));
                setHasUnread(notifications.some(notification =>
                    notification.id !== id && !notification.isRead
                ));
                return;
            }

            await fetch("/api/notifications", {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ id, isRead: true }),
            });

            setNotifications(notifications.map(notification =>
                notification.id === id ? { ...notification, isRead: true } : notification
            ));

            setHasUnread(notifications.some(notification =>
                notification.id !== id && !notification.isRead
            ));
        } catch (error) {
            console.error("Error marking notification as read:", error);
        }
    };

    const getNotificationIcon = (type: string) => {
        switch (type) {
            case "new_feedback":
                return <MessageSquarePlus className="h-3 w-3 text-blue-500" />;
            case "assigned_demand":
                return <ClipboardList className="h-3 w-3 text-purple-500" />;
            case "url_added":
                return <LinkIcon className="h-3 w-3 text-green-500" />;
            case "planning_approved":
                return <CheckCircle className="h-3 w-3 text-green-500" />;
            case "upcoming_birthday":
                return <Bell className="h-3 w-3 text-pink-500" />;
            case "upcoming_hire":
                return <Bell className="h-3 w-3 text-amber-500" />;
            default:
                return <AlertCircle className="h-3 w-3 text-muted-foreground" />;
        }
    };

    const getNotificationTitle = (type: string) => {
        switch (type) {
            case "new_feedback":
                return "Novo feedback";
            case "assigned_demand":
                return "Demanda atribuída";
            case "url_added":
                return "URL adicionada";
            case "planning_approved":
                return "Planejamento aprovado";
            case "upcoming_birthday":
                return "Aniversário próximo";
            case "upcoming_hire":
                return "Aniversário de contratação próximo";
            default:
                return "Notificação";
        }
    };

    const getNotificationLink = (notification: Notification) => {
        if (notification.type === "url_added") {
            return `/admin/demands`;
        }

        if (notification.reference) {
            return notification.reference;
        }

        if (notification.entityType === "feedback") {
            return `/admin/feedbacks`;
        } else if (notification.entityType === "content" || notification.entityType === "general_demand") {
            return `/my-demands`;
        }
        return '#';
    };

    return (
        <DropdownMenu onOpenChange={(open) => {
            if (open) fetchNotifications();
        }}>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="outline"
                    className="relative"
                    size="icon"
                    title="Notificações"
                >
                    <Bell className="w-5 h-5" />
                    {hasUnread && (
                        <span className="absolute top-1 right-2 w-2 h-2 bg-red-500 rounded-full"></span>
                    )}
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-72 mx-4">
                <h5 className="text-sm font-semibold text-zinc-700 dark:text-zinc-200 px-2 py-1 border-b border-zinc-200 dark:border-zinc-700">
                    Notificações
                </h5>
                <div className="overflow-y-auto max-h-96">
                    {notifications.length === 0 ? (
                        <div className="p-2 text-xs text-center text-zinc-500 dark:text-zinc-400">
                            Não há notificações
                        </div>
                    ) : (
                        <>
                            {notifications.slice(0, 10).map((notification) => (
                                notification.type === "url_added" ? (
                                    <NotificationWithActivityInfo
                                        key={notification.id}
                                        notification={notification}
                                        getNotificationIcon={getNotificationIcon}
                                        getNotificationTitle={getNotificationTitle}
                                        markAsRead={markAsRead}
                                    />
                                ) : (
                                    <Link
                                        href={getNotificationLink(notification)}
                                        key={notification.id}
                                        className={`block p-2 border-b border-zinc-200 dark:border-zinc-700 hover:bg-zinc-50 dark:hover:bg-zinc-800 transition-colors ${!notification.isRead ? 'bg-sky-50 dark:bg-sky-900/20' : 'opacity-80'}`}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            markAsRead(notification.id);
                                        }}
                                    >
                                        <div className="flex justify-between items-start mb-1">
                                            <h6 className="text-xs font-semibold text-zinc-700 dark:text-zinc-200 flex items-center gap-1">
                                                {getNotificationIcon(notification.type)}
                                                {getNotificationTitle(notification.type)}
                                            </h6>
                                            <p className="text-[10px] text-zinc-500 dark:text-zinc-400">
                                                {format(new Date(notification.createdAt), "dd/MM/yyyy HH:mm")}
                                            </p>
                                        </div>

                                        <div className="flex justify-between items-start">
                                            <p className="text-xs text-zinc-700 dark:text-zinc-300 line-clamp-2 flex-1 pr-2">
                                                {notification.content}
                                            </p>

                                            {!notification.isRead && (
                                                <Badge
                                                    variant="outline"
                                                    className={`flex-shrink-0 text-[.7rem] uppercase rounded-full ${notification.importance === 'high'
                                                        ? 'border-red-300 text-red-800 bg-red-50 dark:bg-red-900/20 dark:border-red-900 dark:text-red-200'
                                                        : 'border-sky-300 text-sky-800 bg-sky-50 dark:bg-sky-900/20 dark:border-sky-900 dark:text-sky-200'
                                                        }`}
                                                >
                                                    Nova
                                                </Badge>
                                            )}
                                        </div>
                                    </Link>
                                )
                            ))}
                            {notifications.length >= 10 && (
                                <Link
                                    href="/notifications"
                                    className="block p-2 text-center text-xs font-medium text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-zinc-50 dark:hover:bg-zinc-800 transition-colors"
                                >
                                    Ver todas as notificações
                                </Link>
                            )}
                        </>
                    )}
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    );
};