"use client"

import { useState } from "react";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Trash2, Plus, Star, StarOff, ChevronUp, ChevronDown } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { formatCpfOnInput } from "@/lib/formatters";

interface Address {
    id?: string;
    zipCode?: string;
    street?: string;
    number?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
    country?: string;
    createdAt?: string;
    updatedAt?: string;
}

interface Owner {
    id?: string;
    name: string;
    cpf?: string;
    birthDate?: Date | string;
    rg?: string;
    issuingAgency?: string;
    maritalStatus?: string;
    nationality?: string;
    profession?: string;
    isPrimary?: boolean;
    address?: Address;
    clientId?: string;
    addressId?: string;
    createdAt?: string;
    updatedAt?: string;
}

interface OwnersManagerProps {
    owners: Owner[];
    onChange: (owners: Owner[]) => void;
}

const MARITAL_STATUS_OPTIONS = [
    { value: "Solteiro(a)", label: "Solteiro(a)" },
    { value: "Casado(a)", label: "Casado(a)" },
    { value: "Divorciado(a)", label: "Divorciado(a)" },
    { value: "Viúvo(a)", label: "Viúvo(a)" },
    { value: "Separado(a)", label: "Separado(a)" },
];

const NATIONALITY_OPTIONS = [
    { value: "Brasileiro(a)", label: "Brasileiro(a)" },
    { value: "Outro", label: "Outro" },
];

export const OwnersManager = ({ owners, onChange }: OwnersManagerProps) => {
    const [expandedOwner, setExpandedOwner] = useState<number | null>(null);

    const handleAddOwner = () => {
        const newOwner: Owner = {
            name: '',
            isPrimary: owners.length === 0,
            address: {}
        };
        onChange([...owners, newOwner]);
    };

    const handleRemoveOwner = (index: number) => {
        const newOwners = [...owners];
        newOwners.splice(index, 1);
        
        if (owners[index]?.isPrimary && newOwners.length > 0) {
            newOwners[0] = { ...newOwners[0], isPrimary: true };
        }
        
        onChange(newOwners);
    };

    const handleOwnerChange = (index: number, field: keyof Owner, value: string) => {
        const newOwners = [...owners];
        
        if (field === 'birthDate' && value) {
            newOwners[index] = { ...newOwners[index], [field]: value };
        } else if (field === 'cpf') {
            newOwners[index] = { ...newOwners[index], [field]: formatCpfOnInput(value) };
        } else {
            newOwners[index] = { ...newOwners[index], [field]: value };
        }
        
        onChange(newOwners);
    };

    const handleAddressChange = (ownerIndex: number, field: keyof Address, value: string) => {
        const newOwners = [...owners];
        const currentOwner = newOwners[ownerIndex];
        const currentAddress = currentOwner.address || {};
        
        newOwners[ownerIndex] = {
            ...currentOwner,
            address: {
                ...currentAddress,
                [field]: value
            }
        };
        
        onChange(newOwners);
    };

    const handleToggleExpand = (index: number) => {
        setExpandedOwner(expandedOwner === index ? null : index);
    };

    const handleTogglePrimary = (index: number) => {
        const newOwners = owners.map((owner, i) => ({
            ...owner,
            isPrimary: i === index
        }));
        onChange(newOwners);
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-medium">Proprietários</h3>
                <Button
                    type="button"
                    onClick={handleAddOwner}
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                >
                    <Plus className="h-4 w-4" />
                    Adicionar proprietário
                </Button>
            </div>
            
            {owners.length === 0 ? (
                <div className="text-sm text-center py-8 text-muted-foreground border border-dashed rounded-md">
                    Nenhum proprietário cadastrado
                </div>
            ) : (
                <div className="space-y-4">
                    {owners.map((owner, index) => (
                        <div key={index} className="border rounded-lg overflow-hidden">
                            <div className="bg-muted p-3 flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        type="button"
                                        onClick={() => handleTogglePrimary(index)}
                                        title={owner.isPrimary ? "Proprietário principal" : "Marcar como principal"}
                                        className="hover:bg-zinc-300 dark:hover:bg-zinc-900"
                                    >
                                        {owner.isPrimary ? (
                                            <Star className="h-4 w-4 text-yellow-500" />
                                        ) : (
                                            <StarOff className="h-4 w-4" />
                                        )}
                                    </Button>
                                    <span className="font-medium">
                                        {owner.name || `Proprietário ${index + 1}`}
                                        {owner.isPrimary && <span className="text-xs ml-2 text-muted-foreground">(Principal)</span>}
                                    </span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        type="button"
                                        onClick={() => handleToggleExpand(index)}
                                        title={expandedOwner === index ? "Recolher" : "Expandir"}
                                        className="hover:bg-zinc-300 dark:hover:bg-zinc-900"
                                    >
                                        {expandedOwner === index ? <ChevronUp /> : <ChevronDown />}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        type="button"
                                        onClick={() => handleRemoveOwner(index)}
                                        title="Remover proprietário"
                                        className="hover:bg-zinc-300 dark:hover:bg-zinc-900"
                                    >
                                        <Trash2 className="h-4 w-4 text-red-500" />
                                    </Button>
                                </div>
                            </div>
                            
                            <div className="p-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium mb-1">Nome completo</label>
                                        <Input
                                            value={owner.name || ''}
                                            onChange={(e) => handleOwnerChange(index, 'name', e.target.value)}
                                            placeholder="Nome completo"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium mb-1">CPF</label>
                                        <Input
                                            value={owner.cpf || ''}
                                            onChange={(e) => handleOwnerChange(index, 'cpf', e.target.value)}
                                            placeholder="000.000.000-00"
                                        />
                                    </div>
                                </div>
                                
                                {expandedOwner === index && (
                                    <>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                            <div>
                                                <label className="block text-sm font-medium mb-1">Data de nascimento</label>
                                                <Input
                                                    type="date"
                                                    value={
                                                        (() => {
                                                            if (!owner.birthDate) return '';
                                                            if (owner.birthDate instanceof Date) {
                                                                return owner.birthDate.toISOString().split('T')[0];
                                                            } 
                                                            // Se for string ISO completa, extrair apenas a data
                                                            if (typeof owner.birthDate === 'string' && owner.birthDate.includes('T')) {
                                                                return owner.birthDate.split('T')[0];
                                                            }
                                                            // Se já for apenas YYYY-MM-DD
                                                            return owner.birthDate as string;
                                                        })()
                                                    }
                                                    onChange={(e) => handleOwnerChange(index, 'birthDate', e.target.value)}
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium mb-1">RG</label>
                                                <Input
                                                    value={owner.rg || ''}
                                                    onChange={(e) => handleOwnerChange(index, 'rg', e.target.value)}
                                                    placeholder="0000000"
                                                />
                                            </div>
                                        </div>
                                        
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                            <div>
                                                <label className="block text-sm font-medium mb-1">Órgão emissor</label>
                                                <Input
                                                    value={owner.issuingAgency || ''}
                                                    onChange={(e) => handleOwnerChange(index, 'issuingAgency', e.target.value)}
                                                    placeholder="SSP/UF"
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium mb-1">Profissão</label>
                                                <Input
                                                    value={owner.profession || ''}
                                                    onChange={(e) => handleOwnerChange(index, 'profession', e.target.value)}
                                                    placeholder="Profissão"
                                                />
                                            </div>
                                        </div>
                                        
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                            <div>
                                                <label className="block text-sm font-medium mb-1">Estado civil</label>
                                                <Select
                                                    value={owner.maritalStatus || ''}
                                                    onValueChange={(value) => handleOwnerChange(index, 'maritalStatus', value)}
                                                >
                                                    <SelectTrigger id={`maritalStatus-${index}`}>
                                                        <SelectValue placeholder="Selecione o estado civil" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {MARITAL_STATUS_OPTIONS.map((option) => (
                                                            <SelectItem key={option.value} value={option.value}>
                                                                {option.label}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium mb-1">Nacionalidade</label>
                                                <Select
                                                    value={owner.nationality || 'Brasileiro(a)'}
                                                    onValueChange={(value) => handleOwnerChange(index, 'nationality', value)}
                                                >
                                                    <SelectTrigger id={`nationality-${index}`}>
                                                        <SelectValue placeholder="Selecione a nacionalidade" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {NATIONALITY_OPTIONS.map((option) => (
                                                            <SelectItem key={option.value} value={option.value}>
                                                                {option.label}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>
                                        
                                        <div className="mt-6">
                                            <h4 className="text-md font-medium mb-3">Endereço</h4>
                                            <div className="grid grid-cols-2 gap-2 mb-2">
                                                <div>
                                                    <label className="block text-xs font-medium mb-1">CEP</label>
                                                    <Input
                                                        value={owner.address?.zipCode || ''}
                                                        onChange={(e) => handleAddressChange(index, 'zipCode', e.target.value)}
                                                        placeholder="00000-000"
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-xs font-medium mb-1">Rua</label>
                                                    <Input
                                                        value={owner.address?.street || ''}
                                                        onChange={(e) => handleAddressChange(index, 'street', e.target.value)}
                                                        placeholder="Rua"
                                                    />
                                                </div>
                                            </div>
                                            <div className="grid grid-cols-2 gap-2 mb-2">
                                                <div>
                                                    <label className="block text-xs font-medium mb-1">Número</label>
                                                    <Input
                                                        value={owner.address?.number || ''}
                                                        onChange={(e) => handleAddressChange(index, 'number', e.target.value)}
                                                        placeholder="Número"
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-xs font-medium mb-1">Bairro</label>
                                                    <Input
                                                        value={owner.address?.neighborhood || ''}
                                                        onChange={(e) => handleAddressChange(index, 'neighborhood', e.target.value)}
                                                        placeholder="Bairro"
                                                    />
                                                </div>
                                            </div>
                                            <div className="grid grid-cols-2 gap-2">
                                                <div>
                                                    <label className="block text-xs font-medium mb-1">Cidade</label>
                                                    <Input
                                                        value={owner.address?.city || ''}
                                                        onChange={(e) => handleAddressChange(index, 'city', e.target.value)}
                                                        placeholder="Cidade"
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-xs font-medium mb-1">Estado</label>
                                                    <Input
                                                        value={owner.address?.state || ''}
                                                        onChange={(e) => handleAddressChange(index, 'state', e.target.value)}
                                                        placeholder="Estado"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};
