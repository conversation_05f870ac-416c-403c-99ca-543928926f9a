"use client"

import { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '../ui/dialog';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Ellipsis } from 'lucide-react';

interface AuditLog {
    id: string;
    action: string;
    field?: string;
    oldValue?: string;
    newValue?: string;
    editedBy: string;
    editedByName: string;
    editedAt: string;
}

interface PointRecordAuditProps {
    recordId: string;
}

export const PointRecordAudit = ({ recordId }: PointRecordAuditProps) => {
    const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        fetch(`/api/admin/point-record/${recordId}/audit`)
            .then(res => res.json())
            .then(data => setAuditLogs(data))
            .catch(error => console.error('Erro ao buscar auditoria:', error))
            .finally(() => setIsLoading(false));
    }, [recordId]);

    const formatValue = (field: string, value: string) => {
        if (!value) return '-';
        if (field === 'clockIn' || field === 'clockOut') {
            return format(new Date(value), 'HH:mm', { locale: ptBR });
        }
        if (field === 'date') {
            return format(new Date(value), 'dd/MM/yyyy', { locale: ptBR });
        }
        return value;
    };

    const getFieldLabel = (field: string) => {
        const labels: Record<string, string> = {
            clockIn: 'Entrada',
            clockOut: 'Saída',
            date: 'Data',
            createdBy: 'Autor'
        };
        return labels[field] || field;
    };

    const getActionLabel = (action: string) => {
        const labels: Record<string, string> = {
            CREATE: 'Criado',
            UPDATE: 'Editado',
            DELETE: 'Excluído'
        };
        return labels[action] || action;
    };

    return (
        <DialogContent className="max-w-2xl">
            <DialogHeader>
                <DialogTitle>
                    Histórico de alterações
                </DialogTitle>
            </DialogHeader>
            <div className="max-h-96 overflow-y-auto">
                {isLoading ? (
                    <div className="text-center py-4"><Ellipsis className='animate-pulse' /></div>
                ) : auditLogs.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                        Nenhuma alteração registrada
                    </div>
                ) : (
                    <div className="space-y-3">
                        {auditLogs.map((log) => (
                            <div key={log.id} className="border rounded p-3 bg-muted/50">
                                <div className="flex justify-between items-start mb-2">
                                    <span className="font-medium text-sm">
                                        {getActionLabel(log.action)}
                                        {log.field && ` - ${getFieldLabel(log.field)}`}
                                    </span>
                                    <span className="text-xs text-muted-foreground">
                                        {format(new Date(log.editedAt), 'dd/MM/yyyy HH:mm', { locale: ptBR })}
                                    </span>
                                </div>
                                {log.field && log.action === 'UPDATE' && (
                                    <div className="text-sm space-y-1">
                                        <div>
                                            <span className="text-red-600">De: </span>
                                            {formatValue(log.field, log.oldValue || '')}
                                        </div>
                                        <div>
                                            <span className="text-green-600">Para: </span>
                                            {formatValue(log.field, log.newValue || '')}
                                        </div>
                                    </div>
                                )}
                                <div className="text-xs text-muted-foreground mt-2">
                                    Por: {log.editedByName}
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </DialogContent>
    );
};