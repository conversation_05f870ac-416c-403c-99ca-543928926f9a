"use client";
import { useEffect, useState } from "react";
import { Button } from "../ui/button";
import { Download } from "lucide-react";

interface BeforeInstallPromptEvent extends Event {
    prompt(): Promise<void>;
    userChoice: Promise<{ outcome: "accepted" | "dismissed"; platform: string }>;
}

interface SafariNavigator extends Navigator {
    standalone?: boolean;
}

export default function InstallPromptButton() {
    const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
    const [installed, setInstalled] = useState(false);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [debugInfo, setDebugInfo] = useState<string>("");

    useEffect(() => {
        if (
            window.matchMedia("(display-mode: standalone)").matches ||
            (window.navigator as SafariNavigator).standalone
        ) {
            setInstalled(true);
            setDebugInfo("App já está instalado como PWA");
            return;
        }

        const onBeforeInstall = (e: Event) => {
            e.preventDefault();
            setDeferredPrompt(e as BeforeInstallPromptEvent);
            setDebugInfo("Pronto para instalar! Clique no botão de download.");
        };

        const onAppInstalled = () => {
            setInstalled(true);
            setDebugInfo("App foi instalado com sucesso!");
        };

        window.addEventListener("beforeinstallprompt", onBeforeInstall);
        window.addEventListener("appinstalled", onAppInstalled);

        setTimeout(() => {
            if (!deferredPrompt) {
                setDebugInfo("O navegador não disparou o evento de instalação. Verifique se o PWA atende a todos os requisitos.");
            }
        }, 3000);

        return () => {
            window.removeEventListener("beforeinstallprompt", onBeforeInstall);
            window.removeEventListener("appinstalled", onAppInstalled);
        };
    }, [deferredPrompt]);

    const install = async () => {
        if (!deferredPrompt) {
            setDebugInfo("Não foi possível iniciar a instalação. O navegador pode não suportar esta funcionalidade ou o PWA já está instalado.");

            if (/iPhone|iPad|iPod/.test(navigator.userAgent)) {
                alert("Para instalar o app: toque no ícone de compartilhamento e selecione 'Adicionar à Tela de Início'");
            }
            return;
        }

        try {
            await deferredPrompt.prompt();

            const choiceResult = await deferredPrompt.userChoice;

            if (choiceResult.outcome === "accepted") {
                setInstalled(true);
                setDebugInfo("App instalado com sucesso!");
            } else {
                setDebugInfo("Instalação recusada pelo usuário");
            }

            setDeferredPrompt(null);
        } catch (error) {
            console.error("Erro ao tentar instalar:", error);
            setDebugInfo(`Erro ao tentar instalar: ${error}`);
        }
    };

    if (installed || !deferredPrompt) return null;

    return (
        <div className="fixed bottom-[4.5rem] md:bottom-24 right-4 z-50">
            <Button
                className="h-12 w-12 rounded-full shadow-lg bg-primary hover:bg-primary/90"
                onClick={install}
                title="Instalar o B4Desk">
                <Download />
            </Button>

            {/*
                * TODO:
                * Refatorar
                * Adicionar botão de instalação para iOS
                * Adicionar debugInfo para ajudar o usuário a entender o estado da instalação
                */
            }
            {/* {debugInfo && (
                <div className="absolute bottom-14 right-0 bg-white/90 backdrop-blur-sm p-3 rounded-xl shadow-xl text-xs text-zinc-800 w-64 z-50 border border-zinc-300">
                    <p className="text-sm font-medium">{debugInfo}</p>
                    {!deferredPrompt && (
                        <Button 
                            className="mt-2 text-xs h-7 w-full bg-zinc-100  hover:bg-zinc-200" 
                            variant="ghost" 
                            onClick={() => setShowDebug(!showDebug)}>
                            {showDebug ? "Ocultar detalhes" : "Mostrar detalhes"}
                        </Button>
                    )}
                </div>
            )} */}

        </div>
    );
}
