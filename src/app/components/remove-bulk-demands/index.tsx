"use client"

import { useState } from 'react';
import { Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { But<PERSON> } from '@/app/components/ui/button';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    Di<PERSON>Trigger,
    DialogFooter,
} from '@/app/components/ui/dialog';
import { Badge } from '@/app/components/ui/badge';
import { Ellipsis } from 'lucide-react';

interface Content {
    id: string;
    type: 'general' | 'content';
}

interface RemoveBulkDemandsProps {
    demands: Content[];
    onSuccess?: () => void;
    onLocalUpdate?: (removedIds: string[]) => void;
}

export function RemoveBulkDemands({
    demands,
    onSuccess,
    onLocalUpdate,
}: RemoveBulkDemandsProps) {
    const [isLoading, setIsLoading] = useState(false);
    const [isOpen, setIsOpen] = useState(false);

    const contentDemands = demands.filter(demand => demand.type === 'content');
    const generalDemands = demands.filter(demand => demand.type === 'general');
    const totalDemands = demands.length;

    const contentIds = contentDemands.map(demand => demand.id);
    const generalIds = generalDemands.map(demand => demand.id);

    const handleRemove = async () => {
        if (totalDemands === 0) {
            toast.error("Selecione pelo menos uma demanda");
            return;
        }

        setIsLoading(true);
        const removedIds: string[] = [];
        let successCount = 0;

        try {
            if (contentIds.length > 0) {
                const contentResponse = await fetch(`/api/contents/remove-bulk`, {
                    method: "DELETE",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ contentIds }),
                });

                if (contentResponse.ok) {
                    const result = await contentResponse.json();
                    successCount += result.count;
                    removedIds.push(...contentIds);
                } else {
                    const error = await contentResponse.json();
                    toast.error(error.message || "Erro ao remover demandas de conteúdo");
                }
            }

            if (generalIds.length > 0) {
                const generalResponse = await fetch(`/api/general-demands/remove-bulk`, {
                    method: "DELETE",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ demandIds: generalIds }),
                });

                if (generalResponse.ok) {
                    const result = await generalResponse.json();
                    successCount += result.count;
                    removedIds.push(...generalIds);
                } else {
                    const error = await generalResponse.json();
                    toast.error(error.message || "Erro ao remover demandas pontuais");
                }
            }

            if (successCount > 0) {
                toast.success(`${successCount} demandas removidas com sucesso`);
                setIsOpen(false);

                if (onLocalUpdate && removedIds.length > 0) {
                    onLocalUpdate(removedIds);
                }

                if (onSuccess) {
                    onSuccess();
                }
            }
        } catch (error) {
            console.error("Erro ao remover demandas:", error);
            toast.error("Erro ao remover demandas");
        } finally {
            setIsLoading(false);
        }
    };

    const getTipoDemandasText = () => {
        if (contentIds.length > 0 && generalIds.length > 0) {
            return "pontuais e de conteúdo";
        } else if (contentIds.length > 0) {
            return "de conteúdo";
        } else if (generalIds.length > 0) {
            return "pontuais";
        }
        return "";
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button
                    variant="destructive"
                    className="gap-1 whitespace-nowrap w-full sm:w-auto"
                    disabled={totalDemands === 0}
                >
                    <Trash2 className="h-4 w-4" />
                    Remover selecionados
                    {totalDemands > 0 && (
                        <Badge variant="secondary" className="ml-1">
                            {totalDemands}
                        </Badge>
                    )}
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Remover demandas</DialogTitle>
                    <DialogDescription>
                        {totalDemands > 0
                            ? `Tem certeza que deseja remover ${totalDemands} demanda${totalDemands > 1 ? 's' : ''} ${getTipoDemandasText()}?`
                            : "Selecione pelo menos uma demanda para remover"}
                    </DialogDescription>
                </DialogHeader>
                {contentIds.length > 0 && generalIds.length > 0 && (
                    <div className="py-2 text-sm">
                        <p>• {contentIds.length} demanda{contentIds.length > 1 ? 's' : ''} de conteúdo</p>
                        <p>• {generalIds.length} demanda{generalIds.length > 1 ? 's' : ''} pontual{generalIds.length > 1 ? 'is' : ''}</p>
                    </div>
                )}
                <DialogFooter className="flex justify-end gap-2 mt-4">
                    <Button
                        variant="outline"
                        onClick={() => setIsOpen(false)}
                        className="whitespace-nowrap"
                    >
                        Cancelar
                    </Button>
                    <Button
                        variant="destructive"
                        onClick={handleRemove}
                        disabled={isLoading || totalDemands === 0}
                        className="whitespace-nowrap"
                    >
                        {isLoading ? <Ellipsis /> : 'Remover'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
