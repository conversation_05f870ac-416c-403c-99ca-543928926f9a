import { useState } from 'react';
import { Button } from '../ui/button';
import { Trash, Ellipsis } from 'lucide-react';
import { toast } from 'sonner';
import { 
    AlertDialog, 
    AlertDialogAction, 
    AlertDialogCancel, 
    AlertDialogContent, 
    AlertDialogDescription, 
    AlertDialogFooter, 
    AlertDialogHeader, 
    AlertDialogTitle, 
    AlertDialogTrigger 
} from '../ui/alert-dialog';

interface PlanActivity {
    id: string;
    details?: string;
    contentType?: string;
}

interface RemovesPlanActivityProps {
    content: PlanActivity;
    onSuccess: () => void;
    disabled?: boolean;
}

export const RemovesPlanActivity = ({ content, onSuccess, disabled }: RemovesPlanActivityProps) => {
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);

    const handleDelete = async () => {
        try {
            setLoading(true);
            
            const response = await fetch(`/api/contents/${content.id}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Falha ao excluir atividade');
            }

            toast.success('Atividade excluída com sucesso');
            setOpen(false);
            onSuccess();
        } catch (err) {
            console.error('Erro ao excluir atividade:', err);
            toast.error('Falha ao excluir atividade. Tente novamente.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <AlertDialog open={open} onOpenChange={setOpen}>
            <AlertDialogTrigger asChild>
                <Button
                    variant="outline"
                    size="icon"
                    disabled={disabled}
                >
                    <Trash className="text-red-600" />
                </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>
                        Deletar atividade
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                        Esta ação não pode ser desfeita. Isso excluirá permanentemente 
                        {content.contentType && <span className="font-medium"> {content.contentType}</span>} 
                        {content.details && <span className="block mt-1 text-sm italic">&quot;{content.details.substring(0, 60)}{content.details.length > 60 ? '...' : ''}&quot;</span>}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                    <AlertDialogAction 
                        onClick={(e) => {
                            e.preventDefault();
                            handleDelete();
                        }}
                        disabled={loading}
                        className="bg-red-600 hover:bg-red-700"
                    >
                        {loading ? (
                            <>
                                <Ellipsis />
                            </>
                        ) : (
                            'Excluir'
                        )}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}