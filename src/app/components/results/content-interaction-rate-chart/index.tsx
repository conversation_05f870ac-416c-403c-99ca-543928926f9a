"use client";

import { useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveC<PERSON>r, LabelList } from "recharts";

interface ContentInteractionRateChartProps {
    postInteractionRate?: number;
    reelInteractionRate?: number;
    storyReplies?: number;
    liveComments?: number;
    month: string;
    year: number;
}

interface CustomizedLabelProps {
    x?: number | string;
    y?: number | string;
    width?: number | string;
    value?: number | string;
    name?: string;
}

const CustomizedLabel = (props: CustomizedLabelProps) => {
    const { x, y, width, value, name } = props;

    if (value === undefined || Number(value) <= 0) return null;

    const isPercentage = name?.startsWith("Taxa");
    const formattedValue = isPercentage
        ? `${Number(value).toFixed(2)}%`
        : Math.round(Number(value) * 100);

    return (
        <text
            x={Number(x) + (Number(width) / 2)}
            y={Number(y) - 5}
            fill="hsl(var(--foreground))"
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize={12}
        >
            {formattedValue}
        </text>
    );
};

export const ContentInteractionRateChart = ({
    postInteractionRate = 0,
    reelInteractionRate = 0,
    storyReplies = 0,
    liveComments = 0,
    month,
    year
}: ContentInteractionRateChartProps) => {
    const chartData = useMemo(() => {
        return [
            { name: "Taxa Posts", value: postInteractionRate },
            { name: "Taxa Reels", value: reelInteractionRate },
            { name: "R. Stories", value: storyReplies / 100 },
            { name: "C. Lives", value: liveComments / 100 }
        ].filter(item => item.value > 0);
    }, [postInteractionRate, reelInteractionRate, storyReplies, liveComments]);

    if (chartData.length === 0) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="text-base">Taxas de Interação</CardTitle>
                    <CardDescription>{month} de {year}</CardDescription>
                </CardHeader>
                <CardContent className="flex items-center justify-center h-[250px]">
                    <p className="text-muted-foreground">Sem dados de interação disponíveis</p>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader className="pb-2">
                <CardTitle className="text-base">Taxas de interação</CardTitle>
                <CardDescription>{month} de {year}</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                            data={chartData}
                            margin={{ top: 30, right: 10, left: 10, bottom: 20 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="hsl(var(--border))" />
                            <XAxis
                                dataKey="name"
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 10 }}
                                angle={-45}
                                textAnchor="end"
                            />
                            <YAxis
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 12 }}
                                tickFormatter={(value) => {
                                    if (chartData.findIndex(d => d.value === value) < 2) {
                                        return `${value}%`;
                                    }
                                    return `${value * 100}`;
                                }}
                                hide
                            />
                            <Tooltip
                                formatter={(value, name) => {
                                    if (name === "Taxa Posts" || name === "Taxa Reels") {
                                        return [`${Number(value).toFixed(2)}%`, name];
                                    }
                                    return [Math.round(Number(value) * 100), name];
                                }}
                                separator=": "
                                contentStyle={{
                                    backgroundColor: 'hsl(var(--popover))',
                                    borderWidth: '1px',
                                    borderStyle: 'solid',
                                    borderColor: 'hsl(var(--border))',
                                    borderRadius: '0.375rem',
                                    boxShadow: '0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06)',
                                    color: 'hsl(var(--popover-foreground))',
                                    fontWeight: '500',
                                    padding: '6px 8px'
                                }}
                                labelStyle={{
                                    color: 'hsl(var(--popover-foreground))',
                                    fontSize: '12px',
                                    fontWeight: '600',
                                    marginBottom: '2px'
                                }}
                                itemStyle={{
                                    color: 'hsl(var(--popover-foreground))',
                                    fontSize: '12px',
                                    fontWeight: '500'
                                }}
                            />
                            <Bar
                                dataKey="value"
                                fill="hsl(var(--chart-3))"
                                radius={[4, 4, 0, 0]}
                            >
                                <LabelList dataKey="value" content={CustomizedLabel} />
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>
                </div>
            </CardContent>
        </Card>
    );
};

export default ContentInteractionRateChart;