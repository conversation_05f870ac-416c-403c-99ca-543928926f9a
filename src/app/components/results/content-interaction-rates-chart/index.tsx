"use client";

import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/app/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Radial<PERSON><PERSON>, ResponsiveContainer, Tooltip } from 'recharts';

interface ContentInteractionRatesChartProps {
    postInteractionRate?: number | null;
    reelInteractionRate?: number | null;
    storyInteractionRate?: number | null;
    liveInteractionRate?: number | null;
    videoInteractionRate?: number | null;
    month: string;
    year: number;
}

export const ContentInteractionRatesChart = ({
    postInteractionRate = 0,
    reelInteractionRate = 0,
    storyInteractionRate = 0,
    liveInteractionRate = 0,
    videoInteractionRate = 0,
    month,
    year
}: ContentInteractionRatesChartProps) => {
    const data = [
        {
            name: 'Posts',
            value: postInteractionRate || 0,
            fill: '#3b82f6'
        },
        {
            name: 'Reels',
            value: reelInteractionRate || 0,
            fill: '#ef4444'
        },
        {
            name: 'Stories',
            value: storyInteractionRate || 0,
            fill: '#10b981'
        },
        {
            name: 'Lives',
            value: liveInteractionRate || 0,
            fill: '#f59e0b'
        },
        {
            name: 'Víde<PERSON>',
            value: videoInteractionRate || 0,
            fill: '#8b5cf6'
        }
    ].filter(item => item.value > 0);

    return (
        <Card>
            <CardHeader className="pb-2">
                <CardTitle className="text-base">Taxa de interação por tipo de conteúdo</CardTitle>
                <CardDescription>{month} de {year}</CardDescription>
            </CardHeader>
            <CardContent>
                {data.length > 0 ? (
                    <>
                        <div className="h-[220px]">
                            <ResponsiveContainer width="100%" height="100%">
                                <RadialBarChart 
                                    cx="50%" 
                                    cy="50%" 
                                    innerRadius="10%" 
                                    outerRadius="100%" 
                                    data={data}
                                    startAngle={90}
                                    endAngle={-270}
                                >
                                    <RadialBar
                                        label={{ position: 'insideStart', fill: '#fff', fontSize: 12 }}
                                        background={{ fill: 'hsl(var(--muted-foreground)/0.1)' }}
                                        dataKey="value"
                                    />
                                    <Tooltip 
                                        formatter={(value) => [`${value}%`, 'Taxa de interação']}
                                        separator=": "
                                        contentStyle={{
                                            backgroundColor: 'hsl(var(--popover))',
                                            borderWidth: '1px',
                                            borderStyle: 'solid',
                                            borderColor: 'hsl(var(--border))',
                                            borderRadius: '0.375rem',
                                            boxShadow: '0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06)',
                                            color: 'hsl(var(--popover-foreground))',
                                            fontWeight: '500',
                                            padding: '6px 8px'
                                        }}
                                        labelStyle={{
                                            color: 'hsl(var(--popover-foreground))',
                                            fontSize: '12px',
                                            fontWeight: '600',
                                            marginBottom: '2px'
                                        }}
                                        itemStyle={{
                                            color: 'hsl(var(--popover-foreground))',
                                            fontSize: '12px',
                                            fontWeight: '500'
                                        }}
                                    />
                                </RadialBarChart>
                            </ResponsiveContainer>
                        </div>

                        <div className="mt-3">
                            <div className="grid grid-cols-5 gap-1 text-center">
                                {data.map((item, index) => (
                                    <div key={index} className="p-2 bg-muted/30 rounded-md">
                                        <div 
                                            className="w-2 h-2 rounded-full mx-auto mb-1" 
                                            style={{ backgroundColor: item.fill }}
                                        ></div>
                                        <p className="text-[10px] font-medium text-muted-foreground">{item.name}</p>
                                        <p className="text-[12px] font-bold">{item.value}%</p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </>
                ) : (
                    <div className="h-[220px] flex items-center justify-center">
                        <p className="text-muted-foreground">
                            Sem dados de taxa de interação
                        </p>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default ContentInteractionRatesChart;
