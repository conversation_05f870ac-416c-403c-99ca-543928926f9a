"use client";

import { useMemo } from "react";
import { 
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
    <PERSON><PERSON><PERSON><PERSON>, 
    Legend, 
    ResponsiveContainer, 
    PolarAngleAxis, 
    Tooltip,
} from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import React from "react";

interface ContentTypeDistributionProps {
    staticStories?: number;
    animatedStories?: number;
    staticFeed?: number;
    reel?: number;
    extraMaterials?: number;
    month: string;
    year: number;
}

interface ContentItem {
    name: string;
    value: number;
    fill: string;
}

interface TooltipProps {
    active?: boolean;
    payload?: Array<{
        payload: ContentItem;
    }>;
}

export function ContentTypeDistribution({
    staticStories = 0,
    animatedStories = 0,
    staticFeed = 0,
    reel = 0,
    extraMaterials = 0,
    month,
    year
}: ContentTypeDistributionProps) {
    const chartData = useMemo(() => {
        const rawData = [
            { name: "Stories estáticos", value: staticStories, fill: "hsl(var(--chart-1))" },
            { name: "Stories animados", value: animatedStories, fill: "hsl(var(--chart-2))" },
            { name: "Feed estático", value: staticFeed, fill: "hsl(var(--chart-3))" },
            { name: "Feed manual", value: reel, fill: "hsl(var(--chart-4))" },
            { name: "Materiais extras", value: extraMaterials, fill: "hsl(var(--chart-5))" }
        ].filter(item => item.value > 0);

        return [...rawData].sort((a, b) => a.value - b.value);
    }, [staticStories, animatedStories, staticFeed, reel, extraMaterials]);

    const total = useMemo(() =>
        chartData.reduce((sum, item) => sum + item.value, 0),
    [chartData]);

    const CustomTooltip = ({ active, payload }: TooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            const percent = ((data.value / total) * 100).toFixed(0);
            
            return (
                <div className="bg-card border border-border p-2 rounded-md shadow-sm">
                    <p className="font-semibold text-xs">{data.name}</p>
                    <p className="text-sm">{data.value} itens</p>
                    <p className="text-xs text-muted-foreground">{percent}% do total</p>
                </div>
            );
        }
        return null;
    };

    if (total === 0) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="text-base">Distribuição de conteúdo</CardTitle>
                    <CardDescription>{month} de {year}</CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col items-center justify-center h-[250px]">
                    <p className="text-muted-foreground text-center">
                        Não há dados sobre os tipos de conteúdo disponíveis
                    </p>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className="flex flex-col h-full">
            <CardHeader className="pb-2">
                <CardTitle className="text-base">Distribuição de conteúdo</CardTitle>
                <CardDescription>{month} de {year}</CardDescription>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col justify-between">
                <div className="h-[240px]">
                    <ResponsiveContainer width="100%" height="100%">
                        <RadialBarChart
                            innerRadius="15%"
                            outerRadius="75%"
                            data={chartData}
                            startAngle={0}
                            endAngle={360}
                            barCategoryGap={2} 
                            barSize={20}       
                            cy="50%"          
                            cx="50%"
                        >
                            <PolarAngleAxis
                                type="number"
                                domain={[0, 'auto']}
                                angleAxisId={0}
                                tick={false}
                            />
                            <RadialBar
                                background={{ fill: "hsl(var(--muted-foreground)/0.1)" }}
                                dataKey="value"
                                cornerRadius={4}
                                animationDuration={1000}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Legend
                                content={(props) => {
                                    const { payload } = props;
                                    if (!payload) return null;
                                    
                                    return (
                                        <ul className="flex flex-wrap justify-center gap-x-3 gap-y-1 text-xs">
                                            {payload.map((entry, index) => (
                                                <li key={`legend-${index}`} className="flex items-center">
                                                    <div 
                                                        className="w-2 h-2 mr-1 rounded-sm" 
                                                        style={{ backgroundColor: entry.color || '#ccc' }}
                                                    />
                                                    <span className="text-[10px]">
                                                        {entry.value} ({chartData[index]?.value || 0})
                                                    </span>
                                                </li>
                                            ))}
                                        </ul>
                                    );
                                }}
                                verticalAlign="bottom"
                                align="center"
                            />
                        </RadialBarChart>
                    </ResponsiveContainer>
                </div>

                <div className="grid grid-cols-2 gap-2 mt-2 text-xs text-center border-t pt-2">
                    <div>
                        <p className="font-bold">{staticStories + animatedStories}</p>
                        <p className="text-muted-foreground">Total Stories</p>
                    </div>
                    <div>
                        <p className="font-bold">{staticFeed + reel}</p>
                        <p className="text-muted-foreground">Total Feed</p>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}