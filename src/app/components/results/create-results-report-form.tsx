import { useState, useEffect } from 'react';
import { Input } from '@/app/components/ui/input';
import { Label } from '@/app/components/ui/label';
import { Button } from '@/app/components/ui/button';
import { toast } from 'sonner';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../ui/tabs';
import {
    Database,
    Users,
    Eye,
    Pointer,
    Plus,
    Trash2
} from 'lucide-react';
import { Result, ResultsReport } from '@prisma/client';
import { Separator } from '../ui/separator';

interface CreateResultsReportFormProps {
    clientId: string;
    selectedMonth: string | null;
    monthNames: string[];
    existingReport?: ResultsReport & { results?: Result[] } | null;
    onSuccess: () => void;
    onCancel: () => void;
}

type FormDataType = {
    // Campos básicos
    newFollowers: number | string;
    totalPosts: number | string;
    totalInteractions: number | string;
    totalStories: number | string;
    totalViews: number | string;

    // Aba Visualizações - novos campos
    percentageAds: number | string;
    percentageFollowers: number | string;
    percentageNonFollowers: number | string;

    accountsReached: number | string; // Contas alcançadas (antes totalReach)
    percentageAccountsReached: number | string;

    profileActivity: number | string; // Atividade no perfil (número)
    percentageProfileActivity: number | string; // Atividade no perfil (percentual)

    profileVisits: number | string; // Visitas ao perfil (número)
    percentageProfileVisits: number | string; // Visitas ao perfil (percentual)

    externalLinkTaps: number | string; // Toques em links externos (número)
    percentageExternalLinkTaps: number | string; // Toques em links externos (percentual)

    businessAddressTaps: number | string; // Toques no endereço comercial (número)
    percentageBusinessAddressTaps: number | string; // Toques no endereço comercial (percentual)

    // Aba Interações - novos campos
    interactionsPercentageAds: number | string; // Percentual de anúncios
    interactionsPercentageFollowers: number | string; // Percentual de seguidores
    interactionsPercentageNonFollowers: number | string; // Percentual de não seguidores

    // Reel
    reelInteractionPercentage: number | string; // Percentual de interação
    reelLikes: number | string; // Curtidas
    reelComments: number | string; // Comentários
    reelSaves: number | string; // Salvamentos
    reelShares: number | string; // Compartilhamentos

    // Publicações
    postInteractionPercentage: number | string; // Percentual de interação
    postLikes: number | string; // Curtidas
    postComments: number | string; // Comentários
    postSaves: number | string; // Salvamentos
    postShares: number | string; // Compartilhamentos

    // Story
    storyInteractionPercentage: number | string; // Percentual de interação
    storyReplies: number | string; // Respostas

    // Vídeos ao vivo
    liveInteractionPercentage: number | string; // Percentual de interação
    liveComments: number | string; // Comentários

    // Vídeos
    videoInteractionPercentage: number | string; // Percentual de interação
    videoLikes: number | string; // Curtidas
    videoComments: number | string; // Comentários
    videoSaves: number | string; // Salvamentos
    videoShares: number | string; // Compartilhamentos

    // Aba 4 - Seguidores
    totalFollowers: number | string; // Total de seguidores
    unfollowCount: number | string; // Deixaram de seguir

    // Localização dos seguidores (formato JSON simples para o formulário)
    followersLocation: { [city: string]: number | string };

    // Faixa etária dos seguidores (formato JSON simples para o formulário)  
    followersAgeRange: { [range: string]: number | string };

    // Gênero dos seguidores (formato JSON simples para o formulário)
    followersGender: { [gender: string]: number | string };
};

export default function CreateResultsReportForm({
    clientId,
    selectedMonth,
    monthNames,
    existingReport,
    onSuccess,
    onCancel
}: CreateResultsReportFormProps) {
    // Função auxiliar para converter vírgulas para pontos antes do parse
    const normalizeDecimalValue = (value: string | number): string => {
        if (typeof value === 'number') return String(value);
        // Substituir todas as vírgulas por pontos
        return value.replace(/,/g, '.');
    };

    // Função auxiliar para formatação de valores do servidor para exibição
    const formatServerValueToDisplay = (value: number | null | undefined): string => {
        if (value === null || value === undefined) return '0';
        // Sempre exibir valores com vírgulas
        return String(value).replace('.', ',');
    };

    const [formData, setFormData] = useState<FormDataType>(() => {
        if (existingReport && existingReport.results && existingReport.results[0]) {
            const result = existingReport.results[0];

            // Usar a função auxiliar para formatação consistente
            const formatValue = formatServerValueToDisplay;

            try {
                return {
                    // Todos os campos usando formatValue para garantir consistência
                    newFollowers: formatValue(result.newFollowers),
                    totalPosts: formatValue(result.totalPosts),
                    totalInteractions: formatValue(result.totalInteractions),
                    totalStories: formatValue(result.totalStories),
                    totalViews: formatValue(result.totalViews),

                    // Aba Visualizações - novos campos
                    percentageAds: formatValue(result.percentageAds),
                    percentageFollowers: formatValue(result.percentageFollowers),
                    percentageNonFollowers: formatValue(result.percentageNonFollowers),

                    accountsReached: formatValue(result.accountsReached),
                    percentageAccountsReached: formatValue(result.percentageAccountsReached),

                    profileActivity: formatValue(result.profileActivity),
                    percentageProfileActivity: formatValue(result.percentageProfileActivity),

                    profileVisits: formatValue(result.profileVisits),
                    percentageProfileVisits: formatValue(result.percentageProfileVisits),

                    externalLinkTaps: formatValue(result.externalLinkTaps),
                    percentageExternalLinkTaps: formatValue(result.percentageExternalLinkTaps),

                    businessAddressTaps: formatValue(result.businessAddressTaps),
                    percentageBusinessAddressTaps: formatValue(result.percentageBusinessAddressTaps),

                    // Aba Interações - novos campos
                    interactionsPercentageAds: formatValue(result.interactionsPercentageAds),
                    interactionsPercentageFollowers: formatValue(result.interactionsPercentageFollowers),
                    interactionsPercentageNonFollowers: formatValue(result.interactionsPercentageNonFollowers),

                    // Reel
                    reelInteractionPercentage: formatValue(result.reelInteractionPercentage),
                    reelLikes: formatValue(result.reelLikes),
                    reelComments: formatValue(result.reelComments),
                    reelSaves: formatValue(result.reelSaves),
                    reelShares: formatValue(result.reelShares),

                    // Publicações
                    postInteractionPercentage: formatValue(result.postInteractionPercentage),
                    postLikes: formatValue(result.postLikes),
                    postComments: formatValue(result.postComments),
                    postSaves: formatValue(result.postSaves),
                    postShares: formatValue(result.postShares),

                    // Story
                    storyInteractionPercentage: formatValue(result.storyInteractionPercentage),
                    storyReplies: formatValue(result.storyReplies),

                    // Vídeos ao vivo
                    liveInteractionPercentage: formatValue(result.liveInteractionPercentage),
                    liveComments: formatValue(result.liveComments),

                    // Vídeos
                    videoInteractionPercentage: formatValue(result.videoInteractionPercentage),
                    videoLikes: formatValue(result.videoLikes),
                    videoComments: formatValue(result.videoComments),
                    videoSaves: formatValue(result.videoSaves),
                    videoShares: formatValue(result.videoShares),

                    // Aba 4 - Seguidores 
                    totalFollowers: formatValue(result.totalFollowers),
                    unfollowCount: formatValue(result.unfollowCount),

                    // Inicializar objetos JSON para localização, faixa etária e gênero
                    followersLocation: result.followersLocation && typeof result.followersLocation === 'object'
                        ? result.followersLocation as { [city: string]: number | string }
                        : { 'São Paulo': '0', 'Rio de Janeiro': '0', 'Outras': '0' },
                    followersAgeRange: result.followersAgeRange && typeof result.followersAgeRange === 'object'
                        ? result.followersAgeRange as { [range: string]: number | string }
                        : {},
                    followersGender: result.followersGender && typeof result.followersGender === 'object'
                        ? result.followersGender as { [gender: string]: number | string }
                        : {}
                };
            } catch (e) {
                console.error("Erro ao inicializar dados do formulário:", e);
                // Retornar valores padrão em caso de erro
            }
        }

        // Valores padrão formatados no mesmo padrão
        return {
            newFollowers: '0',
            totalPosts: '0',
            totalInteractions: '0',
            totalStories: '0',
            totalViews: '0',

            // Aba Visualizações - novos campos
            percentageAds: '0',
            percentageFollowers: '0',
            percentageNonFollowers: '0',

            accountsReached: '0',
            percentageAccountsReached: '0',

            profileActivity: '0',
            percentageProfileActivity: '0',

            profileVisits: '0',
            percentageProfileVisits: '0',

            externalLinkTaps: '0',
            percentageExternalLinkTaps: '0',

            businessAddressTaps: '0',
            percentageBusinessAddressTaps: '0',

            // Aba Interações - novos campos
            interactionsPercentageAds: '0',
            interactionsPercentageFollowers: '0',
            interactionsPercentageNonFollowers: '0',

            // Reel
            reelInteractionPercentage: '0',
            reelLikes: '0',
            reelComments: '0',
            reelSaves: '0',
            reelShares: '0',

            // Publicações
            postInteractionPercentage: '0',
            postLikes: '0',
            postComments: '0',
            postSaves: '0',
            postShares: '0',

            // Story
            storyInteractionPercentage: '0',
            storyReplies: '0',

            // Vídeos ao vivo
            liveInteractionPercentage: '0',
            liveComments: '0',

            // Vídeos
            videoInteractionPercentage: '0',
            videoLikes: '0',
            videoComments: '0',
            videoSaves: '0',
            videoShares: '0',

            // Aba 4 - Seguidores - valores padrão
            totalFollowers: '0',
            unfollowCount: '0',
            followersLocation: { 'São Paulo': '0', 'Rio de Janeiro': '0', 'Outras': '0' },
            followersAgeRange: {},
            followersGender: {}
        };
    });

    const [isMobile, setIsMobile] = useState(false);
    const [newCityName, setNewCityName] = useState('');
    const [showCitySuggestions, setShowCitySuggestions] = useState(false);

    // Lista de cidades brasileiras populares para sugestões
    const citySuggestions = [
        'São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Salvador', 'Brasília',
        'Fortaleza', 'Manaus', 'Curitiba', 'Recife', 'Porto Alegre',
        'Belém', 'Goiânia', 'Guarulhos', 'Campinas', 'São Luís',
        'São Gonçalo', 'Maceió', 'Duque de Caxias', 'Campo Grande', 'Natal',
        'Teresina', 'São Bernardo do Campo', 'Nova Iguaçu', 'João Pessoa', 'Santo André',
        'Osasco', 'São José dos Campos', 'Jaboatão dos Guararapes', 'Ribeirão Preto', 'Sorocaba',
        'Contagem', 'Aracaju', 'Feira de Santana', 'Cuiabá', 'Joinville',
        'Aparecida de Goiânia', 'Londrina', 'Juiz de Fora', 'Ananindeua', 'Niterói',
        'Outras'
    ];

    useEffect(() => {
        const checkScreenSize = () => {
            setIsMobile(window.innerWidth < 768);
        };

        checkScreenSize();

        window.addEventListener('resize', checkScreenSize);

        return () => window.removeEventListener('resize', checkScreenSize);
    }, []);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;

        // Mantendo o valor original com vírgulas para exibição
        const displayValue = value;

        // Campos percentuais (podem ser negativos)
        const percentageFields = [
            // Aba 2 - Visualizações
            'percentageAds', 'percentageFollowers', 'percentageNonFollowers',
            'percentageAccountsReached', 'percentageProfileActivity', 'percentageProfileVisits',
            'percentageExternalLinkTaps', 'percentageBusinessAddressTaps',
            
            // Aba 3 - Interações
            'interactionsPercentageAds', 'interactionsPercentageFollowers', 'interactionsPercentageNonFollowers',
            'reelInteractionPercentage', 'postInteractionPercentage', 'storyInteractionPercentage',
            'liveInteractionPercentage', 'videoInteractionPercentage'
        ];

        // Todos os campos que devem aceitar valores decimais
        const floatValueFields = [
            // Aba visualizações (antiga cresc)
            'percentageAds', 'percentageFollowers', 'percentageNonFollowers',
            'accountsReached', 'percentageAccountsReached',
            'profileActivity', 'percentageProfileActivity',
            'profileVisits', 'percentageProfileVisits',
            'externalLinkTaps', 'percentageExternalLinkTaps',
            'businessAddressTaps', 'percentageBusinessAddressTaps',

            // Campos de interactions - percentuais gerais
            'interactionsPercentageAds', 'interactionsPercentageFollowers', 'interactionsPercentageNonFollowers',

            // Campos de interactions-dist - reels, posts, stories, lives, videos
            'reelInteractionPercentage', 'reelLikes', 'reelComments', 'reelSaves', 'reelShares',
            'postInteractionPercentage', 'postLikes', 'postComments', 'postSaves', 'postShares',
            'storyInteractionPercentage', 'storyReplies',
            'liveInteractionPercentage', 'liveComments',
            'videoInteractionPercentage', 'videoLikes', 'videoComments', 'videoSaves', 'videoShares'
        ];

        // Converter vírgulas para pontos antes de processar
        const normalizedValue = normalizeDecimalValue(value);

        try {
            if (percentageFields.includes(name)) {
                setFormData((prevState) => ({
                    ...prevState,
                    [name]: displayValue,
                    [`_${name}`]: normalizedValue === '' ? null : parseFloat(normalizedValue) || 0
                }));
            }
            else if (floatValueFields.includes(name)) {
                const parsedValue = normalizedValue === '' ? 0 : parseFloat(normalizedValue) || 0;

                setFormData((prevState) => ({
                    ...prevState,
                    [name]: displayValue,
                    [`_${name}`]: parsedValue
                }));
            }
            else {
                // Casos especiais com sincronização adicional
                if (name === 'totalInteractions') {
                    const parsedValue = normalizedValue === '' ? 0 : parseFloat(normalizedValue) || 0;

                    setFormData((prevState) => ({
                        ...prevState,
                        [name]: displayValue,
                        [`_${name}`]: parsedValue
                    }));
                }
                else if (name === 'totalPosts') {
                    const parsedValue = normalizedValue === '' ? 0 : parseFloat(normalizedValue) || 0;

                    setFormData((prevState) => ({
                        ...prevState,
                        [name]: displayValue,
                        [`_${name}`]: parsedValue
                    }));
                }
                else {
                    // Outros campos (inteiros)
                    const parsedValue = normalizedValue === '' ? 0 : parseFloat(normalizedValue) || 0;

                    setFormData((prevState) => ({
                        ...prevState,
                        [name]: displayValue,
                        [`_${name}`]: parsedValue
                    }));
                }
            }
        } catch (error) {
            console.error(`Erro ao processar campo ${name} com valor ${value}:`, error);
        }
    };

    // Nova função para lidar com dados de seguidores (localização, faixa etária, gênero)
    const handleFollowersDataChange = (
        objectName: 'followersLocation' | 'followersAgeRange' | 'followersGender',
        fieldName: string,
        value: string
    ) => {
        try {
            setFormData(prev => ({
                ...prev,
                [objectName]: {
                    ...prev[objectName],
                    [fieldName]: value
                }
            }));
        } catch (error) {
            console.error(`Erro ao processar campo de seguidores ${objectName}.${fieldName}:`, error);
        }
    };

    // Função para adicionar uma nova cidade
    const addCity = (cityName: string) => {
        if (cityName.trim() && !formData.followersLocation[cityName.trim()]) {
            setFormData(prev => ({
                ...prev,
                followersLocation: {
                    ...prev.followersLocation,
                    [cityName.trim()]: '0'
                }
            }));
        }
    };

    // Função para remover uma cidade
    const removeCity = (cityName: string) => {
        setFormData(prev => {
            const newLocation = { ...prev.followersLocation };
            delete newLocation[cityName];
            return {
                ...prev,
                followersLocation: newLocation
            };
        });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            // Criar uma cópia limpa dos dados para envio à API
            // Definindo tipos mais específicos para evitar o uso de "any"
            interface ProcessedData {
                [key: string]: number | null | {
                    [key: string]: number | null;
                };
            }

            const processedData: ProcessedData = {};

            // Função unificada para processar valores
            const processValue = (value: string | number): number | null => {
                if (value === '' || value === undefined || value === null) return null;
                if (typeof value === 'number') return value;

                // Converter todas as vírgulas para pontos
                return parseFloat(normalizeDecimalValue(value)) || 0;
            };

            // Processar campos regulares
            for (const key in formData) {
                // Pular campos temporários, objetos aninhados e campos JSON
                if (key === 'followersLocation' ||
                    key === 'followersAgeRange' ||
                    key === 'followersGender' ||
                    key.startsWith('_')) continue;

                // Verificar se existe uma versão pré-processada do campo
                if (formData[`_${key}` as keyof typeof formData] !== undefined) {
                    processedData[key] = formData[`_${key}` as keyof typeof formData] as number;
                } else {
                    // Processar o valor original
                    processedData[key] = processValue(formData[key as keyof typeof formData] as string | number);
                }
            }

            // Processar campos JSON de seguidores (localização, faixa etária, gênero)
            const processFollowersData = (data: { [key: string]: number | string }) => {
                const processed: { [key: string]: number } = {};
                for (const [key, value] of Object.entries(data)) {
                    if (value !== '' && value !== '0') {
                        processed[key] = processValue(value) || 0;
                    }
                }
                return Object.keys(processed).length > 0 ? processed : null;
            };

            // Adicionar campos JSON de seguidores
            processedData.followersLocation = processFollowersData(formData.followersLocation);
            processedData.followersAgeRange = processFollowersData(formData.followersAgeRange);
            processedData.followersGender = processFollowersData(formData.followersGender);

            const selectedMonthIndex = monthNames.findIndex(
                m => m.toLowerCase() === selectedMonth
            ) + 1;

            const endpoint = existingReport
                ? `/api/results-report/${existingReport.id}`
                : '/api/results-report';

            const method = existingReport ? 'PUT' : 'POST';

            const response = await fetch(endpoint, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    clientId,
                    month: existingReport ? existingReport.month : selectedMonthIndex,
                    year: existingReport ? existingReport.year : new Date().getFullYear(),
                    results: [processedData]
                }),
            });

            if (response.ok) {
                toast.success(existingReport ? 'Relatório atualizado com sucesso' : 'Relatório criado com sucesso');
                onSuccess();
            } else {
                // Tipagem para a resposta de erro
                interface ErrorResponse {
                    message?: string;
                    [key: string]: unknown;
                }

                const errorData: ErrorResponse = await response.json().catch(() => ({}));
                console.error('Erro ao salvar:', errorData);
                toast.error(existingReport ? `Erro ao atualizar relatório: ${errorData.message || ''}` : `Erro ao criar relatório: ${errorData.message || ''}`);
            }
        } catch (error) {
            console.error('Erro ao enviar dados:', error);
            toast.error(existingReport ? 'Erro ao atualizar relatório' : 'Erro ao criar relatório');
        }
    };

    return (
        <form onSubmit={handleSubmit} className="overflow-hidden w-full">
            <div className={`${isMobile ? "flex flex-row" : ""}`}>
                <Tabs defaultValue="basic" className={`w-full ${isMobile ? "flex" : ""} px-3`}>
                    <div className={isMobile ? "w-1/3 min-w-[130px] px-2 py-4 border-r mr-3 bg-zinc-100 dark:bg-zinc-800" : ""}>
                        <TabsList className={
                            isMobile
                                ? "flex flex-col h-auto space-y-1 bg-transparent w-full"
                                : window.innerWidth >= 768 && window.innerWidth < 1024
                                    ? "flex flex-row overflow-x-auto pb-2 gap-2 mb-4 whitespace-nowrap hide-scrollbar"
                                    : "grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-1 mb-4"
                        }>
                            <TabsTrigger
                                value="basic"
                                className={isMobile
                                    ? "justify-start text-xs py-1.5 h-auto w-full flex items-center gap-2 tabs-trigger-custom"
                                    : "flex items-center gap-1 py-1.5 tabs-trigger-custom"
                                }
                            >
                                <Database size={isMobile ? 14 : 16} />
                                <span>Básico</span>
                            </TabsTrigger>

                            <TabsTrigger
                                value="growth"
                                className={isMobile
                                    ? "justify-start text-xs py-1.5 h-auto w-full flex items-center gap-2 tabs-trigger-custom"
                                    : "flex items-center gap-1 py-1.5 tabs-trigger-custom"
                                }
                            >
                                <Eye size={isMobile ? 14 : 16} />
                                <span>{isMobile ? "Visua." : "Visualizações"}</span>
                            </TabsTrigger>

                            <TabsTrigger
                                value="reach"
                                className={isMobile
                                    ? "justify-start text-xs py-1.5 h-auto w-full flex items-center gap-2 tabs-trigger-custom"
                                    : "flex items-center gap-1 py-1.5 tabs-trigger-custom"
                                }
                            >
                                <Pointer size={isMobile ? 14 : 16} />
                                <span>Interações</span>
                            </TabsTrigger>

                            <TabsTrigger
                                value="engagement"
                                className={isMobile
                                    ? "justify-start text-xs py-1.5 h-auto w-full flex items-center gap-2 tabs-trigger-custom"
                                    : "flex items-center gap-1 py-1.5 tabs-trigger-custom"
                                }
                            >
                                <Users size={isMobile ? 14 : 16} />
                                <span>{isMobile ? "Segui." : "Seguidores"}</span>
                            </TabsTrigger>
                        </TabsList>
                    </div>

                    <div className={isMobile ? "w-2/3 flex-1" : "w-full"}>
                        <TabsContent value="basic" className="space-y-4">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="newFollowers">Novos seguidores</Label>
                                    <Input
                                        id="newFollowers"
                                        name="newFollowers"
                                        type="text"
                                        value={formData.newFollowers}
                                        onChange={handleInputChange}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="totalPosts">Total de posts</Label>
                                    <Input
                                        id="totalPosts"
                                        name="totalPosts"
                                        type="text"
                                        value={formData.totalPosts}
                                        onChange={handleInputChange}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="totalInteractions">Total de interações</Label>
                                    <Input
                                        id="totalInteractions"
                                        name="totalInteractions"
                                        type="text"
                                        value={formData.totalInteractions}
                                        onChange={handleInputChange}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="totalStories">Total de Stories</Label>
                                    <Input
                                        id="totalStories"
                                        name="totalStories"
                                        type="text"
                                        value={formData.totalStories}
                                        onChange={handleInputChange}
                                    />
                                </div>
                                <div>
                                    <Label htmlFor="totalViews">Total de visualizações</Label>
                                    <Input
                                        id="totalViews"
                                        name="totalViews"
                                        type="text"
                                        value={formData.totalViews}
                                        onChange={handleInputChange}
                                    />
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="growth" className="space-y-6">
                            {/* Seção: Visualizações */}
                            <div>
                                <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-3 border-b pb-2">
                                    Visualizações
                                </h3>
                                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="percentageAds">Percentual de anúncios (%)</Label>
                                        <Input
                                            id="percentageAds"
                                            name="percentageAds"
                                            type="text"
                                            value={formData.percentageAds}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="percentageFollowers">Percentual de seguidores (%)</Label>
                                        <Input
                                            id="percentageFollowers"
                                            name="percentageFollowers"
                                            type="text"
                                            value={formData.percentageFollowers}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="percentageNonFollowers">Percentual de não seguidores (%)</Label>
                                        <Input
                                            id="percentageNonFollowers"
                                            name="percentageNonFollowers"
                                            type="text"
                                            value={formData.percentageNonFollowers}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            {/* Seção: Contas alcançadas */}
                            <div>
                                <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-3 border-b pb-2">
                                    Contas alcançadas
                                </h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="accountsReached">Contas alcançadas</Label>
                                        <Input
                                            id="accountsReached"
                                            name="accountsReached"
                                            type="text"
                                            value={formData.accountsReached}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="percentageAccountsReached">Percentual de contas alcançadas (%)</Label>
                                        <Input
                                            id="percentageAccountsReached"
                                            name="percentageAccountsReached"
                                            type="text"
                                            value={formData.percentageAccountsReached}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            {/* Seção: Atividade no perfil */}
                            <div>
                                <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-3 border-b pb-2">
                                    Atividade no perfil
                                </h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="profileActivity">Atividade no perfil</Label>
                                        <Input
                                            id="profileActivity"
                                            name="profileActivity"
                                            type="text"
                                            value={formData.profileActivity}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="percentageProfileActivity">Percentual de atividade (%)</Label>
                                        <Input
                                            id="percentageProfileActivity"
                                            name="percentageProfileActivity"
                                            type="text"
                                            value={formData.percentageProfileActivity}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            {/* Seção: Visitas ao perfil */}
                            <div>
                                <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-3 border-b pb-2">
                                    Visitas ao perfil
                                </h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="profileVisits">Visitas ao perfil</Label>
                                        <Input
                                            id="profileVisits"
                                            name="profileVisits"
                                            type="text"
                                            value={formData.profileVisits}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="percentageProfileVisits">Percentual de visitas (%)</Label>
                                        <Input
                                            id="percentageProfileVisits"
                                            name="percentageProfileVisits"
                                            type="text"
                                            value={formData.percentageProfileVisits}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            {/* Seção: Toques em links externos */}
                            <div>
                                <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-3 border-b pb-2">
                                    Toques em links externos
                                </h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="externalLinkTaps">Toques em links externos</Label>
                                        <Input
                                            id="externalLinkTaps"
                                            name="externalLinkTaps"
                                            type="text"
                                            value={formData.externalLinkTaps}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="percentageExternalLinkTaps">Percentual de toques (%)</Label>
                                        <Input
                                            id="percentageExternalLinkTaps"
                                            name="percentageExternalLinkTaps"
                                            type="text"
                                            value={formData.percentageExternalLinkTaps}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            {/* Seção: Toques no endereço comercial */}
                            <div>
                                <h3 className="text-base font-medium text-gray-900 dark:text-gray-100 mb-3 border-b pb-2">
                                    Toques no endereço comercial
                                </h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="businessAddressTaps">Toques no endereço comercial</Label>
                                        <Input
                                            id="businessAddressTaps"
                                            name="businessAddressTaps"
                                            type="text"
                                            value={formData.businessAddressTaps}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="percentageBusinessAddressTaps">Percentual de toques (%)</Label>
                                        <Input
                                            id="percentageBusinessAddressTaps"
                                            name="percentageBusinessAddressTaps"
                                            type="text"
                                            value={formData.percentageBusinessAddressTaps}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                        </TabsContent>

                        <TabsContent value="reach" className="space-y-4">
                            {/* Seção de Percentuais Gerais */}
                            <div>
                                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="interactionsPercentageAds">Percentual de anúncios</Label>
                                        <Input
                                            id="interactionsPercentageAds"
                                            name="interactionsPercentageAds"
                                            type="text"
                                            value={formData.interactionsPercentageAds}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="interactionsPercentageFollowers">Percentual de seguidores</Label>
                                        <Input
                                            id="interactionsPercentageFollowers"
                                            name="interactionsPercentageFollowers"
                                            type="text"
                                            value={formData.interactionsPercentageFollowers}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="interactionsPercentageNonFollowers">Percentual de não seguidores</Label>
                                        <Input
                                            id="interactionsPercentageNonFollowers"
                                            name="interactionsPercentageNonFollowers"
                                            type="text"
                                            value={formData.interactionsPercentageNonFollowers}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            {/* Seção de Reel */}
                            <div>
                                <h3 className="text-base font-semibold mb-4">Reel</h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="reelInteractionPercentage">Percentual de interação</Label>
                                        <Input
                                            id="reelInteractionPercentage"
                                            name="reelInteractionPercentage"
                                            type="text"
                                            value={formData.reelInteractionPercentage}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="reelLikes">Curtidas</Label>
                                        <Input
                                            id="reelLikes"
                                            name="reelLikes"
                                            type="text"
                                            value={formData.reelLikes}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="reelComments">Comentários</Label>
                                        <Input
                                            id="reelComments"
                                            name="reelComments"
                                            type="text"
                                            value={formData.reelComments}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="reelSaves">Salvamentos</Label>
                                        <Input
                                            id="reelSaves"
                                            name="reelSaves"
                                            type="text"
                                            value={formData.reelSaves}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="reelShares">Compartilhamentos</Label>
                                        <Input
                                            id="reelShares"
                                            name="reelShares"
                                            type="text"
                                            value={formData.reelShares}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            {/* Seção de Publicações */}
                            <div>
                                <h3 className="text-base font-semibold mb-4">Publicações</h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="postInteractionPercentage">Percentual de interação</Label>
                                        <Input
                                            id="postInteractionPercentage"
                                            name="postInteractionPercentage"
                                            type="text"
                                            value={formData.postInteractionPercentage}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="postLikes">Curtidas</Label>
                                        <Input
                                            id="postLikes"
                                            name="postLikes"
                                            type="text"
                                            value={formData.postLikes}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="postComments">Comentários</Label>
                                        <Input
                                            id="postComments"
                                            name="postComments"
                                            type="text"
                                            value={formData.postComments}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="postSaves">Salvamentos</Label>
                                        <Input
                                            id="postSaves"
                                            name="postSaves"
                                            type="text"
                                            value={formData.postSaves}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="postShares">Compartilhamentos</Label>
                                        <Input
                                            id="postShares"
                                            name="postShares"
                                            type="text"
                                            value={formData.postShares}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            {/* Seção de Story */}
                            <div>
                                <h3 className="text-base font-semibold mb-4">Story</h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="storyInteractionPercentage">Percentual de interação</Label>
                                        <Input
                                            id="storyInteractionPercentage"
                                            name="storyInteractionPercentage"
                                            type="text"
                                            value={formData.storyInteractionPercentage}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="storyReplies">Respostas</Label>
                                        <Input
                                            id="storyReplies"
                                            name="storyReplies"
                                            type="text"
                                            value={formData.storyReplies}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            {/* Seção de Vídeos ao vivo */}
                            <div>
                                <h3 className="text-base font-semibold mb-4">Vídeos ao vivo</h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="liveInteractionPercentage">Percentual de interação</Label>
                                        <Input
                                            id="liveInteractionPercentage"
                                            name="liveInteractionPercentage"
                                            type="text"
                                            value={formData.liveInteractionPercentage}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="liveComments">Comentários</Label>
                                        <Input
                                            id="liveComments"
                                            name="liveComments"
                                            type="text"
                                            value={formData.liveComments}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>

                            <Separator />

                            {/* Seção de Vídeos */}
                            <div>
                                <h3 className="text-base font-semibold mb-4">Vídeos</h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="videoInteractionPercentage">Percentual de interação</Label>
                                        <Input
                                            id="videoInteractionPercentage"
                                            name="videoInteractionPercentage"
                                            type="text"
                                            value={formData.videoInteractionPercentage}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="videoLikes">Curtidas</Label>
                                        <Input
                                            id="videoLikes"
                                            name="videoLikes"
                                            type="text"
                                            value={formData.videoLikes}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="videoComments">Comentários</Label>
                                        <Input
                                            id="videoComments"
                                            name="videoComments"
                                            type="text"
                                            value={formData.videoComments}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="videoSaves">Salvamentos</Label>
                                        <Input
                                            id="videoSaves"
                                            name="videoSaves"
                                            type="text"
                                            value={formData.videoSaves}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="videoShares">Compartilhamentos</Label>
                                        <Input
                                            id="videoShares"
                                            name="videoShares"
                                            type="text"
                                            value={formData.videoShares}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                </div>
                            </div>
                        </TabsContent>

                        <TabsContent value="engagement" className="space-y-4">
                            <div>
                                {/* Seção 1: Dados básicos de seguidores */}
                                <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6">
                                    <div>
                                        <Label htmlFor="totalFollowers">Total de seguidores</Label>
                                        <Input
                                            id="totalFollowers"
                                            name="totalFollowers"
                                            type="text"
                                            value={formData.totalFollowers}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="newFollowers">Novos seguidores</Label>
                                        <Input
                                            id="newFollowers"
                                            name="newFollowers"
                                            type="text"
                                            value={formData.newFollowers}
                                            onChange={handleInputChange}
                                            disabled
                                            className="bg-gray-100 dark:bg-gray-800"
                                        />
                                        <p className="text-xs text-gray-500 mt-1">Valor da aba Básico</p>
                                    </div>
                                    <div>
                                        <Label htmlFor="unfollowCount">Deixaram de seguir</Label>
                                        <Input
                                            id="unfollowCount"
                                            name="unfollowCount"
                                            type="text"
                                            value={formData.unfollowCount}
                                            onChange={handleInputChange}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="newFollowersPercentage">% Novos seguidores</Label>
                                        <Input
                                            id="newFollowersPercentage"
                                            type="text"
                                            value={(() => {
                                                const totalFollowers = parseFloat(normalizeDecimalValue(String(formData.totalFollowers))) || 0;
                                                const newFollowers = parseFloat(normalizeDecimalValue(String(formData.newFollowers))) || 0;

                                                if (totalFollowers > 0 && newFollowers > 0) {
                                                    const percentage = (newFollowers / totalFollowers) * 100;
                                                    return String(Math.round(percentage * 100) / 100).replace('.', ',');
                                                }
                                                return '0';
                                            })()}
                                            disabled
                                            className="bg-gray-100 dark:bg-gray-800"
                                        />
                                        <p className="text-xs text-gray-500 mt-1">Calculado automaticamente</p>
                                    </div>
                                </div>

                                <Separator className="my-6" />

                                {/* Seção 2: Localização dos seguidores */}
                                <div className="mb-6">
                                    <h4 className="text-sm font-medium mb-3">Localização (%)</h4>
                                    
                                    {/* Interface para adicionar nova cidade */}
                                    <div className="mb-4 p-4 pb-6 border rounded-lg bg-gray-50 dark:bg-zinc-900">
                                        <Label className="text-sm font-medium mb-2 block">Adicionar cidade</Label>
                                        <div className="relative">
                                            <div className="flex flex-col sm:flex-row gap-2">
                                                <div className="flex-1 relative">
                                                    <Input
                                                        type="text"
                                                        placeholder="Nome"
                                                        className='placeholder:text-sm'
                                                        value={newCityName}
                                                        onChange={(e) => {
                                                            setNewCityName(e.target.value);
                                                            setShowCitySuggestions(e.target.value.length > 0);
                                                        }}
                                                        onKeyPress={(e) => {
                                                            if (e.key === 'Enter') {
                                                                e.preventDefault();
                                                                addCity(newCityName);
                                                                setNewCityName('');
                                                                setShowCitySuggestions(false);
                                                            }
                                                        }}
                                                        onBlur={() => {
                                                            // Delay para permitir clique nas sugestões
                                                            setTimeout(() => setShowCitySuggestions(false), 200);
                                                        }}
                                                        onFocus={() => {
                                                            if (newCityName.length > 0) {
                                                                setShowCitySuggestions(true);
                                                            }
                                                        }}
                                                    />
                                                    
                                                    {/* Lista de sugestões */}
                                                    {showCitySuggestions && (
                                                        <div className="absolute top-full left-0 right-0 z-10 mt-1 bg-white dark:bg-gray-800 border rounded-md shadow-lg max-h-40 overflow-y-auto">
                                                            {citySuggestions
                                                                .filter(city => 
                                                                    city.toLowerCase().includes(newCityName.toLowerCase()) &&
                                                                    !formData.followersLocation[city]
                                                                )
                                                                .slice(0, 8)
                                                                .map(city => (
                                                                    <button
                                                                        key={city}
                                                                        type="button"
                                                                        className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 text-sm"
                                                                        onClick={() => {
                                                                            setNewCityName(city);
                                                                            setShowCitySuggestions(false);
                                                                        }}
                                                                    >
                                                                        {city}
                                                                    </button>
                                                                ))
                                                            }
                                                        </div>
                                                    )}
                                                </div>
                                                <Button
                                                    type="button"
                                                    onClick={() => {
                                                        addCity(newCityName);
                                                        setNewCityName('');
                                                        setShowCitySuggestions(false);
                                                    }}
                                                    size="sm"
                                                    disabled={!newCityName.trim() || !!formData.followersLocation[newCityName.trim()]}
                                                >
                                                    <Plus size={16} />
                                                    Adicionar
                                                </Button>
                                            </div>
                                        </div>
                                        {newCityName.trim() && !!formData.followersLocation[newCityName.trim()] && (
                                            <p className="text-xs text-red-500 mt-1">Esta cidade já foi adicionada</p>
                                        )}
                                    </div>

                                    {/* Lista de cidades */}
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {Object.keys(formData.followersLocation).length === 0 ? (
                                            <div className="col-span-full text-center text-gray-500 py-4">
                                                <p>Nenhuma cidade adicionada ainda.</p>
                                                <p className="text-xs">Use o campo acima para adicionar cidades.</p>
                                            </div>
                                        ) : (
                                            Object.keys(formData.followersLocation).map((city) => (
                                                <div key={city} className="relative">
                                                    <Label htmlFor={`location-${city.toLowerCase().replace(/\s+/g, '-')}`}>
                                                        {city}
                                                    </Label>
                                                    <div className="flex gap-1">
                                                        <Input
                                                            id={`location-${city.toLowerCase().replace(/\s+/g, '-')}`}
                                                            type="text"
                                                            value={formData.followersLocation[city] || '0'}
                                                            onChange={(e) => handleFollowersDataChange('followersLocation', city, e.target.value)}
                                                            placeholder="0,0"
                                                            className="flex-1"
                                                        />
                                                        <Button
                                                            type="button"
                                                            variant="destructive"
                                                            size="icon"
                                                            onClick={(
                                                            ) => removeCity(city)}
                                                            title="Remover cidade"
                                                        >
                                                            <Trash2 size={14} />
                                                        </Button>
                                                    </div>
                                                </div>
                                            ))
                                        )}
                                    </div>
                                </div>

                                <Separator className="my-6" />

                                {/* Seção 3: Faixa etária dos seguidores */}
                                <div className="mb-6">
                                    <h4 className="text-sm font-medium mb-3">Faixa etária (%)</h4>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                        {['13-17', '18-24', '25-34', '35-44', '45-54', '55-64', '65+'].map((range) => (
                                            <div key={range}>
                                                <Label htmlFor={`age-${range}`}>
                                                    {range} anos
                                                </Label>
                                                <Input
                                                    id={`age-${range}`}
                                                    type="text"
                                                    value={formData.followersAgeRange[range] || '0'}
                                                    onChange={(e) => handleFollowersDataChange('followersAgeRange', range, e.target.value)}
                                                    placeholder="0,0"
                                                />
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                <Separator className="my-6" />

                                {/* Seção 4: Gênero dos seguidores */}
                                <div className="mb-6">
                                    <h4 className="text-sm font-medium mb-3">Gênero (%)</h4>
                                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                                        {['Masculino', 'Feminino', 'Outros'].map((gender) => (
                                            <div key={gender}>
                                                <Label htmlFor={`gender-${gender.toLowerCase()}`}>
                                                    {gender}
                                                </Label>
                                                <Input
                                                    id={`gender-${gender.toLowerCase()}`}
                                                    type="text"
                                                    value={formData.followersGender[gender] || '0'}
                                                    onChange={(e) => handleFollowersDataChange('followersGender', gender, e.target.value)}
                                                    placeholder="0,0"
                                                />
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </TabsContent>
                    </div>
                </Tabs>
            </div>

            <div className={`mt-6 ${isMobile ? "flex flex-col" : "flex flex-col xs:flex-row justify-end"} gap-2`}>
                <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                    className={isMobile ? "order-2" : "order-2 xs:order-1"}
                >
                    Cancelar
                </Button>
                <Button
                    type="submit"
                    className={isMobile ? "order-1" : "order-1 xs:order-2"}
                >
                    Salvar relatório
                </Button>
            </div>
        </form>
    );
}