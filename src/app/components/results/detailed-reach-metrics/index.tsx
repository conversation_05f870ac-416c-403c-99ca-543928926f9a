"use client";

import { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card';
import {
    <PERSON><PERSON>hart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    ResponsiveContainer,
    Cell,
    LabelList
} from 'recharts';

interface DetailedReachMetricsProps {
    reachFollowers: number;
    reachNonFollowers: number;
    percentageProfileActivity: number;
    profileVisits: number;
    websiteClicks: number;
    profileActivity: number;
    month: string;
    year: number;
}

interface CustomizedLabelProps {
    x?: string | number;
    y?: string | number;
    width?: string | number;
    height?: string | number;
    value?: string | number;
    index?: number;
    payload?: {
        name: string;
        fullName: string;
        value: number;
        color: string;
    };
}

export const DetailedReachMetrics = ({
    percentageProfileActivity,
    profileVisits,
    profileActivity,
    month,
    year
}: DetailedReachMetricsProps) => {
    const chartData = useMemo(() => {
        return [
            {
                name: "Visitas",
                fullName: "Visitas ao perfil",
                value: profileVisits,
                color: "hsl(var(--chart-3))"
            },
            {
                name: "Atividade",
                fullName: "Atividade do perfil",
                value: profileActivity,
                color: "hsl(var(--chart-5))"
            }
        ].sort((a, b) => b.value - a.value);
    }, [profileVisits, profileActivity]);

    const maxValue = useMemo(() => {
        if (chartData.length === 0) return 1000;
        return Math.max(...chartData.map(item => item.value)) * 1.2;
    }, [chartData]);

    const formatNumber = (value: number) => {
        if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
        if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
        return value.toString();
    };

    const renderCustomizedLabel = (props: CustomizedLabelProps) => {
        const { x, y, width, height, value } = props;

        if (x === undefined || y === undefined || width === undefined || height === undefined || value === undefined) {
            return null;
        }

        const xNum = typeof x === 'string' ? parseFloat(x) : x;
        const yNum = typeof y === 'string' ? parseFloat(y) : y;
        const widthNum = typeof width === 'string' ? parseFloat(width) : width;
        const heightNum = typeof height === 'string' ? parseFloat(height) : height;
        const valueNum = typeof value === 'string' ? parseFloat(value) : value;

        if (widthNum < 30) return null;

        return (
            <g>
                <text
                    x={xNum + widthNum - 5}
                    y={yNum + heightNum / 2}
                    fill="#fff"
                    textAnchor="end"
                    dominantBaseline="middle"
                    style={{
                        fontSize: '10px',
                        fontWeight: 'bold',
                        textShadow: '0px 0px 2px rgba(0,0,0,0.5)'
                    }}
                >
                    {formatNumber(valueNum)}
                </text>
            </g>
        );
    };

    return (
        <Card className="h-full">
            <CardHeader className="pb-0 pt-3 px-3">
                <CardTitle className="text-sm flex justify-between items-center">
                    <span>Métricas de alcance</span>
                    <span className="text-xs font-normal bg-emerald-100 dark:bg-emerald-900 text-emerald-800 dark:text-emerald-200 px-2 py-0.5 rounded-md">
                        {percentageProfileActivity}% de atividade
                    </span>
                </CardTitle>
                <CardDescription className="text-xs">{month} de {year}</CardDescription>
            </CardHeader>
            <CardContent className="px-2 pb-2 pt-1">
                <div className="h-[240px]">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                            data={chartData}
                            layout="vertical"
                            margin={{ top: 0, right: 10, left: 0, bottom: 0 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                            <XAxis
                                type="number"
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 10 }}
                                domain={[0, maxValue]}
                                tickFormatter={formatNumber}
                            />
                            <YAxis
                                type="category"
                                dataKey="name"
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 10 }}
                                width={80}
                            />
                            <Tooltip
                                formatter={(value) => [`${value.toLocaleString()}`, '']}
                                labelFormatter={() => ''}
                                contentStyle={{
                                    backgroundColor: 'hsl(var(--card))',
                                    borderColor: 'hsl(var(--border))',
                                    fontSize: '11px',
                                    borderRadius: '4px',
                                    padding: '4px 8px',
                                }}
                                content={({ active, payload }) => {
                                    if (active && payload && payload.length) {
                                        const data = payload[0].payload;
                                        return (
                                            <div className="bg-card border border-border p-2 rounded-md shadow-sm">
                                                <p className="text-xs">{data.fullName}</p>
                                                <p className="font-bold text-xs">{payload[0]?.value?.toLocaleString()}</p>
                                            </div>
                                        );
                                    }
                                    return null;
                                }}
                            />
                            <Bar
                                dataKey="value"
                                barSize={20}
                                radius={[0, 4, 4, 0]}
                                animationDuration={800}
                            >
                                {chartData.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={entry.color} />
                                ))}
                                <LabelList dataKey="value" content={renderCustomizedLabel} />
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>
                </div>

                <div className="mt-2 flex flex-wrap gap-1 justify-center">
                    {chartData.map((item, index) => (
                        <div key={index} className="flex items-center gap-1 px-2 py-0.5 rounded-full bg-muted/30 text-[10px]">
                            <div className="w-1.5 h-1.5 rounded-full" style={{ backgroundColor: item.color }}></div>
                            <span className="font-medium">{item.name}</span>
                            <span className="font-bold">{formatNumber(item.value)}</span>
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    );
};

export default DetailedReachMetrics;