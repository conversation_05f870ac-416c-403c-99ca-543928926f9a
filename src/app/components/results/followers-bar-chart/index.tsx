"use client";

import { useMemo, useState } from "react";
import { TrendingUp, TrendingDown } from "lucide-react";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, ReferenceLine, Cell, TooltipProps } from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";

interface FollowersBarChartProps {
    currentMonth: string;
    currentFollowers: number;
    previousMonth: string;
    previousFollowers: number | null;
}

export const FollowersBarChart = ({
    currentMonth,
    currentFollowers,
    previousMonth,
    previousFollowers
}: FollowersBarChartProps) => {
    const chartData = useMemo(() => {
        const difference = previousFollowers ? currentFollowers - previousFollowers : 0;
        const growthPercent = previousFollowers ? ((difference / previousFollowers) * 100).toFixed(1) : "N/A";

        return [
            {
                name: previousMonth,
                followers: previousFollowers || 0,
                color: "hsl(var(--chart-2))",
                growthValue: 0
            },
            {
                name: currentMonth,
                followers: currentFollowers,
                color: difference >= 0 ? "hsl(var(--chart-1))" : "hsl(var(--destructive))",
                growthValue: difference,
                growthPercent: growthPercent
            }
        ];
    }, [currentMonth, currentFollowers, previousMonth, previousFollowers]);

    const [activeMonth, setActiveMonth] = useState<"current" | "previous">("current");

    const activeIndex = useMemo(
        () => activeMonth === "current" ? 1 : 0,
        [activeMonth]
    );

    const { growthPercent, growthDirection } = useMemo(() => {
        if (previousFollowers) {
            const growth = currentFollowers - previousFollowers;
            const percent = (growth / previousFollowers) * 100;
            return {
                growthPercent: Math.abs(percent).toFixed(1),
                growthDirection: growth >= 0 ? "up" : "down"
            };
        }
        return {
            growthPercent: "N/A",
            growthDirection: "neutral"
        };
    }, [currentFollowers, previousFollowers]);

    const CustomTooltip = ({ active, payload }: TooltipProps<number, string>) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <div className="bg-card border border-border p-2 rounded-md shadow-sm">
                    <p className="font-medium">{data.name}</p>
                    <p className="text-sm">{data.followers.toLocaleString()} seguidores</p>
                    {data.growthValue !== 0 && (
                        <p className={`text-xs ${data.growthValue > 0 ? 'text-green-500' : 'text-red-500'}`}>
                            {data.growthValue > 0 ? '+' : ''}{data.growthValue.toLocaleString()} ({data.growthPercent}%)
                        </p>
                    )}
                </div>
            );
        }
        return null;
    };

    const selectedValue = chartData[activeIndex].followers;

    return (
        <Card>
            <CardHeader className="flex flex-row items-start space-y-0 pb-2">
                <div className="grid gap-1">
                    <CardTitle className="text-base">Total de seguidores</CardTitle>
                    <CardDescription>Crescimento acumulado</CardDescription>
                </div>
                <Select value={activeMonth} onValueChange={(value: "current" | "previous") => setActiveMonth(value)}>
                    <SelectTrigger
                        className="ml-auto h-7 w-[130px] rounded-lg pl-2.5"
                        aria-label="Selecionar mês"
                    >
                        <SelectValue placeholder="Selecionar mês" />
                    </SelectTrigger>
                    <SelectContent align="end" className="rounded-xl">
                        <SelectItem value="current" className="rounded-lg">
                            <div className="flex items-center gap-2 text-xs">
                                <span className="flex h-3 w-3 shrink-0 rounded-sm"
                                    style={{ backgroundColor: chartData[1].color }} />
                                {currentMonth}
                            </div>
                        </SelectItem>
                        <SelectItem value="previous" className="rounded-lg">
                            <div className="flex items-center gap-2 text-xs">
                                <span className="flex h-3 w-3 shrink-0 rounded-sm"
                                    style={{ backgroundColor: chartData[0].color }} />
                                {previousMonth}
                            </div>
                        </SelectItem>
                    </SelectContent>
                </Select>
            </CardHeader>
            <CardContent>
                <div className="mb-2 text-center">
                    <p className="text-3xl font-bold">{selectedValue.toLocaleString()}</p>
                    <p className="text-sm text-muted-foreground">seguidores</p>
                </div>
                <div className="h-[220px]">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                            data={chartData}
                            margin={{ top: 5, right: 10, left: 10, bottom: 15 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" vertical={false} />
                            <XAxis
                                dataKey="name"
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 12 }}
                            />
                            <YAxis
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 12 }}
                                tickFormatter={(value) => value.toLocaleString()}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Bar
                                dataKey="followers"
                                radius={[4, 4, 0, 0]}
                                barSize={50}
                                animationDuration={500}
                                fill="hsl(var(--chart-1))"
                                activeBar={{ stroke: "hsl(var(--border))", strokeWidth: 2 }}
                            >
                                {chartData.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={entry.color} />
                                ))}
                            </Bar>
                            <ReferenceLine
                                x={chartData[activeIndex].name}
                                stroke="hsl(var(--accent))"
                                strokeWidth={2}
                                strokeDasharray="3 3"
                                isFront={true}
                            />
                        </BarChart>
                    </ResponsiveContainer>
                </div>
                <div className="flex justify-center ">
                    {previousFollowers &&
                        <div className="text-center">
                            <Badge variant={growthDirection === "up" ? "success" : "destructive"} className="mb-1">
                                {growthDirection === "up" ? (
                                    <TrendingUp className="w-3 h-3 mr-1" />
                                ) : (
                                    <TrendingDown className="w-3 h-3 mr-1" />
                                )}
                                {growthPercent}%
                            </Badge>
                        </div>}
                </div>
            </CardContent>
        </Card>
    );
}