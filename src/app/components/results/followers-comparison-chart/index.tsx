"use client";

import { useMemo, useState } from "react";
import { TrendingUp, TrendingDown } from "lucide-react";
import { Label, Pie, PieChart, ResponsiveContainer, Sector } from "recharts";
import { PieSectorDataItem } from "recharts/types/polar/Pie";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";

interface FollowersComparisonProps {
    currentMonth: string;
    currentFollowers: number;
    previousMonth: string;
    previousFollowers: number | null;
}

export const FollowersComparisonChart = ({ currentMonth, currentFollowers, previousMonth, previousFollowers }: FollowersComparisonProps) => {
    const chartData = useMemo(() => [
        {
            month: "current",
            label: currentMonth,
            followers: currentFollowers,
            fill: "hsl(var(--chart-1))"
        },
        {
            month: "previous",
            label: previousMonth,
            followers: previousFollowers || Math.max(currentFollowers * 0.1, 1),
            fill: "hsl(var(--chart-2))"
        }
    ], [currentMonth, currentFollowers, previousMonth, previousFollowers]);

    const [activeMonth, setActiveMonth] = useState<"current" | "previous">("current");

    const activeIndex = useMemo(
        () => chartData.findIndex((item) => item.month === activeMonth),
        [activeMonth, chartData]
    );

    const { growthPercent, growthDirection } = useMemo(() => {
        if (previousFollowers) {
            const growth = currentFollowers - previousFollowers;
            const percent = (growth / previousFollowers) * 100;
            return {
                growthPercent: Math.abs(percent).toFixed(1),
                growthDirection: growth >= 0 ? "up" : "down"
            };
        }
        return {
            growthPercent: "N/A",
            growthDirection: "neutral"
        };
    }, [currentFollowers, previousFollowers]);

    return (
        <Card>
            <CardHeader className="flex flex-row items-start space-y-0 pb-2">
                <div className="grid gap-1">
                    <CardTitle className="text-base">Novos seguidores</CardTitle>
                    <CardDescription>Comparação mensal</CardDescription>
                </div>
                <Select value={activeMonth} onValueChange={(value: "current" | "previous") => setActiveMonth(value)}>
                    <SelectTrigger
                        className="ml-auto h-7 w-[130px] rounded-lg pl-2.5"
                        aria-label="Selecionar mês"
                    >
                        <SelectValue placeholder="Selecionar mês" />
                    </SelectTrigger>
                    <SelectContent align="end" className="rounded-xl">
                        <SelectItem value="current" className="rounded-lg">
                            <div className="flex items-center gap-2 text-xs">
                                <span className="flex h-3 w-3 shrink-0 rounded-sm bg-[hsl(var(--chart-1))]" />
                                {currentMonth}
                            </div>
                        </SelectItem>
                        <SelectItem value="previous" className="rounded-lg">
                            <div className="flex items-center gap-2 text-xs">
                                <span className="flex h-3 w-3 shrink-0 rounded-sm bg-[hsl(var(--chart-2))]" />
                                {previousMonth}
                            </div>
                        </SelectItem>
                    </SelectContent>
                </Select>
            </CardHeader>
            <CardContent className="flex justify-center pb-2">
                <div className="mx-auto aspect-square w-full h-[220px]">
                    <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                            <Pie
                                data={chartData}
                                dataKey="followers"
                                nameKey="month"
                                innerRadius={60}
                                outerRadius={80}
                                strokeWidth={5}
                                activeIndex={activeIndex}
                                activeShape={({
                                    outerRadius = 0,
                                    ...props
                                }: PieSectorDataItem) => (
                                    <g>
                                        <Sector {...props} outerRadius={outerRadius + 10} />
                                        <Sector
                                            {...props}
                                            outerRadius={outerRadius + 25}
                                            innerRadius={outerRadius + 12}
                                        />
                                    </g>
                                )}
                            >
                                <Label
                                    content={({ viewBox }) => {
                                        if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                                            return (
                                                <text
                                                    x={viewBox.cx}
                                                    y={viewBox.cy}
                                                    textAnchor="middle"
                                                    dominantBaseline="middle"
                                                >
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={viewBox.cy}
                                                        className="fill-foreground text-3xl font-bold"
                                                    >
                                                        {chartData[activeIndex].followers.toLocaleString()}
                                                    </tspan>
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={(viewBox.cy || 0) + 24}
                                                        className="fill-muted-foreground"
                                                    >
                                                        seguidores
                                                    </tspan>
                                                </text>
                                            )
                                        }
                                    }}
                                />
                            </Pie>
                        </PieChart>
                    </ResponsiveContainer>
                </div>
            </CardContent>
            <div className="flex justify-center pb-4">
                {previousFollowers ? (
                    <div className="text-center">
                        <Badge variant={growthDirection === "up" ? "success" : "destructive"} className="mb-1">
                            {growthDirection === "up" ? (
                                <TrendingUp className="w-3 h-3 mr-1" />
                            ) : (
                                <TrendingDown className="w-3 h-3 mr-1" />
                            )}
                            {growthPercent}%
                        </Badge>
                        <p className="text-xs text-muted-foreground">
                            {growthDirection === "up" ? "Aumento" : "Redução"} desde {previousMonth}
                        </p>
                    </div>
                ) : (
                    <p className="text-xs text-muted-foreground">
                        Não há dados do mês anterior
                    </p>
                )}
            </div>
        </Card>
    );
}