"use client";

interface FollowersDataSectionProps {
    followersLocation?: Record<string, number> | null;
    followersAgeRange?: Record<string, number> | null;
    followersGender?: Record<string, number> | null;
}

export default function FollowersDataSection({
    followersLocation,
    followersAgeRange,
    followersGender,
}: FollowersDataSectionProps) {
    return (
        <div className="mt-20">
            <h4 className="text-lg font-semibold mb-4 flex items-center">
                <span className="w-2 h-6 bg-orange-500 mr-2 rounded-sm"></span>
                Dados dos seguidores
            </h4>

            {followersLocation && Object.keys(followersLocation).length > 0 && (
                <div className="mb-6">
                    <h5 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">
                        Localização dos seguidores
                    </h5>
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
                        {Object.entries(followersLocation)
                            .sort(([,a], [,b]) => b - a)
                            .map(([city, percentage]) => (
                                <div key={city} className="rounded-xl border bg-card text-card-foreground shadow p-4">
                                    <h6 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                        {city}
                                    </h6>
                                    <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                                        {percentage.toFixed(1)}%
                                    </p>
                                </div>
                            ))}
                    </div>
                </div>
            )}

            {followersAgeRange && Object.keys(followersAgeRange).length > 0 && (
                <div className="mb-6">
                    <h5 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">
                        Faixa etária dos seguidores
                    </h5>
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
                        {Object.entries(followersAgeRange)
                            .sort(([,a], [,b]) => b - a)
                            .map(([ageRange, percentage]) => (
                                <div key={ageRange} className="rounded-xl border bg-card text-card-foreground shadow p-4">
                                    <h6 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                        {ageRange} anos
                                    </h6>
                                    <p className="text-lg font-bold text-green-600 dark:text-green-400">
                                        {percentage.toFixed(1)}%
                                    </p>
                                </div>
                            ))}
                    </div>
                </div>
            )}

            {followersGender && Object.keys(followersGender).length > 0 && (
                <div className="mb-6">
                    <h5 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">
                        Gênero dos seguidores
                    </h5>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                        {Object.entries(followersGender)
                            .sort(([,a], [,b]) => b - a)
                            .map(([gender, percentage]) => (
                                <div key={gender} className="rounded-xl border bg-card text-card-foreground shadow p-4">
                                    <h6 className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                                        {gender}
                                    </h6>
                                    <p className="text-lg font-bold text-purple-600 dark:text-purple-400">
                                        {percentage.toFixed(1)}%
                                    </p>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
        </div>
    );
}
