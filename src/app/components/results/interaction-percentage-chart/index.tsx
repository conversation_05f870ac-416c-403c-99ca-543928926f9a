"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from 'recharts';

interface InteractionPercentageChartProps {
    interactionsPercentageAds?: number | null;
    interactionsPercentageFollowers?: number | null;
    interactionsPercentageNonFollowers?: number | null;
    month: string;
    year: number;
}

export const InteractionPercentageChart = ({
    interactionsPercentageAds = 0,
    interactionsPercentageFollowers = 0,
    interactionsPercentageNonFollowers = 0,
    month,
    year
}: InteractionPercentageChartProps) => {
    const data = [
        {
            name: 'Anúncio<PERSON>',
            value: interactionsPercentageAds || 0,
            color: '#f59e0b'
        },
        {
            name: 'Seguido<PERSON>',
            value: interactionsPercentageFollowers || 0,
            color: '#10b981'
        },
        {
            name: 'Não seguidores',
            value: interactionsPercentageNonFollowers || 0,
            color: '#6366f1'
        }
    ].filter(item => item.value > 0);

    return (
        <Card>
            <CardHeader className="pb-2">
                <CardTitle className="text-base">Percentual de interações</CardTitle>
                <CardDescription>{month} de {year}</CardDescription>
            </CardHeader>
            <CardContent>
                {data.length > 0 ? (
                    <>
                        <div className="h-[220px]">
                            <ResponsiveContainer width="100%" height="100%">
                                <PieChart>
                                    <Pie
                                        data={data}
                                        cx="50%"
                                        cy="50%"
                                        labelLine={false}
                                        outerRadius={80}
                                        fill="#8884d8"
                                        dataKey="value"
                                    >
                                        {data.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={entry.color} />
                                        ))}
                                    </Pie>
                                </PieChart>
                            </ResponsiveContainer>
                        </div>

                        <div className="mt-3">
                            <div className="grid grid-cols-3 gap-1 text-center">
                                {data.map((item, index) => (
                                    <div key={index} className="p-2 bg-muted/30 rounded-md">
                                        <div 
                                            className="w-3 h-3 rounded-full mx-auto mb-1" 
                                            style={{ backgroundColor: item.color }}
                                        ></div>
                                        <p className="text-[10px] font-medium text-muted-foreground">{item.name}</p>
                                        <p className="text-[12px] font-bold">{item.value}%</p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </>
                ) : (
                    <div className="h-[220px] flex items-center justify-center">
                        <p className="text-muted-foreground">
                            Sem dados de percentual de interações
                        </p>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};

export default InteractionPercentageChart;
