"use client";

import { useMemo } from "react";
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Legend, LabelList } from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";

interface InteractionsByTypeContentProps {
    interactionsByTypeContent?: Record<string, number>;
    month: string;
    year: number;
}

interface CustomizedLabelProps {
    x?: string | number; 
    y?: string | number;
    width?: string | number;
    height?: string | number;
    value?: string | number
    index?: number;
    payload?: {
        name: string;
        followers: number;
        nonFollowers: number;
    };
}

export const InteractionsByTypeContent = ({
    interactionsByTypeContent = {},
    month,
    year
}: InteractionsByTypeContentProps) => {
    const chartData = useMemo(() => {
        type ContentType = 'feed' | 'reels' | 'stories';
        type AudienceType = 'followers' | 'nonFollowers';
        
        const keyMapping: Record<string, { type: ContentType; audience: AudienceType }> = {
            "feedFollowers": { type: "feed", audience: "followers" },
            "feedNonFollowers": { type: "feed", audience: "nonFollowers" },
            "reelsFollowers": { type: "reels", audience: "followers" },
            "reelsNonFollowers": { type: "reels", audience: "nonFollowers" },
            "storiesFollowers": { type: "stories", audience: "followers" },
            "storiesNonFollowers": { type: "stories", audience: "nonFollowers" }
        };

        const groupedData = {
            feed: { name: "Publicações", followers: 0, nonFollowers: 0 },
            reels: { name: "Reels", followers: 0, nonFollowers: 0 },
            stories: { name: "Stories", followers: 0, nonFollowers: 0 }
        };

        Object.entries(interactionsByTypeContent || {}).forEach(([key, value]) => {
            const mapping = keyMapping[key as keyof typeof keyMapping];
            if (mapping) {
                groupedData[mapping.type][mapping.audience] = value || 0;
            }
        });

        return Object.values(groupedData)
            .filter(item => item.followers > 0 || item.nonFollowers > 0)
            .sort((a, b) => {
                const totalA = a.followers + a.nonFollowers;
                const totalB = b.followers + b.nonFollowers;
                return totalB - totalA;
            });
    }, [interactionsByTypeContent]);

    const formatNumber = (value: number) => {
        if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
        if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
        return value.toString();
    };

    const renderCustomizedFollowersLabel = (props: CustomizedLabelProps) => {
        const { x = 0, y = 0, width = 0, height = 0, value = 0 } = props;
        const numX = typeof x === 'string' ? parseFloat(x) : x;
        const numY = typeof y === 'string' ? parseFloat(y) : y;
        const numWidth = typeof width === 'string' ? parseFloat(width) : width;
        const numHeight = typeof height === 'string' ? parseFloat(height) : height;
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        
        if (numValue <= 0) return null;
        
        return (
            <text 
                x={numX + numWidth - 5} 
                y={numY + numHeight / 2} 
                fill="white"
                textAnchor="end" 
                dominantBaseline="middle"
                style={{ 
                    fontSize: '10px',
                    fontWeight: 'bold',
                    textShadow: '0px 0px 2px rgba(0,0,0,0.5)'
                }}
            >
                {formatNumber(numValue)}
            </text>
        );
    };

    const renderCustomizedNonFollowersLabel = (props: CustomizedLabelProps) => {
        const { x = 0, y = 0, width = 0, height = 0, value = 0 } = props;
        const numX = typeof x === 'string' ? parseFloat(x) : x;
        const numY = typeof y === 'string' ? parseFloat(y) : y;
        const numWidth = typeof width === 'string' ? parseFloat(width) : width;
        const numHeight = typeof height === 'string' ? parseFloat(height) : height;
        const numValue = typeof value === 'string' ? parseFloat(value) : value;
        
        if (numValue <= 0) return null;
        
        return (
            <text 
                x={numX + numWidth / 2} 
                y={numY + numHeight / 2} 
                fill="white"
                textAnchor="middle" 
                dominantBaseline="middle"
                style={{ 
                    fontSize: '10px',
                    fontWeight: 'bold',
                    textShadow: '0px 0px 2px rgba(0,0,0,0.5)'
                }}
            >
                {formatNumber(numValue)}
            </text>
        );
    };

    if (!chartData.length) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="text-base">Interações por tipo de conteúdo</CardTitle>
                    <CardDescription>{month} de {year}</CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col items-center justify-center h-[250px]">
                    <p className="text-muted-foreground text-center">
                        Não há dados de interações por tipo disponíveis
                    </p>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader className="pb-0 pt-3 px-3">
                <CardTitle className="text-sm">Interações por tipo de conteúdo</CardTitle>
                <CardDescription className="text-xs">{month} de {year}</CardDescription>
            </CardHeader>
            <CardContent className="px-2 pb-2 pt-1">
                <div className="h-[220px]">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                            data={chartData}
                            layout="vertical"
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                            <XAxis 
                                type="number" 
                                tickFormatter={formatNumber}
                                tick={{ fontSize: 10 }}
                            />
                            <YAxis 
                                type="category" 
                                dataKey="name" 
                                tick={{ fontSize: 10 }}
                                width={80}
                            />
                            <Tooltip
                                content={({ active, payload }) => {
                                    if (active && payload && payload.length) {
                                        const data = payload[0].payload;
                                        return (
                                            <div className="bg-card border border-border p-2 rounded-md shadow-sm">
                                                <p className="text-xs font-medium">{data.name}</p>
                                                <div className="mt-1">
                                                    <p className="text-xs">
                                                        <span style={{ color: 'hsl(var(--chart-1))' }}>●</span> Seguidores: <span className="font-bold">{data.followers.toLocaleString()}</span>
                                                    </p>
                                                    <p className="text-xs">
                                                        <span style={{ color: 'hsl(var(--chart-2))' }}>●</span> Não seguidores: <span className="font-bold">{data.nonFollowers.toLocaleString()}</span>
                                                    </p>
                                                    <p className="text-xs font-bold mt-1 border-t pt-1">
                                                        Total: {(data.followers + data.nonFollowers).toLocaleString()}
                                                    </p>
                                                </div>
                                            </div>
                                        );
                                    }
                                    return null;
                                }}
                            />
                            <Legend 
                                formatter={(value) => value === 'followers' ? 'Seguidores' : 'Não seguidores'} 
                                wrapperStyle={{ fontSize: '10px' }}
                            />
                            <Bar 
                                dataKey="followers" 
                                name="followers" 
                                fill="hsl(var(--chart-1))" 
                                stackId="a" 
                                barSize={20}
                                radius={[4, 4, 0, 0]}
                            >
                                <LabelList dataKey="followers" content={renderCustomizedFollowersLabel} />
                            </Bar>
                            <Bar 
                                dataKey="nonFollowers" 
                                name="nonFollowers" 
                                fill="hsl(var(--chart-2))" 
                                stackId="a" 
                                barSize={20}
                                radius={[0, 0, 4, 4]}
                            >
                                <LabelList dataKey="nonFollowers" content={renderCustomizedNonFollowersLabel} />
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>
                </div>
                
                <div className="mt-2 grid grid-cols-3 gap-1">
                    {chartData.map((item, index) => (
                        <div key={index} className="bg-muted/30 rounded-md p-1 text-center">
                            <p className="text-[10px] font-medium truncate">{item.name}</p>
                            <p className="text-[10px] font-bold">{formatNumber(item.followers + item.nonFollowers)}</p>
                        </div>
                    ))}
                </div>
            </CardContent>
        </Card>
    );
};

export default InteractionsByTypeContent;