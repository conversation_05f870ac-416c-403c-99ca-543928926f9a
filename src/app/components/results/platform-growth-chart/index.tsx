"use client";

import { useState, useMemo } from "react";
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON>lt<PERSON>, ResponsiveContainer, Cell } from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { TrendingUp, TrendingDown } from "lucide-react";
import { Badge } from "@/app/components/ui/badge";

interface PlatformGrowthChartProps {
    currentMonth: string;
    previousMonth?: string;
    month: string;
    year: number;
    color?: string;
    secondaryColor?: string;
    currentFollowers?: number;
    totalFollowers?: number;
    unfollowCount?: number;
    previousTotalFollowers?: number;
}

interface TooltipPayloadItem {
    payload: {
        isPrevious: boolean;
        name: string;
        followers: number;
        color: string;
        growthPercent?: number;
    };
    dataKey?: string;
    value?: number;
    name?: string;
    color?: string;
}

interface CustomTooltipProps {
    active?: boolean;
    payload?: TooltipPayloadItem[];
    label?: string;
}

export const PlatformGrowthChart = ({
    currentMonth,
    previousMonth,
    month,
    year,
    color = "hsl(var(--chart-1))",
    secondaryColor = "hsl(var(--chart-3))",
    currentFollowers = 0,
    totalFollowers = 0,
    unfollowCount = 0,
    previousTotalFollowers = 0,
}: PlatformGrowthChartProps) => {
    const [activeBar, setActiveBar] = useState<number | null>(null);

    const growthPercentage = useMemo(() => {
        if (!previousTotalFollowers || previousTotalFollowers === 0) return 0;
        return ((totalFollowers - previousTotalFollowers) / previousTotalFollowers) * 100;
    }, [totalFollowers, previousTotalFollowers]);
    
    const formattedGrowthPercentage = parseFloat(growthPercentage.toFixed(1));

    const growthAbsolute = totalFollowers - previousTotalFollowers;
    
    const chartData = useMemo(() => {
        return [
            {
                name: previousMonth,
                followers: previousTotalFollowers,
                color: secondaryColor,
                growthPercent: 0,
                isPrevious: true
            },
            {
                name: currentMonth,
                followers: totalFollowers,
                color: growthPercentage >= 0 ? color : "hsl(var(--destructive))",
                growthPercent: formattedGrowthPercentage,
                isPrevious: false
            }
        ];
    }, [previousMonth, previousTotalFollowers, secondaryColor, currentMonth, totalFollowers, growthPercentage, color, formattedGrowthPercentage]);

    const yDomain = useMemo(() => {
        const max = Math.max(totalFollowers, previousTotalFollowers);
        const min = Math.min(totalFollowers, previousTotalFollowers);
        const minValue = min > max * 0.9 ? min * 0.9 : 0;
        const maxValue = max * 1.1;
        return [minValue, maxValue];
    }, [totalFollowers, previousTotalFollowers]);

    const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;
            return (
                <div className="bg-card border border-border p-3 rounded-md shadow-sm">
                    <p className="font-medium">{data.name}</p>
                    <p className="font-bold">{data.followers.toLocaleString()} seguidores</p>
                    {!data.isPrevious && previousTotalFollowers >= 0 && (
                        <>
                            <div className={`text-sm mt-1 ${formattedGrowthPercentage >= 0 ? "text-green-500" : "text-red-500"}`}>
                                {formattedGrowthPercentage >= 0 ? "+" : ""}{formattedGrowthPercentage}% 
                            </div>
                            <div className={`text-xs ${formattedGrowthPercentage >= 0 ? "text-green-500" : "text-red-500"}`}>
                                {growthAbsolute > 0 ? "+" : ""}{growthAbsolute.toLocaleString()} seguidores
                            </div>
                        </>
                    )}
                    {data.isPrevious && (
                        <p className="text-xs text-muted-foreground">Mês base para comparação</p>
                    )}
                </div>
            );
        }
        return null;
    };

    const handleBarClick = (index: number) => {
        setActiveBar(index === activeBar ? null : index);
    };

    return (
        <Card className="min-h-[450px]">
            <CardHeader className="pb-2 pt-3">
                <CardTitle className="text-base">Total de seguidores</CardTitle>
                <CardDescription>Evolução mensal - {month} de {year}</CardDescription>
            </CardHeader>
            <CardContent className="pb-4">
                <div className="mb-3 text-center">
                    <div className="flex justify-center items-center gap-1">
                        <p className="text-3xl font-bold">
                            {totalFollowers.toLocaleString()}
                        </p>
                        {previousTotalFollowers > 0 && (
                            <Badge variant={formattedGrowthPercentage >= 0 ? "success" : "destructive"} className="ml-2">
                                {formattedGrowthPercentage >= 0 ? (
                                    <TrendingUp className="w-3 h-3 mr-1" />
                                ) : (
                                    <TrendingDown className="w-3 h-3 mr-1" />
                                )}
                                {Math.abs(formattedGrowthPercentage).toFixed(1)}%
                            </Badge>
                        )}
                    </div>
                    
                    <div className="text-sm text-muted-foreground mt-1">
                        <p>total de seguidores em {currentMonth}</p>
                        
                        {previousTotalFollowers >= 0 && (
                            <div className="mt-1">
                                {growthAbsolute !== 0 && (
                                    <p className={`text-sm font-medium ${formattedGrowthPercentage >= 0 ? "text-green-600" : "text-red-600"}`}>
                                        {growthAbsolute > 0 ? "+" : ""}{growthAbsolute.toLocaleString()} seguidores este mês
                                    </p>
                                )}
                                {growthAbsolute === 0 && (
                                    <p className="text-sm text-muted-foreground font-medium">
                                        Sem alteração no total de seguidores
                                    </p>
                                )}
                            </div>
                        )}
                        
                        {currentFollowers > 0 && (
                            <div className="text-sm text-muted-foreground mt-1">
                                Novos este mês: {currentFollowers.toLocaleString()} seguidores
                                {unfollowCount > 0 && (
                                    <span className="block text-xs">
                                        {unfollowCount.toLocaleString()} deixaram de seguir
                                    </span>
                                )}
                            </div>
                        )}
                    </div>
                    
                    <div className="flex justify-center gap-2 items-center text-xs pt-3 border-t border-border/50 mt-3">
                        <div className="flex flex-col items-center">
                            <span className="text-muted-foreground">{previousMonth}</span>
                            <span className="font-medium">{previousTotalFollowers.toLocaleString()}</span>
                        </div>
                        <span className="text-muted-foreground">
                            {growthAbsolute >= 0 ? (
                                <TrendingUp className="w-4 h-4" />
                            ) : (
                                <TrendingDown className="w-4 h-4" />
                            )}
                        </span>
                        <div className="flex flex-col items-center">
                            <span className="text-muted-foreground">{currentMonth}</span>
                            <span className="font-medium">{totalFollowers.toLocaleString()}</span>
                        </div>
                    </div>
                </div>
                <div className="h-[200px]">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                            data={chartData}
                            margin={{ top: 5, right: 30, left: 0, bottom: 5 }}
                            barGap={20}
                        >
                            <CartesianGrid strokeDasharray="3 3" vertical={false} />
                            <XAxis
                                dataKey="name"
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 12 }}
                            />
                            <YAxis
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 12 }}
                                tickFormatter={(value) => value.toLocaleString()}
                                domain={yDomain}
                                allowDecimals={false}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Bar
                                dataKey="followers"
                                radius={[4, 4, 0, 0]}
                                barSize={50}
                                onClick={(_, index) => handleBarClick(index)}
                                animationDuration={800}
                                animationEasing="ease-out"
                            >
                                {chartData.map((entry, index) => (
                                    <Cell
                                        key={`cell-${index}`}
                                        fill={entry.color}
                                        strokeWidth={2}
                                        style={entry.isPrevious ? {
                                            fillOpacity: 0.5,
                                            stroke: entry.color,
                                        } : {}}
                                        fillOpacity={activeBar === index ? 1 : 0.8}
                                    />
                                ))}
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>
                </div>
            </CardContent>
        </Card>
    );
};