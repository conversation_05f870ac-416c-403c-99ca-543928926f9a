"use client";

import { useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import {
    <PERSON><PERSON>hart,
    Bar,
    XAxis,
    YAxis,
    CartesianGrid,
    ResponsiveContainer,
    LabelList
} from "recharts";

interface ReachByPostTypeProps {
    reachByPostType?: Record<string, number>;
    month: string;
    year: number;
}

export const ReachByPostType = ({
    reachByPostType = {},
    month,
    year
}: ReachByPostTypeProps) => {
    const chartData = useMemo(() => {
        return Object.entries(reachByPostType || {}).map(([type, value]) => ({
            type,
            value,
        })).sort((a, b) => b.value - a.value);
    }, [reachByPostType]);

    const yAxisWidth = 35;
    
    const barSize = useMemo(() => {
        const height = 200
        const calculatedSize = Math.floor(height / Math.max(chartData.length, 1));
        return Math.max(30, Math.min(40, calculatedSize));
    }, [chartData.length]);

    const maxValue = useMemo(() => {
        if (chartData.length === 0) return 1000;
        return Math.max(...chartData.map(item => item.value)) * 1.1;
    }, [chartData]);

    const formatNumber = (value: number) => {
        if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
        if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
        return value.toString();
    };

    if (chartData.length === 0) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="text-base">Alcance por tipo de publicação</CardTitle>
                    <CardDescription>{month} de {year}</CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col items-center justify-center h-[250px]">
                    <p className="text-muted-foreground text-center">
                        Não há dados de alcance por tipo de publicação disponíveis
                    </p>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader className="pb-2">
                <CardTitle className="text-base">Alcance por tipo de publicação</CardTitle>
                <CardDescription>{month} de {year}</CardDescription>
            </CardHeader>
            <CardContent className="px-0 py-0">
                <div className="h-[220px] w-full px-2">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                            data={chartData}
                            layout="vertical"
                            margin={{ top: 5, right: 45, left: yAxisWidth, bottom: 5 }}
                            barCategoryGap={0}
                            barGap={0}
                        >
                            <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                            <XAxis
                                type="number"
                                domain={[0, maxValue]}
                                tickFormatter={formatNumber}
                                axisLine={false}
                                tickLine={false}
                                tickMargin={8}
                            />
                            <YAxis
                                dataKey="type"
                                type="category"
                                tick={{ fontSize: 12 }}
                                width={yAxisWidth}
                                axisLine={false}
                                tickLine={false}
                                padding={{ top: 0, bottom: 0 }}
                            />
                            <Bar
                                dataKey="value"
                                fill="hsl(var(--chart-3))"
                                radius={[0, 4, 4, 0]}
                                barSize={barSize}
                                minPointSize={5}
                                animationDuration={500}
                            >
                                <LabelList
                                    dataKey="value"
                                    position="right"
                                    formatter={formatNumber}
                                    style={{
                                        fill: 'hsl(var(--foreground))',
                                        fontSize: '12px',
                                        fontWeight: 'bold'
                                    }}
                                    offset={5}
                                />
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>
                </div>
            </CardContent>
        </Card>
    );
};