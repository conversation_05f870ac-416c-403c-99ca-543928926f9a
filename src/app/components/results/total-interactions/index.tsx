"use client";

import { useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";

interface TotalInteractionsProps {
    totalInteractions?: number;
    interactionTotal?: number;
    month: string;
    year: number;
    dailyData?: Array<{ day: string; value: number }>;
}

export const TotalInteractions = ({ 
    totalInteractions = 0, 
    interactionTotal = 0, 
    month, 
    year, 
    dailyData 
}: TotalInteractionsProps) => {
    const actualInteractions = interactionTotal || totalInteractions;

    const chartData = useMemo(() => {
        if (dailyData && dailyData.length > 0) {
            return dailyData;
        }

        return [{ day: "Total", value: actualInteractions }];
    }, [actualInteractions, dailyData]);

    const maxValue = useMemo(() => {
        const max = Math.max(...chartData.map(item => item.value));
        return Math.ceil(max * 1.1); 
    }, [chartData]);

    return (
        <Card>
            <CardHeader className="pb-2">
                <CardTitle className="text-base flex justify-between items-center">
                    <span>Total de Interações</span>
                    <span className="text-lg font-bold">{actualInteractions.toLocaleString()}</span>
                </CardTitle>
                <CardDescription>{month} de {year}</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="h-[180px] w-full">
                    <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                            data={chartData}
                            margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="hsl(var(--border))" />
                            <XAxis
                                dataKey="day"
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 12 }}
                                tickMargin={8}
                            />
                            <YAxis
                                domain={[0, maxValue]}
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 12 }}
                                tickMargin={8}
                                width={40}
                                tickFormatter={(value) => value.toLocaleString()}
                            />
                            <Tooltip
                                contentStyle={{
                                    backgroundColor: 'hsl(var(--card))',
                                    borderColor: 'hsl(var(--border))',
                                    borderRadius: '0.5rem',
                                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                                }}
                                labelStyle={{ fontWeight: 'bold', marginBottom: '4px' }}
                                formatter={(value: number) => [value.toLocaleString()]}
                            />
                            <Line
                                type="monotone"
                                dataKey="value"
                                stroke="hsl(var(--chart-1))"
                                strokeWidth={3}
                                dot={{ fill: 'hsl(var(--chart-1))', r: 4 }}
                                activeDot={{ r: 6, fill: 'hsl(var(--chart-1))', stroke: 'hsl(var(--background))' }}
                                animationDuration={1500}
                            />
                        </LineChart>
                    </ResponsiveContainer>
                </div>
            </CardContent>
        </Card>
    );
};