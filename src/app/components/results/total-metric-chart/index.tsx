"use client";

import { useMemo } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, Cell, Label } from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";

interface TotalMetricChartProps {
    value: number;
    title: string;
    labelText: string;
    month: string;
    year: number;
    colorIndex?: number;
}

export const TotalMetricChart = ({
    value,
    title,
    labelText,
    month,
    year,
    colorIndex = 1
}: TotalMetricChartProps) => {
    const chartData = useMemo(() => [
        { name: title, value: value, fill: `hsl(var(--chart-${colorIndex}))` },
        { name: "Placeholder", value: Math.max(value * 0.1, 1), fill: `hsl(var(--chart-${colorIndex + 3}))` }
    ], [value, title, colorIndex]);

    return (
        <Card>
            <CardHeader className="pb-2">
                <CardTitle className="text-base">{title}</CardTitle>
                <CardDescription>{month} de {year}</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
                <div className="mx-auto aspect-square w-full max-w-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                            <Pie
                                data={chartData}
                                dataKey="value"
                                nameKey="name"
                                innerRadius={60}
                                outerRadius={80}
                                strokeWidth={5}
                                startAngle={90}
                                endAngle={-270}
                                paddingAngle={2}
                            >
                                {chartData.map((entry, index) => (
                                    <Cell
                                        key={`cell-${index}`}
                                        fill={entry.fill}
                                        stroke="transparent"
                                    />
                                ))}
                                <Label
                                    content={({ viewBox }) => {
                                        if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                                            return (
                                                <text
                                                    x={viewBox.cx}
                                                    y={viewBox.cy}
                                                    textAnchor="middle"
                                                    dominantBaseline="middle"
                                                >
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={viewBox.cy}
                                                        className="fill-foreground text-3xl font-bold"
                                                    >
                                                        {value.toLocaleString()}
                                                    </tspan>
                                                    <tspan
                                                        x={viewBox.cx}
                                                        y={(viewBox.cy || 0) + 24}
                                                        className="fill-muted-foreground text-sm"
                                                    >
                                                        {labelText}
                                                    </tspan>
                                                </text>
                                            );
                                        }
                                        return null;
                                    }}
                                />
                            </Pie>
                        </PieChart>
                    </ResponsiveContainer>
                </div>
            </CardContent>
        </Card>
    );
};