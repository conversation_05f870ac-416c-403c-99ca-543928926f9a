"use client";

import { useMemo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';

interface VideoMetricsChartProps {
    videoLikes?: number | null;
    videoComments?: number | null;
    videoSaves?: number | null;
    videoShares?: number | null;
    videoInteractionRate?: number | null;
    month: string;
    year: number;
}

export const VideoMetricsChart = ({
    videoLikes = 0,
    videoComments = 0,
    videoSaves = 0,
    videoShares = 0,
    videoInteractionRate = 0,
    month,
    year
}: VideoMetricsChartProps) => {
    const chartData = useMemo(() => {
        return [
            { name: "Curtidas", value: videoLikes || 0 },
            { name: "Comentários", value: videoComments || 0 },
            { name: "Salvamentos", value: videoSaves || 0 },
            { name: "Compart.", value: videoShares || 0 }
        ];
    }, [videoLikes, videoComments, videoSaves, videoShares]);

    const formatNumber = (value: number) => {
        if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`;
        if (value >= 1000) return `${(value / 1000).toFixed(1)}K`;
        return value.toString();
    };

    const colors = [
        "hsl(var(--chart-1))",
        "hsl(var(--chart-2))",
        "hsl(var(--chart-3))",
        "hsl(var(--chart-4))" 
    ];

    return (
        <Card>
            <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                    <div>
                        <CardTitle className="text-base">Métricas de vídeos</CardTitle>
                        <CardDescription>{month} de {year}</CardDescription>
                    </div>
                    <div className="bg-muted/50 rounded-md px-2 py-1 text-xs">
                        Taxa de Interação: <span className="font-bold">{(videoInteractionRate || 0).toFixed(2)}%</span>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                <div className="h-[220px]">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                            data={chartData}
                            margin={{ top: 5, right: 30, left: 20, bottom: 25 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="hsl(var(--border))" />
                            <XAxis
                                dataKey="name"
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 10 }}
                                angle={-45}
                                textAnchor="end"
                            />
                            <YAxis
                                axisLine={false}
                                tickLine={false}
                                tick={{ fontSize: 12 }}
                                tickMargin={8}
                                tickFormatter={formatNumber}
                            />
                            <Tooltip
                                formatter={(value) => [formatNumber(value as number), "Quantidade"]}
                                separator=": "
                                contentStyle={{
                                    backgroundColor: 'hsl(var(--popover))',
                                    borderWidth: '1px',
                                    borderStyle: 'solid',
                                    borderColor: 'hsl(var(--border))',
                                    borderRadius: '0.375rem',
                                    boxShadow: '0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06)',
                                    color: 'hsl(var(--popover-foreground))',
                                    fontWeight: '500',
                                    padding: '6px 8px'
                                }}
                                labelStyle={{
                                    color: 'hsl(var(--popover-foreground))',
                                    fontSize: '12px',
                                    fontWeight: '600',
                                    marginBottom: '2px'
                                }}
                                itemStyle={{
                                    color: 'hsl(var(--popover-foreground))',
                                    fontSize: '12px',
                                    fontWeight: '500'
                                }}
                            />
                            <Bar dataKey="value" radius={[4, 4, 0, 0]}>
                                {chartData.map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                                ))}
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>
                </div>

                <div className="mt-3">
                    <div className="grid grid-cols-4 gap-1 text-center">
                        {chartData.map((item, index) => (
                            <div key={index} className="p-1 bg-muted/30 rounded-md">
                                <p className="text-[10px] font-medium">{item.name}</p>
                                <p className="text-[12px] font-bold">{formatNumber(item.value)}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default VideoMetricsChart;
