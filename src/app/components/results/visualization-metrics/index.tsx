"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Users } from 'lucide-react';

interface VisualizationMetricsProps {
    percentageAds?: number;
    percentageFollowers?: number;
    percentageNonFollowers?: number;
    accountsReached?: number;
    percentageAccountsReached?: number;
    profileActivity?: number;
    percentageProfileActivity?: number;
    profileVisits?: number;
    percentageProfileVisits?: number;
    externalLinkTaps?: number;
    percentageExternalLinkTaps?: number;
    businessAddressTaps?: number;
    percentageBusinessAddressTaps?: number;
    month: string;
    year: number;
}

export const VisualizationMetrics = ({
    percentageAds = 0,
    percentageFollowers = 0,
    percentageNonFollowers = 0,
    accountsReached = 0,
    percentageAccountsReached = 0,
    profileActivity = 0,
    percentageProfileActivity = 0,
    profileVisits = 0,
    percentageProfileVisits = 0,
    externalLinkTaps = 0,
    percentageExternalLinkTaps = 0,
    businessAddressTaps = 0,
    percentageBusinessAddressTaps = 0,
    month,
    year
}: VisualizationMetricsProps) => {

    return (
        <div className="grid grid-cols-1 gap-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-base">Distribuição de visualizações</CardTitle>
                        <CardDescription>Por tipo de audiência - {month} de {year}</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                                <span className="text-sm font-medium">Seguidores</span>
                                <span className="text-lg font-bold text-primary">
                                    {percentageFollowers || 0}%
                                </span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                                <span className="text-sm font-medium">Não seguidores</span>
                                <span className="text-lg font-bold text-primary">
                                    {percentageNonFollowers || 0}%
                                </span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                                <span className="text-sm font-medium">Anúncios</span>
                                <span className="text-lg font-bold text-primary">
                                    {percentageAds || 0}%
                                </span>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="pb-4 relative z-10">
                        <div className="flex items-center gap-2">
                            <CardTitle>
                                Alcance de contas
                            </CardTitle>
                        </div>
                        <CardDescription className="text-sm text-gray-600 mt-1">
                            Percentual de contas alcançadas através do conteúdo
                        </CardDescription>
                    </CardHeader>

                    <CardContent className="relative z-10">
                        <div className="space-y-6">
                            <div className="text-center">
                                <div className="text-3xl font-bold mb-2">
                                    {percentageAccountsReached || 0}%
                                </div>
                                <p className="text-sm text-gray-600 max-w-xs mx-auto">
                                    das contas foram alcançadas através do conteúdo
                                </p>
                            </div>

                            <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                                <div className="flex items-center gap-2">
                                    <Users className="h-4 w-4 text-blue-500" />
                                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                                        {(accountsReached || 0).toLocaleString()} contas
                                    </span>
                                </div>
                                <div className="text-right">
                                    <div className="text-xs text-gray-500">Performance</div>
                                    <div className={`text-sm font-semibold ${percentageAccountsReached >= 75 ? 'text-green-600' : percentageAccountsReached >= 50 ? 'text-green-500' : percentageAccountsReached >= 25 ? 'text-yellow-500' : 'text-red-500'}`}>
                                        {percentageAccountsReached >= 75 ? 'Excelente alcance' :
                                            percentageAccountsReached >= 50 ? 'Bom alcance' :
                                                percentageAccountsReached >= 25 ? 'Alcance moderado' :
                                                    'Alcance baixo'}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <div className="grid grid-cols-1 gap-4">
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-base">Atividade no perfil</CardTitle>
                        <CardDescription>Interações com o perfil - {month} de {year}</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                                <span className="text-sm font-medium">Total de atividade no perfil</span>
                                <div className="text-right">
                                    <div className="text-lg font-bold text-primary">
                                        {profileActivity || 0}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                        ({percentageProfileActivity || 0}%)
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                                <span className="text-sm font-medium">Visitas ao perfil</span>
                                <div className="text-right">
                                    <div className="text-lg font-bold text-primary">
                                        {profileVisits || 0}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                        ({percentageProfileVisits || 0}%)
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                                <span className="text-sm font-medium">Toques em links externos</span>
                                <div className="text-right">
                                    <div className="text-lg font-bold text-primary">
                                        {externalLinkTaps || 0}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                        ({percentageExternalLinkTaps || 0}%)
                                    </div>
                                </div>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                                <span className="text-sm font-medium">Toques no endereço comercial</span>
                                <div className="text-right">
                                    <div className="text-lg font-bold text-primary">
                                        {businessAddressTaps || 0}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                        ({percentageBusinessAddressTaps || 0}%)
                                    </div>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};
