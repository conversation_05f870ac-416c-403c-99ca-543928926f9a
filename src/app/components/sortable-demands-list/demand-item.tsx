import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar";
import { Badge } from "@/app/components/ui/badge";
import { GripVertical } from "lucide-react";
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface Client {
  id: string;
  name: string;
}

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  role?: string;
}

interface Content {
  id: string;
  type: 'content' | 'general';
  activityDate: string;
  title?: string;
  description?: string | null;
  status?: string | null;
  priority?: string;
  contentType?: string;
  client: Client;
  assignedTo: User | null;
  isLooseClient?: boolean;
  looseClientName?: string | null;
  position?: number;
}

interface DemandItemProps {
  demand: Content;
  position: number;
}

export function DemandItem({ demand, position }: DemandItemProps) {
  const getPriorityColor = (priority: string | null | undefined) => {
    if (!priority) return "bg-gray-200 text-gray-800";

    switch (priority.toLowerCase()) {
      case "baixa":
        return "bg-green-100 text-green-800";
      case "normal":
        return "bg-blue-100 text-blue-800";
      case "alta":
        return "bg-orange-100 text-orange-800";
      case "urgente":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string | null | undefined) => {
    if (!status) return "bg-gray-200 text-gray-800";

    switch (status.toLowerCase()) {
      case "pendente":
        return "bg-yellow-100 text-yellow-800";
      case "em andamento":
        return "bg-blue-100 text-blue-800";
      case "concluído":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPositionBadgeColor = (position: number) => {
    if (position <= 5) {
      return "bg-red-100 text-red-800";
    } else if (position <= 10) {
      return "bg-orange-100 text-orange-800";
    } else if (position <= 15) {
      return "bg-blue-100 text-blue-800";
    } else {
      return "bg-green-100 text-green-800";
    }
  };

  return (
    <div className="border rounded-md p-3 bg-card shadow-sm flex items-center gap-3 cursor-grab active:cursor-grabbing hover:bg-accent/50 transition-colors">
      <div className="flex-shrink-0 flex items-center justify-center w-8 h-full">
        <div className="flex flex-col items-center">
          <GripVertical className="h-5 w-5 text-muted-foreground" />
          <Badge variant="outline" className={`${getPositionBadgeColor(position)} mt-1`}>
            {position}
          </Badge>
        </div>
      </div>

      <div className="flex-grow">
        <div className="flex items-center gap-2 mb-1">
          <Badge variant="outline" className={getPriorityColor(demand.priority)}>
            {demand.priority || "Normal"}
          </Badge>

          <Badge
            variant="outline"
            className={getStatusColor(demand.status)}
          >
            <span className="truncate">
              {demand.status || "Pendente"}
            </span>
          </Badge>

          <Badge variant="outline" className="text-xs">
            {demand.id.substring(0, 8).toUpperCase()}
          </Badge>
        </div>

        <div className="font-medium truncate" title={demand.title}>
          {demand.title || demand.contentType}
        </div>

        <div className="flex items-center justify-between mt-1">
          <div className="text-sm text-muted-foreground truncate" title={demand.client.name}>
            {demand.client.name}
            {demand.isLooseClient && (
              <span className="ml-1 text-xs italic">
                ⦁ Não fixo
              </span>
            )}
            <div>
              <span className="text-sm text-muted-foreground">
                {format(new Date(demand.activityDate), "dd 'de' MMMM, yyyy", { locale: ptBR })}
              </span>
            </div>
          </div>
          
          <div className="flex-shrink-0">
            {demand.assignedTo ? (
              <div className="flex items-center gap-1">
                <Avatar className="h-6 w-6 border-2 border-zinc-200">
                  <AvatarImage
                    src={demand.assignedTo.image || undefined}
                    alt={
                      demand.assignedTo.name ||
                      demand.assignedTo.email
                    }
                  />
                  <AvatarFallback className="text-xs">
                    {demand.assignedTo.name?.[0] ||
                      demand.assignedTo.email[0]}
                  </AvatarFallback>
                </Avatar>
                <span className="text-xs">
                  {demand.assignedTo.name ||
                    demand.assignedTo.email}
                </span>
              </div>
            ) : (
              <span className="text-sm text-muted-foreground">
                Não atribuído
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
