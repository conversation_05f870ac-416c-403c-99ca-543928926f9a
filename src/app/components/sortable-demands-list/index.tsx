import { useState, useEffect } from "react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { SortableDemandItem } from "./sortable-demand-item";
import { DemandItem } from "./demand-item";
import { Card, CardContent } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { toast } from "sonner";
import { Ellipsis } from "lucide-react";

interface Client {
  id: string;
  name: string;
}

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  role?: string;
}

interface Content {
  id: string;
  type: 'content' | 'general';
  activityDate: string;
  title?: string;
  description?: string | null;
  status?: string | null;
  priority?: string;
  client: Client;
  assignedTo: User | null;
  isLooseClient?: boolean;
  looseClientName?: string | null;
  position?: number;
}

interface SortableDemandsListProps {
  demands: Content[];
  userId: string;
  userName?: string;
  demandType: 'general' | 'content' | 'all';
  onDemandsUpdate: (updatedDemands: Content[]) => void;
}

export function SortableDemandsList({ demands, userId, userName, demandType, onDemandsUpdate }: SortableDemandsListProps) {
  const [items, setItems] = useState<Content[]>([]);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {

    const sortedDemands = [...demands].sort((a, b) => {
      const posA = a.position || 9999;
      const posB = b.position || 9999;
      return posA - posB;
    });

    setItems(sortedDemands);
  }, [demands]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);

        const newItems = arrayMove(items, oldIndex, newIndex);

        const updatedItems = newItems.map((item, index) => {
          const position = index + 1;
          let priority = "baixa";

          if (position <= 5) {
            priority = "urgente";
          } else if (position <= 10) {
            priority = "alta";
          } else if (position <= 15) {
            priority = "normal";
          }

          return {
            ...item,
            position,
            priority
          };
        });

        savePositions(updatedItems);

        return updatedItems;
      });
    }

    setActiveId(null);
  };

  const savePositions = async (items: Content[]) => {
    setIsSaving(true);

    try {
      const contentItems = items.filter(item => item.type === 'content');
      const generalItems = items.filter(item => item.type === 'general');

      const contentUpdates = contentItems.map((item, index) => ({
        id: item.id,
        position: index + 1,
        priority: item.priority
      }));

      const generalUpdates = generalItems.map((item, index) => ({
        id: item.id,
        position: index + 1,
        priority: item.priority
      }));

      const shouldUpdateContent = (demandType === 'content' || demandType === 'all') && contentUpdates.length > 0;
      const shouldUpdateGeneral = (demandType === 'general' || demandType === 'all') && generalUpdates.length > 0;

      const updateResults = [];

      if (shouldUpdateContent) {
        try {
          const contentResponse = await fetch("/api/contents/update-positions", {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ updates: contentUpdates, userId }),
          });

          if (!contentResponse.ok) {
            let errorDetail = "";
            try {
              const errorData = await contentResponse.json();
              errorDetail = errorData.error || `Status: ${contentResponse.status}`;
              console.error("Detalhes do erro da API de conteúdo:", errorData);
            } catch {
              errorDetail = `Status: ${contentResponse.status}`;
            }

            updateResults.push({ success: false, error: `Falha ao atualizar posições de conteúdo: ${errorDetail}` });
          } else {
            updateResults.push({ success: true, type: 'content' });
          }
        } catch (error) {
          console.error("Erro ao atualizar posições de conteúdo:", error);
          updateResults.push({ success: false, error: "Erro ao atualizar posições de conteúdo" });
        }
      }

      if (shouldUpdateGeneral) {
        try {
          const generalResponse = await fetch("/api/general-demands/update-positions", {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ updates: generalUpdates, userId }),
          });

          if (!generalResponse.ok) {
            let errorDetail = "";
            try {
              const errorData = await generalResponse.json();
              errorDetail = errorData.error || `Status: ${generalResponse.status}`;
              console.error("Detalhes do erro da API de demandas gerais:", errorData);
            } catch {
              errorDetail = `Status: ${generalResponse.status}`;
            }

            updateResults.push({ success: false, error: `Falha ao atualizar posições de demandas gerais: ${errorDetail}` });
          } else {
            updateResults.push({ success: true, type: 'general' });
          }
        } catch (error) {
          console.error("Erro ao atualizar posições de demandas gerais:", error);
          updateResults.push({ success: false, error: "Erro ao atualizar posições de demandas gerais" });
        }
      }

      const errors = updateResults.filter(result => !result.success);
      if (errors.length > 0) {
        throw new Error(errors.map(e => e.error).join("; "));
      }

      onDemandsUpdate(items);
      toast.success("Prioridades atualizadas com sucesso!");

    } catch (error) {
      console.error("Erro ao salvar posições:", error);

      let errorMessage = "Erro ao salvar a nova ordem das demandas";
      if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }

      setItems(demands);

      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const activeItem = activeId ? items.find(item => item.id === activeId) : null;

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <h2 className="text-lg font-semibold mb-2">
          Lista de prioridades: {userName || 'Colaborador selecionado'}
        </h2>
        <p className="text-sm text-muted-foreground mb-2">
          A ordenação inclui todas as demandas deste colaborador e não afeta as demandas de outros colaboradores.
        </p>
        <div className="bg-muted p-3 rounded-md mb-4">
          <p className="text-sm font-medium mb-1">Como funciona:</p>
          <p className="text-sm text-muted-foreground mb-2">
            Arraste os itens para reordenar a prioridade das demandas. A prioridade é atualizada automaticamente com base na posição:
          </p>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 text-xs">
            <div className="flex items-center gap-1">
              <Badge variant="outline" className="bg-red-100 text-red-800">Urgente</Badge>
              <span>Posições 1-5</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge variant="outline" className="bg-orange-100 text-orange-800">Alta</Badge>
              <span>Posições 6-10</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge variant="outline" className="bg-blue-100 text-blue-800">Normal</Badge>
              <span>Posições 11-15</span>
            </div>
            <div className="flex items-center gap-1">
              <Badge variant="outline" className="bg-green-100 text-green-800">Baixa</Badge>
              <span>Posições 16+</span>
            </div>
          </div>
        </div>

        <div className="relative">
          {isSaving && (
            <div className="absolute inset-0 bg-background/50 flex items-center justify-center z-10">
              <Ellipsis />
            </div>
          )}

          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={items.map(item => item.id)} strategy={verticalListSortingStrategy}>
              <div className="space-y-2">
                {items.length > 0 ? (
                  items.map((item, index) => (
                    <SortableDemandItem
                      key={item.id}
                      demand={item}
                      position={index + 1}
                    />
                  ))
                ) : (
                  <div className="p-8 text-center border rounded-md bg-card">
                    <p className="text-muted-foreground">
                      Nenhuma demanda encontrada para ordenar.
                    </p>
                    <p className="text-sm text-muted-foreground mt-1">Tente remover os filtros ou adicionar novas demandas.</p>
                  </div>
                )}
              </div>
            </SortableContext>

            <DragOverlay>
              {activeItem ? (
                <DemandItem
                  demand={activeItem}
                  position={items.findIndex(item => item.id === activeItem.id) + 1}
                />
              ) : null}
            </DragOverlay>
          </DndContext>
        </div>
      </CardContent>
    </Card>
  );
}
