import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { DemandItem } from "./demand-item";

interface Client {
  id: string;
  name: string;
}

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  role?: string;
}

interface Content {
  id: string;
  type: 'content' | 'general';
  activityDate: string;
  title?: string;
  description?: string | null;
  status?: string | null;
  priority?: string;
  client: Client;
  assignedTo: User | null;
  isLooseClient?: boolean;
  looseClientName?: string | null;
  position?: number;
}

interface SortableDemandItemProps {
  demand: Content;
  position: number;
}

export function SortableDemandItem({ demand, position }: SortableDemandItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: demand.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 10 : 0,
    boxShadow: isDragging ? '0 5px 15px rgba(0, 0, 0, 0.15)' : 'none',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`touch-manipulation ${isDragging ? 'scale-[1.02]' : ''}`}
    >
      <DemandItem demand={demand} position={position} />
    </div>
  );
}
