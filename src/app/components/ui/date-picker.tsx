"use client"

import * as React from "react"
import { format, startOfWeek, endOfWeek, eachDayOfInterval } from "date-fns"
import { pt } from "date-fns/locale"
import { Calendar as CalendarIcon, Loader2 } from "lucide-react"

import { cn } from "@/lib/utils"

import { Calendar } from "./calendar"
import { Popover, PopoverContent, PopoverTrigger } from "./popover"
import { Button } from "./button"

export interface DatePickerProps {
    date: Date | undefined
    setDate: (date: Date | undefined) => void
    isLoading?: boolean
    className?: string
    fromMonth?: Date
    toMonth?: Date
    disabled?: boolean
    buttonLabel?: React.ReactNode
    iconOnly?: boolean
    highlightWeek?: boolean
}

export function DatePicker({
    date,
    setDate,
    isLoading = false,
    className,
    fromMonth,
    toMonth,
    disabled = false,
    buttonLabel,
    iconOnly = false,
    highlightWeek = false,
}: DatePickerProps) {
    const weekDays = React.useMemo(() => {
        if (!date || !highlightWeek) return []
        
        const start = startOfWeek(date, { locale: pt })
        const end = endOfWeek(date, { locale: pt })
        
        return eachDayOfInterval({ start, end })
    }, [date, highlightWeek])

    return (
        <Popover modal={true}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    className={cn(
                        "w-full justify-between text-left font-normal",
                        !date && "text-muted-foreground",
                        className
                    )}
                    type="button"
                    disabled={disabled || isLoading}
                >
                    {isLoading ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                        <CalendarIcon className="mr-2 h-4 w-4" />
                    )}
                    {iconOnly ? null : (
                        buttonLabel || (date ? format(date, "PPP", { locale: pt }) : "Escolha a data")
                    )}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    initialFocus
                    locale={pt}
                    fromMonth={fromMonth}
                    toMonth={toMonth}
                    disabled={disabled}
                    modifiers={{
                        weekHighlight: date && highlightWeek ? weekDays : []
                    }}
                    modifiersClassNames={{
                        weekHighlight: "bg-accent/40 text-accent-foreground rounded-none"
                    }}
                />
            </PopoverContent>
        </Popover>
    )
}