export function UpdatesInformationOthers() {
    return (
        <div className="space-y-2">
            {/* <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Na página inicial, você verá somente as demandas que não foram concluídas.</p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Na página de demandas, você verá por padrão somente as demandas que não foram concluídas. Mas, você pode filtrar as demandas por status para ver as demais.</p>
            </div> */}
            {/* <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm"><PERSON><PERSON><PERSON>, você pode receber alterações em suas demandas. Ao receber uma alteração, o status da demanda será automaticamente atualizado para &quot;alteração&quot; e você será notificado. O comentário da alteração será exibido na demanda.</p>
            </div>
            <div className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">Agora, você pode mudar o status das demandas para &quot;em andamento&quot; quando começar a trabalhar nela. Quando terminar de trabalhar na demanda, você pode marcá-la como concluída, isso automaticamente mudará o status para &quot;em revisão&quot; para a próxima etapa do planejamento mensal. Ou seja, você saberá que concluiu quando o status mudar para &quot;em revisão&quot;. Isso significa que você terminou de trabalhar na demanda, mas ela ainda não foi revisada pela equipe.</p>
            </div> */}
            <div className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 mt-1.5 flex-shrink-0 rounded-full bg-primary2"></div>
                <p className="text-sm">
                    Agora, você pode adicionar multiplas URLs de arquivos para cada conteúdo. Além disso, você pode definir o tipo de cada URL (Feed ou Story), o tipo de mídia (Foto ou Vídeo) e adicionar a URL de uma pasta do Google Drive.
                </p>
            </div>
        </div>
    );
}
