"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { ExternalLink, User } from "lucide-react"
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from "../ui/table"
import { format, isThis<PERSON><PERSON>h, parseISO } from "date-fns"
import { ptBR } from "date-fns/locale"
import { MarkAsCompleted } from "@/app/components/mark-as-completed"
import { ActivityInfo } from "@/app/components/activity-info"
import { isPendingDemand } from "@/lib/utils"
import { Button } from "../ui/button"

interface Client {
    id: string
    name: string
    instagramUsername?: string
}

interface UserInterface {
    id: string
    name: string | null
    email: string
    image: string | null
}

interface Step {
    id: string
    type: string
    assignedTo: UserInterface
}

interface WeeklyActivity {
    id: string
    week: number
    description: string
    monthlyPlanning: {
        status: string
        id: string
        month: number
        year: number
        client: Client
    }
}

interface Content {
    urlStructuringFeed: string[] | undefined
    title: string | undefined
    id: string
    contentType: string
    status: string
    destination: string
    details?: string
    activityDate: string
    reference?: string
    copywriting?: string
    caption?: string
    weeklyActivity?: WeeklyActivity
    assignedTo?: UserInterface | null
    type?: "content" | "general"
    client?: Client
    isLooseClient?: boolean
    steps?: Step[]
}

export const UserLatestDemands = () => {
    const [userDemands, setUserDemands] = useState<Content[]>([])

    useEffect(() => {
        const fetchUserDemands = async () => {
            try {
                const response = await fetch("/api/user/demands")

                if (!response.ok) {
                    console.error(`Erro ao buscar demandas do usuário: ${response.status}`)
                    return
                }

                const data = await response.json()

                const pendingDemands = data.filter((demand: Content) => {
                    if (demand.type === "content") {
                        return (
                            demand.weeklyActivity?.monthlyPlanning?.status === "aprovado" &&
                            isPendingDemand(demand) &&
                            isThisMonth(parseISO(demand.activityDate))
                        )
                    }

                    if (demand.type === "general") {
                        return isPendingDemand(demand) && isThisMonth(parseISO(demand.activityDate))
                    }

                    return false
                })

                setUserDemands(pendingDemands.slice(0, 4))
            } catch (error) {
                console.error("Erro ao buscar demandas do usuário:", error)
            }
        }

        fetchUserDemands()
    }, [])

    const handleStatusUpdate = async (contentId: string, newStatus: string) => {
        setUserDemands((prevDemands) =>
            prevDemands.map((demand) => (demand.id === contentId ? { ...demand, status: newStatus } : demand)),
        )

        const demand = userDemands.find((d) => d.id === contentId)
        if (!demand) return

        try {
            const apiUrl =
                demand.type === "general" ? `/api/general-demands/${contentId}/status` : `/api/contents/${contentId}`

            await fetch(apiUrl, {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ status: newStatus }),
            })
        } catch (error) {
            console.error("Erro ao atualizar status:", error)
        }
    }

    if (userDemands.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center py-16 px-4">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-orange-100 dark:from-orange-900/20 dark:to-orange-900/20 rounded-2xl flex items-center justify-center mb-4">
                    <User className="w-8 h-8 text-orange-600 dark:text-orange-400" />
                </div>
                <p className="text-sm text-slate-500 dark:text-slate-400 text-center max-w-sm">
                    Você não possui demandas para o mês atual atribuídas.
                </p>
            </div>
        )
    }

    return (
        <div className="overflow-auto max-h-[500px] scrollbar-thin scrollbar-thumb-slate-300 dark:scrollbar-thumb-zinc-600 scrollbar-track-transparent">
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Cliente</TableHead>
                        <TableHead>Data</TableHead>
                        <TableHead>Tipo</TableHead>
                        <TableHead>Descrição</TableHead>
                        <TableHead className="text-right">Ações</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {userDemands.map((demand) => (
                        <TableRow key={demand.id}>
                            <TableCell>
                                <div className="flex flex-col">
                                    <span className="font-medium text-sm">
                                        {demand.type === "general"
                                            ? demand.client
                                                ? demand.isLooseClient
                                                    ? `${demand.client.name}`
                                                    : demand.client.name
                                                : "Cliente não especificado"
                                            : demand.weeklyActivity
                                                ? demand.weeklyActivity.monthlyPlanning.client.name
                                                : demand.client
                                                    ? demand.isLooseClient
                                                        ? `${demand.client.name}`
                                                        : demand.client.name
                                                    : "Cliente não especificado"}
                                    </span>
                                </div>
                            </TableCell>
                            <TableCell>
                                {format(new Date(demand.activityDate), "dd/MM/yyyy", { locale: ptBR })}
                            </TableCell>
                            <TableCell>
                                {demand.type === "general" ? "Demanda pontual" : demand.contentType}
                            </TableCell>
                            <TableCell>
                                <div className="text-sm text-slate-600 dark:text-slate-400 line-clamp-2">
                                    {demand.type === "general"
                                        ? demand.title || demand.details || "Demanda pontual"
                                        : demand.details || demand.title || `Conteúdo para ${demand.destination || "destino não especificado"}`}
                                </div>
                            </TableCell>
                            <TableCell className="text-right">
                                <div className="inline-flex items-center gap-2">
                                    <MarkAsCompleted
                                        contentId={demand.id}
                                        currentStatus={demand.status || ""}
                                        onStatusUpdate={(newStatus) => handleStatusUpdate(demand.id, newStatus)}
                                        type={demand.type || "content"}
                                    />
                                    <ActivityInfo
                                        activity={{
                                            reference: demand.reference,
                                            copywriting: demand.copywriting,
                                            caption: demand.caption,
                                            url: demand.urlStructuringFeed,
                                        }}
                                    />
                                    <Link href="/my-demands" title="Ver demanda">
                                        <Button variant="secondary" size="icon">
                                            <ExternalLink size={14} />
                                        </Button>
                                    </Link>
                                </div>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
            {userDemands.length > 0 && (
                <div className="pt-4 text-center">
                    <Link
                        href="/my-demands"
                    >
                        <Button variant="link" className="text-muted-foreground">
                            Ver todas as suas demandas
                        </Button>
                    </Link>
                </div>
            )}
        </div>
    )
}
