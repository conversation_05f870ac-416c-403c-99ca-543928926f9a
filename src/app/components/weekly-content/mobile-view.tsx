"use client";

import React from 'react';
import { Button } from "@/app/components/ui/button";
import { Separator } from "@/app/components/ui/separator";
import { ActivityInfo } from "@/app/components/activity-info";
import { UpdateContentStatus } from "@/app/components/update-content-status";
import { EditPlanActivity } from "@/app/components/edit-plan-activity";
import { RemovesPlanActivity } from "@/app/components/removes-plan-activity";
import { ClipboardCopy } from "lucide-react";
import { toast } from "sonner";
import { ContentSteps } from "@/app/components/content-steps";
import { ContentAssignmentView } from "@/app/components/content-assignment-view";

interface ContentType {
    clientId?: string;
    id: string;
    activityDate: string;
    contentType: string;
    channel: string;
    details: string;
    destination: string;
    caption: string;
    status?: string;
    assignedTo?: {
        id: string;
        name: string;
        email: string;
        image?: string;
    } | null;
    steps?: {
        id: string;
        type: string;
        assignedTo: {
            id: string;
            name: string;
            email: string;
            image?: string;
        }
    }[];
}

interface WeeklyContentMobileViewProps {
    activities: {
        id: string;
        description: string;
        week: number;
        contents: ContentType[];
    }[];
    refreshClientData: () => void;
    updateKey: number;
    isReadOnly: boolean;
    addingActivityToWeek: string | null;
    userRole: string;
}

export const WeeklyContentMobileView = ({
    activities,
    refreshClientData,
    updateKey,
    isReadOnly,
    addingActivityToWeek,
    userRole
}: WeeklyContentMobileViewProps) => {
    return (
        <div className="space-y-4">
            {activities.flatMap(activity =>
                (activity.contents || []).map(content => (
                    <React.Fragment key={`${content.id}-${updateKey}`}>
                        <div key={content.id} className="p-3 space-y-2 border-t dark:border-zinc-800">
                            <div className="flex justify-between gap-2 items-center">
                                <div className="flex items-center gap-1 xs:gap-4">
                                    <div className="font-medium text-sm">
                                        {new Date(content.activityDate).toLocaleDateString("pt-BR", {
                                            weekday: 'short',
                                            day: "2-digit",
                                            month: "2-digit",
                                        })}
                                        <span className="block text-[10px] text-muted-foreground mt-0.5">
                                            #{content.id.substring(0, 8).toUpperCase()}
                                        </span>
                                    </div>
                                    <Separator
                                        orientation="vertical"
                                        className="w-2 h-2"
                                    />
                                    <div className="flex flex-col xs:flex-row items-center">
                                        <p className="text-xs truncate font-medium">
                                            {content.contentType}
                                        </p>
                                        <Separator
                                            orientation="vertical"
                                            className="w-10 xs:w-2 h-[1px] mx-2"
                                        />
                                        <p className="text-xs truncate font-medium">
                                            {content.destination}
                                        </p>
                                    </div>
                                </div>
                                <div className="flex gap-2 items-center">
                                    <ActivityInfo activity={content} />
                                    <ContentSteps
                                        contentId={content.id}
                                        onSuccess={refreshClientData}
                                    />
                                    <EditPlanActivity
                                        content={{
                                            ...content,
                                            clientId: content.clientId || '',
                                            weeklyActivityId: activity.id,
                                            assignedToId: content.assignedTo?.id || null,
                                            assignedTo: content.assignedTo || null,
                                        }}
                                        onSuccess={refreshClientData}
                                        disabled={isReadOnly}
                                    />
                                    <RemovesPlanActivity
                                        content={content}
                                        onSuccess={refreshClientData}
                                        disabled={isReadOnly}
                                    />
                                </div>
                            </div>
                            <div className="text-sm">
                                {content.details}
                                <ContentAssignmentView 
                                    assignedTo={content.assignedTo} 
                                    steps={content.steps}
                                />
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    className="ml-2 hover:bg-transparent hover:opacity-100"
                                    onClick={() => {
                                        navigator.clipboard.writeText(content.details);
                                        toast.success("Conteúdo copiado");
                                    }}>
                                    <ClipboardCopy />
                                </Button>
                            </div>
                            <div className="flex justify-between items-center">
                                <p className="text-xs font-semibold">
                                    {content.channel}
                                </p>
                                <div className="ml-2">
                                    <UpdateContentStatus
                                        contentId={content.id}
                                        currentStatus={content.status || 'pendente'}
                                        onSuccess={refreshClientData}
                                        disabled={isReadOnly}
                                        isAdmin={userRole === 'ADMIN' || userRole === 'DEVELOPER'}
                                    />
                                </div>
                            </div>
                        </div>
                    </React.Fragment>
                ))
            )}

            {!activities.some(act => act.contents && act.contents.length > 0) && !addingActivityToWeek && (
                <div className="text-center text-gray-500 dark:text-gray-400 py-4">
                    Nenhum conteúdo cadastrado para esta semana.
                </div>
            )}
        </div>
    );
};