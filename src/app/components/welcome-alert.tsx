import { useState, useEffect } from "react";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { Separator } from "@/app/components/ui/separator";

interface WelcomeAlertProps {
    userId: string;
    userName?: string;
    version?: string;
}

export function WelcomeAlert({ userId, userName, version = "1.0" }: WelcomeAlertProps) {
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const seenVersion = localStorage.getItem(`welcome-seen-${userId}`);

        if (!seenVersion || seenVersion !== version) {
            setIsOpen(true);
        }
    }, [userId, version]);

    const handleClose = () => {
        localStorage.setItem(`welcome-seen-${userId}`, version);
        setIsOpen(false);
    };

    return (

        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>
                        Boas vindas ao B4Desk! 🎉
                    </DialogTitle>
                    <DialogDescription className="pt-2">
                        Olá <span className="font-medium">{userName || "usuário"}</span>, seja bem-vindo ao B4Desk!
                    </DialogDescription>
                </DialogHeader>
                <Separator />
                <div className="text-sm text-zinc-700 dark:text-zinc-300 space-y-2">
                    <p>
                        Ficamos muito felizes em ter você conosco. O B4Desk é uma ferramenta poderosa para ajudar os clientes da B4 Comunicação a gerenciar seus materiais e atividades de forma mais eficiente.
                    </p>

                    <Separator className="my-2" />

                    <p>
                        Estamos em constante evolução e adicionando novas funcionalidades para tornar sua experiência ainda melhor. Qualquer feedback é bem-vindo!
                    </p>
                </div>

                <DialogFooter>
                    <Button onClick={handleClose} className="w-full sm:w-auto">
                        Entendi
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}