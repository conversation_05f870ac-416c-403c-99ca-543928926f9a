"use client"

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON><PERSON>, MoveLeft } from "lucide-react";
import { toast } from "sonner";
import { Footer } from "../components/footer";
import { Header } from "../components/header";
import { Button } from "../components/ui/button";
import { Input } from "../components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { MultiSelect } from "@/app/components/ui/multi-select";
import { formatPhoneOnInput, isValidPhone, formatCnpjOnInput, isValidCnpj } from "@/lib/formatters";
import { useSession } from "next-auth/react";
import { NotAllowed } from "../components/not-allowed";
import Loading from "../components/ui/loading";
import { OwnersManager } from "../components/owners-manager";

interface Owner {
    id?: string;
    name: string;
    cpf?: string;
    birthDate?: Date | string;
    rg?: string;
    issuingAgency?: string;
    maritalStatus?: string;
    nationality?: string;
    profession?: string;
    isPrimary?: boolean;
    address?: Address;
}

interface Client {
    id: string;
    name: string;
    companyName?: string
    tradingName?: string;
    instagramUsername?: string;
    phone?: string;
    monthlyPostsLimit?: number;
    monthlyStoriesLimit?: number;
    additionalContent?: number;
    cnpj?: string;
    brandAnniversary?: Date;
    mainTypography?: string;
    secondaryTypography?: string;
    importantDetails?: string;
    restrictions?: string;
    profileDescription?: string;
    segment?: string[];
    services?: string[];
    products?: string[];
    storeAddress?: Address;
    factoryAddress?: Address;
    storeAddressId?: string;
    factoryAddressId?: string;
    paymentMethod?: string;
    paymentDay?: number;
    mission?: string;
    vision?: string;
    values?: string;
    owners?: Owner[];
    urlLogoGoogleDrive?: string;
}

interface Address {
    id?: string;
    zipCode?: string;
    street?: string;
    number?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
    country?: string;
}

const PAYMENT_METHODS = [
    { value: "PIX", label: "PIX" },
    { value: "Transferência bancária", label: "Transferência bancária" },
    { value: "Boleto", label: "Boleto" },
    { value: "Cartão de crédito", label: "Cartão de crédito" },
    { value: "Dinheiro", label: "Dinheiro" },
    { value: "Outro", label: "Outro" }
];

const SEGMENTS = [
    { value: "Alimentação", label: "Alimentação" },
    { value: "Moda", label: "Moda" },
    { value: "Tecnologia", label: "Tecnologia" },
    { value: "Saúde", label: "Saúde" },
    { value: "Beleza", label: "Beleza" },
    { value: "Educação", label: "Educação" },
    { value: "Entretenimento", label: "Entretenimento" },
    { value: "Serviços", label: "Serviços" },
    { value: "Varejo", label: "Varejo" },
    { value: "Loja de Moda Fitness", label: "Loja de Moda Fitness" },
    { value: "Pizzaria", label: "Pizzaria" },
    { value: 'Lavagem de carros', label: "Lavagem de carros" },
    { value: 'Loja de departamentos', label: "Loja de departamentos" },
    { value: 'Corretor(a) de imóveis', label: "Corretor(a) de imóveis" },
    { value: "Loja de alfaiataria", label: "Loja de alfaiataria" },
    { value: "Outro", label: "Outro" }
];

const SERVICES = [
    { value: "Consultoria", label: "Consultoria" },
    { value: "Desenvolvimento", label: "Desenvolvimento" },
    { value: "Design", label: "Design" },
    { value: "Marketing", label: "Marketing" },
    { value: "Vendas", label: "Vendas" },
    { value: "Suporte", label: "Suporte" },
    { value: "Treinamento", label: "Treinamento" },
    { value: "Outro", label: "Outro" }
];

const PRODUCTS = [
    { value: "Alimentos", label: "Alimentos" },
    { value: "Bebidas", label: "Bebidas" },
    { value: "Roupas", label: "Roupas" },
    { value: "Calçados", label: "Calçados" },
    { value: "Acessórios", label: "Acessórios" },
    { value: "Eletrônicos", label: "Eletrônicos" },
    { value: "Móveis", label: "Móveis" },
    { value: "Decoração", label: "Decoração" },
    { value: "Cosméticos", label: "Cosméticos" },
    { value: "Outro", label: "Outro" }
];

export default function CreateClientPage() {
    const { data: session, status } = useSession();
    const router = useRouter();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isAdmin, setIsAdmin] = useState(false);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [newClient, setNewClient] = useState<Omit<Client, 'id'>>({
        name: '',
        companyName: '',
        tradingName: '',
        instagramUsername: '',
        phone: '',
        monthlyPostsLimit: 0,
        monthlyStoriesLimit: 0,
        additionalContent: 0,
        cnpj: '',
        mainTypography: '',
        secondaryTypography: '',
        importantDetails: '',
        restrictions: '',
        profileDescription: '',
        segment: [],
        services: [],
        products: [],
        storeAddress: {
            street: '',
            number: '',
            neighborhood: '',
            city: '',
            state: '',
            country: '',
        },
        factoryAddress: {
            street: '',
            number: '',
            neighborhood: '',
            city: '',
            state: '',
            country: '',
        },
        owners: [],
        paymentMethod: '',
        paymentDay: undefined,
        mission: '',
        vision: '',
        values: '',
        urlLogoGoogleDrive: '',
    });

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        if (status === "authenticated") {
            const fetchUserRole = async () => {
                try {
                    if (session?.user?.email) {
                        const response = await fetch(`/api/users/${session.user.email}`);
                        if (response.ok) {
                            const user = await response.json();
                            const allowedRoles = ["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"];
                            setIsAdmin(allowedRoles.includes(user?.role || ''));
                        } else if (response.status === 404) {
                            console.warn("Usuário não encontrado no banco de dados. Acesso negado.");
                            setIsAdmin(false);
                        } else {
                            console.error("Erro ao buscar função do usuário. Status:", response.status);
                        }
                    }
                } catch (error) {
                    console.error("Erro ao buscar função do usuário:", error);
                } finally {
                    setIsFetchingRole(false);
                }
            };

            fetchUserRole();
        }
    }, [status, session]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;

        if (name === 'phone') {
            setNewClient((prevClient) => ({
                ...prevClient,
                [name]: formatPhoneOnInput(value),
            }));
        } else if (name === 'cnpj') {
            setNewClient((prevClient) => ({
                ...prevClient,
                [name]: formatCnpjOnInput(value),
            }));
        } else if (['monthlyPostsLimit', 'monthlyStoriesLimit', 'additionalContent', 'paymentDay'].includes(name)) {
            const numValue = Number(value);
            if (name === 'paymentDay') {
                const validDay = !isNaN(numValue) ? Math.min(Math.max(numValue, 1), 31) : undefined;
                setNewClient((prevClient) => ({
                    ...prevClient,
                    [name]: validDay,
                }));
            } else {
                setNewClient((prevClient) => ({
                    ...prevClient,
                    [name]: Number(value) || 0,
                }));
            }
        } else if (['ownerBirthday', 'brandAnniversary'].includes(name)) {
            setNewClient((prevClient) => ({
                ...prevClient,
                [name]: value ? new Date(value) : undefined,
            }));
        } else {
            setNewClient((prevClient) => ({
                ...prevClient,
                [name]: value,
            }));
        }
    };

    const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>, addressType: 'store' | 'factory') => {
        const { name, value } = e.target;

        setNewClient((prevClient) => ({
            ...prevClient,
            [addressType === 'store' ? 'storeAddress' : 'factoryAddress']: {
                ...prevClient[addressType === 'store' ? 'storeAddress' : 'factoryAddress'],
                [name]: value,
            },
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        if (status !== "authenticated") {
            toast.error("Você precisa estar autenticado para cadastrar um cliente.");
            setIsSubmitting(false);
            return;
        }

        if (newClient.phone && !isValidPhone(newClient.phone)) {
            toast.error("O número de telefone deve ter 10 ou 11 dígitos, incluindo o DDD.");
            setIsSubmitting(false);
            return;
        }

        if (newClient.cnpj && !isValidCnpj(newClient.cnpj)) {
            toast.error("CNPJ inválido. Verifique o formato e os dígitos verificadores.");
            setIsSubmitting(false);
            return;
        }

        try {
            const response = await fetch('/api/clients', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: newClient.name,
                    companyName: newClient.companyName,
                    tradingName: newClient.tradingName,
                    instagramUsername: newClient.instagramUsername,
                    phone: newClient.phone,
                    monthlyPostsLimit: Number(newClient.monthlyPostsLimit),
                    monthlyStoriesLimit: Number(newClient.monthlyStoriesLimit),
                    additionalContent: Number(newClient.additionalContent),
                    cnpj: newClient.cnpj,
                    brandAnniversary: newClient.brandAnniversary,
                    mainTypography: newClient.mainTypography,
                    secondaryTypography: newClient.secondaryTypography,
                    importantDetails: newClient.importantDetails,
                    restrictions: newClient.restrictions,
                    profileDescription: newClient.profileDescription,
                    segment: newClient.segment,
                    services: newClient.services,
                    products: newClient.products,
                    storeAddress: newClient.storeAddress && Object.values(newClient.storeAddress).some(val => val) ? {
                        create: {
                            zipCode: newClient.storeAddress?.zipCode,
                            street: newClient.storeAddress?.street,
                            number: newClient.storeAddress?.number,
                            neighborhood: newClient.storeAddress?.neighborhood,
                            city: newClient.storeAddress?.city,
                            state: newClient.storeAddress?.state,
                            country: newClient.storeAddress?.country || 'Brasil',
                        }
                    } : undefined,
                    factoryAddress: newClient.factoryAddress && Object.values(newClient.factoryAddress).some(val => val) ? {
                        create: {
                            zipCode: newClient.factoryAddress?.zipCode,
                            street: newClient.factoryAddress?.street,
                            number: newClient.factoryAddress?.number,
                            neighborhood: newClient.factoryAddress?.neighborhood,
                            city: newClient.factoryAddress?.city,
                            state: newClient.factoryAddress?.state,
                            country: newClient.factoryAddress?.country || 'Brasil',
                        }
                    } : undefined,
                    owners: newClient.owners && newClient.owners.length > 0 ?
                        newClient.owners.map(owner => {
                            const cleanOwnerData = { ...owner };

                            if ('clientId' in cleanOwnerData) delete cleanOwnerData.clientId;
                            if ('addressId' in cleanOwnerData) delete cleanOwnerData.addressId;
                            if ('createdAt' in cleanOwnerData) delete cleanOwnerData.createdAt;
                            if ('updatedAt' in cleanOwnerData) delete cleanOwnerData.updatedAt;

                            if (cleanOwnerData.birthDate) {
                                try {
                                    if (typeof cleanOwnerData.birthDate === 'string' && !cleanOwnerData.birthDate.includes('T')) {
                                        cleanOwnerData.birthDate = cleanOwnerData.birthDate;
                                    } else {
                                        const date = new Date(cleanOwnerData.birthDate as string | number | Date);
                                        cleanOwnerData.birthDate = date.toISOString().split('T')[0];
                                    }
                                } catch {
                                    delete cleanOwnerData.birthDate;
                                }
                            }

                            const cleanAddress = owner.address ? { ...owner.address } : undefined;
                            if (cleanAddress) {
                                if ('id' in cleanAddress) delete cleanAddress.id;
                                if ('createdAt' in cleanAddress) delete cleanAddress.createdAt;
                                if ('updatedAt' in cleanAddress) delete cleanAddress.updatedAt;
                            }

                            return {
                                ...cleanOwnerData,
                                address: cleanAddress
                            };
                        })
                        : [],
                    paymentMethod: newClient.paymentMethod,
                    paymentDay: newClient.paymentDay,
                    mission: newClient.mission,
                    vision: newClient.vision,
                    values: newClient.values,
                    urlLogoGoogleDrive: newClient.urlLogoGoogleDrive,
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || "Erro ao cadastrar cliente");
            }

            toast.success("Cliente cadastrado com sucesso!", {
                position: "bottom-left"
            });

            router.push('/clients');
        } catch (error) {
            console.error("Erro ao cadastrar cliente:", error);
            toast.error("Erro ao cadastrar cliente.");
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isFetchingRole ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : !isAdmin ? (
                    <NotAllowed
                        page="clients"
                    />
                ) : (
                    <>
                        <div className="flex flex-col items-start gap-2 mb-4 space-y-4">
                            <Button variant="outline" onClick={() => router.push("/clients")}>
                                <MoveLeft /> Voltar
                            </Button>
                            <h1 className="text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800">Criar cliente</h1>
                        </div>

                        <div className="max-w-2xl mx-auto mt-6">
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div>
                                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Nome
                                    </label>
                                    <Input type="text" id="name" name="name" placeholder='Nome' value={newClient.name} onChange={handleInputChange} required className="mt-1" />
                                </div>
                                <div className='grid xs:grid-cols-2 gap-2'>
                                    <div>
                                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Telefone
                                        </label>
                                        <Input type="text" id="phone" name="phone" placeholder='Número' value={newClient.phone} onChange={handleInputChange} className="mt-1" />
                                    </div>
                                    <div>
                                        <label htmlFor="instagramUsername" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Instagram
                                        </label>
                                        <Input type="text" id="instagramUsername" name="instagramUsername" placeholder='nome_de_usuario' value={newClient.instagramUsername} onChange={handleInputChange} className="mt-1" />
                                    </div>
                                </div>
                                <div>
                                    <label htmlFor="urlLogoGoogleDrive" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        URL da Logo no Google Drive
                                    </label>
                                    <Input
                                        type="text"
                                        id="urlLogoGoogleDrive"
                                        name="urlLogoGoogleDrive"
                                        placeholder='https://drive.google.com/file/d/XXXXXXXX/view'
                                        value={newClient.urlLogoGoogleDrive}
                                        onChange={handleInputChange}
                                        className="mt-1"
                                    />
                                    <p className="text-xs text-muted-foreground mt-1">
                                        Cole aqui a URL da imagem do Google Drive
                                    </p>
                                </div>
                                <div className='grid grid-cols-2 gap-2'>
                                    <div>
                                        <label htmlFor="monthlyPostsLimit" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Limite de posts
                                        </label>
                                        <Input type="number" id="monthlyPostsLimit" name="monthlyPostsLimit" value={newClient.monthlyPostsLimit} onChange={handleInputChange} className="mt-1" />
                                    </div>
                                    <div>
                                        <label htmlFor="monthlyStoriesLimit" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Limite de stories
                                        </label>
                                        <Input type="number" id="monthlyStoriesLimit" name="monthlyStoriesLimit" value={newClient.monthlyStoriesLimit} onChange={handleInputChange} className="mt-1" />
                                    </div>
                                </div>
                                <div>
                                    <label htmlFor="additionalContent" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Materiais extras
                                    </label>
                                    <Input type="number" id="additionalContent" name="additionalContent" value={newClient.additionalContent} onChange={handleInputChange} className="mt-1" />
                                </div>

                                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <h2 className="text-lg font-medium mb-4">Dados da empresa</h2>
                                    <div className="grid gap-4">
                                        <div>
                                            <label htmlFor="companyName" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Razão Social
                                            </label>
                                            <Input type="text" id="companyName" name="companyName" placeholder="Razão Social" value={newClient.companyName} onChange={handleInputChange} className="mt-1" />
                                        </div>
                                        <div>
                                            <label htmlFor="tradingName" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Nome Fantasia
                                            </label>
                                            <Input type="text" id="tradingName" name="tradingName" placeholder="Nome Fantasia" value={newClient.tradingName} onChange={handleInputChange} className="mt-1" />
                                        </div>
                                        <div>
                                            <label htmlFor="cnpj" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                CNPJ
                                            </label>
                                            <Input type="text" id="cnpj" name="cnpj" placeholder="00.000.000/0000-00" value={newClient.cnpj} onChange={handleInputChange} className="mt-1" />
                                        </div>
                                    </div>
                                </div>

                                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <OwnersManager
                                        owners={newClient.owners || []}
                                        onChange={(owners) => setNewClient(prev => ({ ...prev, owners }))}
                                    />
                                </div>

                                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <h2 className="text-lg font-medium mb-4">Dados da marca</h2>
                                    <div className="mt-4">
                                        <label htmlFor="brandAnniversary" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Aniversário da marca
                                        </label>
                                        <Input type="date" id="brandAnniversary" name="brandAnniversary" onChange={handleInputChange} className="mt-1" />
                                    </div>
                                    <div className="grid xs:grid-cols-2 gap-4 mt-4">
                                        <div>
                                            <label htmlFor="mainTypography" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Tipografia principal
                                            </label>
                                            <Input type="text" id="mainTypography" name="mainTypography" placeholder="Ex: Helvetica" value={newClient.mainTypography} onChange={handleInputChange} className="mt-1" />
                                        </div>
                                        <div>
                                            <label htmlFor="secondaryTypography" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Tipografia secundária
                                            </label>
                                            <Input type="text" id="secondaryTypography" name="secondaryTypography" placeholder="Ex: Arial" value={newClient.secondaryTypography} onChange={handleInputChange} className="mt-1" />
                                        </div>
                                    </div>
                                    <div className="mt-4">
                                        <label htmlFor="segment" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Segmento
                                        </label>
                                        <MultiSelect
                                            options={SEGMENTS}
                                            selected={newClient.segment || []}
                                            onChange={(values) => setNewClient((prev) => ({ ...prev, segment: values }))}
                                            placeholder="Selecione segmentos"
                                            className="mt-1"
                                        />
                                    </div>
                                </div>

                                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <h2 className="text-lg font-medium mb-4">Informações de negócio</h2>
                                    <div>
                                        <label htmlFor="services" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Serviços oferecidos
                                        </label>
                                        <MultiSelect
                                            options={SERVICES}
                                            selected={newClient.services || []}
                                            onChange={(values) => setNewClient((prev) => ({ ...prev, services: values }))}
                                            placeholder="Selecione serviços"
                                            className="mt-1"
                                        />
                                    </div>
                                    <div className="mt-4">
                                        <label htmlFor="products" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Produtos
                                        </label>
                                        <MultiSelect
                                            options={PRODUCTS}
                                            selected={newClient.products || []}
                                            onChange={(values) => setNewClient((prev) => ({ ...prev, products: values }))}
                                            placeholder="Selecione produtos"
                                            className="mt-1"
                                        />
                                    </div>
                                    <div className="grid xs:grid-cols-2 gap-4 mt-4">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Endereço da loja
                                            </label>
                                            <div className="space-y-2">
                                                <Input type="text" id="store-zipCode" name="zipCode" placeholder="CEP" value={newClient.storeAddress?.zipCode || ''} onChange={(e) => handleAddressChange(e, 'store')} />
                                                <Input type="text" id="store-street" name="street" placeholder="Rua" value={newClient.storeAddress?.street || ''} onChange={(e) => handleAddressChange(e, 'store')} />
                                                <div className="grid grid-cols-2 gap-2">
                                                    <Input type="text" id="store-number" name="number" placeholder="Número" value={newClient.storeAddress?.number || ''} onChange={(e) => handleAddressChange(e, 'store')} />
                                                    <Input type="text" id="store-neighborhood" name="neighborhood" placeholder="Bairro" value={newClient.storeAddress?.neighborhood || ''} onChange={(e) => handleAddressChange(e, 'store')} />
                                                </div>
                                                <div className="grid grid-cols-2 gap-2">
                                                    <Input type="text" id="store-city" name="city" placeholder="Cidade" value={newClient.storeAddress?.city || ''} onChange={(e) => handleAddressChange(e, 'store')} />
                                                    <Input type="text" id="store-state" name="state" placeholder="Estado" value={newClient.storeAddress?.state || ''} onChange={(e) => handleAddressChange(e, 'store')} />
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                Endereço da fábrica
                                            </label>
                                            <div className="space-y-2">
                                                <Input type="text" id="factory-zipCode" name="zipCode" placeholder="CEP" value={newClient.factoryAddress?.zipCode || ''} onChange={(e) => handleAddressChange(e, 'factory')} />
                                                <Input type="text" id="factory-street" name="street" placeholder="Rua" value={newClient.factoryAddress?.street || ''} onChange={(e) => handleAddressChange(e, 'factory')} />
                                                <div className="grid grid-cols-2 gap-2">
                                                    <Input type="text" id="factory-number" name="number" placeholder="Número" value={newClient.factoryAddress?.number || ''} onChange={(e) => handleAddressChange(e, 'factory')} />
                                                    <Input type="text" id="factory-neighborhood" name="neighborhood" placeholder="Bairro" value={newClient.factoryAddress?.neighborhood || ''} onChange={(e) => handleAddressChange(e, 'factory')} />
                                                </div>
                                                <div className="grid grid-cols-2 gap-2">
                                                    <Input type="text" id="factory-city" name="city" placeholder="Cidade" value={newClient.factoryAddress?.city || ''} onChange={(e) => handleAddressChange(e, 'factory')} />
                                                    <Input type="text" id="factory-state" name="state" placeholder="Estado" value={newClient.factoryAddress?.state || ''} onChange={(e) => handleAddressChange(e, 'factory')} />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <h2 className="text-lg font-medium mb-4">Detalhes de pagamento</h2>
                                    <div className="grid xs:grid-cols-2 gap-4">
                                        <div>
                                            <label htmlFor="paymentMethod" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Método de pagamento
                                            </label>
                                            <Select onValueChange={(value) => setNewClient((prev) => ({ ...prev, paymentMethod: value }))} defaultValue={newClient.paymentMethod}>
                                                <SelectTrigger className="mt-1" id="paymentMethod">
                                                    <SelectValue placeholder="Selecione o método de pagamento" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {PAYMENT_METHODS.map((option) => (
                                                        <SelectItem key={option.value} value={option.value}>
                                                            {option.label}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                        <div>
                                            <label htmlFor="paymentDay" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                                Dia de pagamento
                                            </label>
                                            <Input type="number" id="paymentDay" name="paymentDay" min="1" max="31" placeholder="Dia do mês (1-31)" value={newClient.paymentDay || ''} onChange={handleInputChange} className="mt-1" />
                                            <p className="text-xs text-gray-500 mt-1">
                                                Informe o dia do mês (1-31) em que o pagamento deve ser realizado
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <h2 className="text-lg font-medium mb-4">Perfil e restrições</h2>
                                    <div>
                                        <label htmlFor="profileDescription" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Descrição do perfil
                                        </label>
                                        <Input type="text" id="profileDescription" name="profileDescription" placeholder="Descreva o perfil da marca" value={newClient.profileDescription} onChange={handleInputChange} className="mt-1" />
                                    </div>
                                    <div className="mt-4">
                                        <label htmlFor="importantDetails" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Detalhes importantes
                                        </label>
                                        <Input type="text" id="importantDetails" name="importantDetails" placeholder="Informações relevantes" value={newClient.importantDetails} onChange={handleInputChange} className="mt-1" />
                                    </div>
                                    <div className="mt-4">
                                        <label htmlFor="restrictions" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Restrições
                                        </label>
                                        <Input type="text" id="restrictions" name="restrictions" placeholder="Restrições ou limitações" value={newClient.restrictions} onChange={handleInputChange} className="mt-1" />
                                    </div>
                                </div>

                                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                    <h2 className="text-lg font-medium mb-4">Missão, Visão e Valores</h2>
                                    <div>
                                        <label htmlFor="mission" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Missão
                                        </label>
                                        <Input type="text" id="mission" name="mission" placeholder="Missão da empresa" value={newClient.mission} onChange={handleInputChange} className="mt-1" />
                                    </div>
                                    <div className="mt-4">
                                        <label htmlFor="vision" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Visão
                                        </label>
                                        <Input type="text" id="vision" name="vision" placeholder="Visão da empresa" value={newClient.vision} onChange={handleInputChange} className="mt-1" />
                                    </div>
                                    <div className="mt-4">
                                        <label htmlFor="values" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                                            Valores
                                        </label>
                                        <Input type="text" id="values" name="values" placeholder="Valores da empresa" value={newClient.values} onChange={handleInputChange} className="mt-1" />
                                    </div>
                                </div>

                                <Button type="submit" className="w-full !mt-6" disabled={isSubmitting}>
                                    {isSubmitting ? <Ellipsis /> : "Cadastrar"}
                                </Button>
                            </form>
                        </div>
                    </>
                )
                }
            </div >
            <Footer />
        </div >
    );
}