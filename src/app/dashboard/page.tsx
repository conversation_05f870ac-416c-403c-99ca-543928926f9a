"use client";

import { Ch<PERSON><PERSON>UpDown, <PERSON>T<PERSON>, X } from "lucide-react";
import Link from "next/link";

import { useEffect, useMemo, useState } from "react";
import { Client as ImportedClient } from "../clients/page";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Header } from "../components/header";
import { Footer } from "../components/footer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card";
import { AdminDashboard } from "../components/dashboard/admin-dashboard";
import { UserDashboard } from "../components/dashboard/user-dashboard";
import { Button } from "../components/ui/button";
import Loading from "../components/ui/loading";
import { ClientNavigationModal } from "../components/client-navigation-modal";
import ClientDashboard from "../components/client-dashboard";
import { ResultsReport } from "@prisma/client";
import { Separator } from "../components/ui/separator";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "../components/ui/collapsible";
import { UpdatesInformationAdmin } from "../components/updates-information-admin";
import { UpdatesInformationOthers } from "../components/updates-information-others";
import { ClientSearchCommand } from "../components/client-search-command";
import { PointClock } from "../components/point-clock";
import { WelcomeAlert } from "../components/welcome-alert";
import { Badge } from "../components/ui/badge";

interface MonthlyPlanning {
    id: string;
    month: string | number;
    year: number;
    clientName?: string;
    clientId?: string;
    createdAt?: string;
}

interface Client extends Omit<ImportedClient, 'monthlyPlannings'> {
    id: string;
    name: string;
    monthlyPlannings?: MonthlyPlanning[];
    resultsReport?: ResultsReport[];
}

const getUserPermissions = (role: string, accessLevel: string) => {
    const accessLevels = {
        'VIEWER': 1,
        'EDITOR': 2,
        'ADMIN': 3
    };

    const numericLevel = accessLevels[accessLevel as keyof typeof accessLevels] || 1;

    return {
        canAccessDashboard: ['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(role),
        canCreatePlanning:
            ['ADMIN', 'DEVELOPER', 'DESIGNER_SENIOR', 'GENERAL_ASSISTANT'].includes(role) ||
            (['COPY', 'DESIGNER'].includes(role) && numericLevel > 2),
        canAccessFeedTasks: role === 'DESIGNER',
        isDesigner: role === 'DESIGNER' || role === 'DESIGNER_SENIOR',
        isDesignerJunior: role === 'DESIGNER_JUNIOR',
        isCopy: role === 'COPY',
        isContentCreator: role === 'DESIGNER' || role === 'DESIGNER_SENIOR' || role === 'COPY',
        isAdmin: role === 'ADMIN' || role === 'DEVELOPER' || role === 'GENERAL_ASSISTANT',
    };
};

export default function Dashboard() {
    const { data: session, status } = useSession();
    const [currentUser, setCurrentUser] = useState<{ id?: string; name?: string | null; role?: string | null; accessLevel?: string | null; client?: { id: string; name?: string } | null } | null>(null);
    const [clients, setClients] = useState<Client[]>([]);
    const [allClients, setAllClients] = useState<Client[]>([]);
    const [archivedClients, setArchivedClients] = useState<Client[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [currentTime, setCurrentTime] = useState(new Date());
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [showInformation, setShowInformation] = useState(true);
    const [clientNavigationOpen, setClientNavigationOpen] = useState(false);
    const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
    const router = useRouter();
    const [viewedClients, setViewedClients] = useState<{ id: string; name: string }[]>([]);
    const [userPermissions, setUserPermissions] = useState({
        canAccessDashboard: false,
        canCreatePlanning: false,
        canAccessFeedTasks: false,
        isDesigner: false,
        isDesignerJunior: false,
        isCopy: false,
        isContentCreator: false,
        isAdmin: false
    });

    const newsClients = useMemo(() => {
        const now = currentTime ?? new Date();
        return clients.filter((client) => {
            if (!client.createdAt) return false;
            const created = new Date(client.createdAt);
            return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear();
        });
    }, [clients, currentTime]);

    const newClientsCount = newsClients?.length ?? 0;

    const { archivedThisMonth, netChangeThisMonth } = useMemo(() => {
        const now = currentTime ?? new Date();
        const month = now.getMonth();
        const year = now.getFullYear();

        const archivedCount = archivedClients.filter((c) => {
            if (!c.archivedAt) return false;
            const archivedDate = new Date(c.archivedAt);
            return archivedDate.getMonth() === month && archivedDate.getFullYear() === year;
        }).length;

        const startOfCurrentMonth = new Date(year, month, 1);
        const endOfPrevMonth = new Date(startOfCurrentMonth.getTime() - 1);

        const combined = [...(allClients || []), ...(archivedClients || [])];

        const prevTotal = combined.filter(c => {
            if (!c.createdAt) return false;
            const created = new Date(c.createdAt);
            if (created.getTime() > endOfPrevMonth.getTime()) return false;
            if (c.archivedAt) {
                const archivedAt = new Date(c.archivedAt);
                if (archivedAt.getTime() <= endOfPrevMonth.getTime()) return false;
            }
            return true;
        }).length;

        const net = newClientsCount - archivedCount;

        let percent = 0;
        if (prevTotal > 0) {
            percent = (net / prevTotal) * 100;
        }

        const sign = net >= 0 ? '+' : '-';
        const absNet = Math.abs(net);

        const formatted = prevTotal > 0 ? `${sign}${absNet} (${percent.toFixed(1)}%)` : `${sign}${absNet}`;

        return {
            formattedNewClientsPercentage: formatted,
            archivedThisMonth: archivedCount,
            netChangeThisMonth: net
        };
    }, [currentTime, allClients, archivedClients, newClientsCount]);

    const userWorkArrangement = session?.user?.workArrangement;

    useEffect(() => {
        const storedClients = JSON.parse(localStorage.getItem("viewedClients") || "[]");
        setViewedClients(storedClients);
    }, []);

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        const refreshSession = async () => {
            if (status === "authenticated") {
                await fetch('/api/auth/session');
            }
        };

        refreshSession();
    }, [status]);

    useEffect(() => {
        if (status === "authenticated") {
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth() + 1;
            const currentYear = currentDate.getFullYear();

            fetch(`/api/clients?include=resultsReport&month=${currentMonth}&year=${currentYear}`)
                .then(async (res) => {
                    const data = await res.json().catch(() => null);
                    if (!res.ok || !Array.isArray(data)) {
                        console.error('/api/clients (month) returned unexpected:', data);
                        return [];
                    }
                    return data;
                })
                .then((arr) => setClients(arr))
                .finally(() => setIsLoading(false));

            fetch(`/api/clients?include=resultsReport`)
                .then(async (res) => {
                    const data = await res.json().catch(() => null);
                    if (!res.ok || !Array.isArray(data)) {
                        console.error('/api/clients (all) returned unexpected:', data);
                        return [];
                    }
                    return data;
                })
                .then((arr) => setAllClients(arr));

            fetch(`/api/clients?archived=true`)
                .then(async (res) => {
                    const data = await res.json().catch(() => null);
                    if (!res.ok || !Array.isArray(data)) {
                        console.error('/api/clients archived returned unexpected:', data);
                        return [];
                    }
                    return data;
                })
                .then((arr) => setArchivedClients(arr));

            const fetchUserRole = async () => {
                try {
                    if (session?.user?.email) {
                        const response = await fetch(`/api/users/${session.user.email}`);
                        if (response.ok) {
                            const user = await response.json();
                            setUserPermissions(getUserPermissions(user?.role || '', user?.accessLevel || 'VIEWER'));
                            setCurrentUser(user);
                        } else {
                            console.error("Erro ao buscar função do usuário:", await response.json());
                        }
                    }
                } catch (error) {
                    console.error("Erro ao buscar função do usuário:", error);
                } finally {
                    setIsFetchingRole(false);
                }
            };

            fetchUserRole();
        }
    }, [status, session]);

    const refreshData = () => {
        setIsLoading(true);
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth() + 1;
        const currentYear = currentDate.getFullYear();

        fetch(`/api/clients?include=resultsReport&month=${currentMonth}&year=${currentYear}`)
            .then(async (res) => {
                const data = await res.json().catch(() => null);
                if (!res.ok || !Array.isArray(data)) {
                    console.error('/api/clients (month) returned unexpected:', data);
                    return [];
                }
                return data;
            })
            .then((arr) => setClients(arr))
            .finally(() => setIsLoading(false));

        fetch(`/api/clients?include=resultsReport`)
            .then(async (res) => {
                const data = await res.json().catch(() => null);
                if (!res.ok || !Array.isArray(data)) {
                    console.error('/api/clients (all) returned unexpected:', data);
                    return [];
                }
                return data;
            })
            .then((arr) => setAllClients(arr));

        fetch(`/api/clients?archived=true`)
            .then(async (res) => {
                const data = await res.json().catch(() => null);
                if (!res.ok || !Array.isArray(data)) {
                    console.error('/api/clients archived returned unexpected:', data);
                    return [];
                }
                return data;
            })
            .then((arr) => setArchivedClients(arr));
    };

    const handleOpenClientNavigation = (clientId: string) => {
        setSelectedClientId(clientId);
        setClientNavigationOpen(true);
    };

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        const storedShowInformation = localStorage.getItem("showInformation");
        if (storedShowInformation !== null) {
            setShowInformation(JSON.parse(storedShowInformation));
        }
    }, []);

    useEffect(() => {
        localStorage.setItem("showInformation", JSON.stringify(showInformation));
    }, [showInformation]);

    const formattedDate = currentTime.toLocaleDateString("pt-BR", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
    });

    const currentMonth = currentTime.toLocaleString("pt-BR", { month: "long" });

    if (isFetchingRole || isLoading) {
        return (
            <>
                <Header />
                <div className="min-h-[80vh] flex flex-col justify-center items-center">
                    <Loading />
                </div>
                <Footer />
            </>
        );
    }

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                <div className="flex flex-col-reverse lg:flex-row justify-between gap-6">
                    <div className="flex flex-col items-start gap-4">
                        <div className="hidden md:flex gap-2 items-center">
                            <h2 className="text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800">
                                Dashboard
                            </h2>
                            <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 rounded-full">
                                v3.0
                            </Badge>
                        </div>
                        {(showInformation && session?.user.role !== "CLIENT") && (
                            <Collapsible className="">
                                <div className="flex items-end gap-10">
                                    <div>
                                        <h3 className="text-sm font-semibold">
                                            🎉 Confira as últimas atualizações!
                                        </h3>
                                        <span className="text-xs text-muted-foreground">Julho de 2025</span>
                                        <Separator className="my-2" />
                                        <p className="text-sm text-muted-foreground">
                                            Confira as últimas atualizações do sistema. Clique para saber.
                                        </p>
                                    </div>
                                    <div className="flex gap-2">
                                        <CollapsibleTrigger asChild>
                                            <Button variant="outline" size="icon">
                                                <ChevronsUpDown className="h-4 w-4" />
                                                <span className="sr-only">Toggle</span>
                                            </Button>
                                        </CollapsibleTrigger>
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => {
                                                setShowInformation(false);
                                            }}
                                        >
                                            <X className="h-4 w-4" />
                                            <span className="sr-only">Hide</span>
                                        </Button>
                                    </div>
                                </div>
                                <CollapsibleContent>
                                    <Card className="mt-4">
                                        <CardHeader>
                                            <CardTitle>
                                                O que há de novo?
                                            </CardTitle>
                                            <CardDescription>
                                                Julho de 2025
                                            </CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                            {userPermissions.isAdmin ? (
                                                <UpdatesInformationAdmin />
                                            ) : (
                                                <UpdatesInformationOthers />
                                            )}

                                            <div className="mt-4 pt-3 border-t">
                                                <p className="text-xs flex items-start gap-1">
                                                    <span>💡</span>
                                                    Mais funcionalidades serão adicionadas em breve. Fique atento às atualizações!
                                                </p>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </CollapsibleContent>
                            </Collapsible>
                        )}
                        {(clients.length > 0 && userPermissions.canAccessDashboard) && (
                            <ClientSearchCommand
                                clients={clients}
                                onClientSelect={handleOpenClientNavigation}
                            />
                        )}
                    </div>

                    <div className="flex flex-col items-end gap-4">
                        <div className="flex flex-col gap-1 items-end md:items-start lg:items-end">
                            <p className="text-sm font-medium">
                                Olá, {session?.user?.name?.split(" ")[0] || "Usuário"}! 👋🏼
                            </p>
                            <p className="text-xs text-zinc-600 dark:text-zinc-400">Hoje é {formattedDate}</p>
                        </div>
                        <div className="hidden md:flex flex-col xs:flex-row md:flex-col gap-2 items-end xs:items-start md:items-end">
                            {userPermissions.isAdmin && (
                                <Link href="/panel">
                                    <Button variant="secondary">
                                        <PanelTop />
                                        Acessar painel administrativo
                                    </Button>
                                </Link>
                            )}
                            {userWorkArrangement === "ON_SITE" && userPermissions.canAccessDashboard && (
                                <PointClock />
                            )}
                        </div>
                    </div>
                </div>

                {userPermissions.canAccessDashboard && (
                    <WelcomeAlert
                        userId={session?.user?.email || ""}
                        userName={session?.user?.name || ""}
                        version="5.0"
                    />
                )}

                {userPermissions.canAccessDashboard ? (
                    userPermissions.isAdmin ? (
                        <AdminDashboard
                            clients={clients}
                            allClients={allClients}
                            currentMonthLabel={currentMonth}
                            newsClients={newsClients}
                            archivedThisMonth={archivedThisMonth}
                            netChangeThisMonth={netChangeThisMonth}
                            viewedClients={viewedClients}
                            onClientSelect={handleOpenClientNavigation}
                            canCreatePlanning={userPermissions.canCreatePlanning}
                            onRefreshData={refreshData}
                        />
                    ) : (
                        <UserDashboard />
                    )
                ) : session?.user?.role === "CLIENT" && currentUser?.client ? (
                    <div className="mt-8 text-sm">
                        <ClientDashboard client={currentUser?.client} />
                    </div>
                ) : (
                    <div className="mt-8 text-sm">
                        <h3 className="font-bold">
                            Você não tem permissão para acessar.
                        </h3>
                        <p>
                            Entre em contato com o administrador do sistema.
                        </p>
                        <Separator className="my-4" />
                        <p>
                            Se você é um cliente da B4 Comunicação, entre em contato com nossa equipe.
                        </p>
                    </div>
                )}
            </div>
            <Footer />

            <ClientNavigationModal
                open={clientNavigationOpen}
                onOpenChange={setClientNavigationOpen}
                clientId={selectedClientId}
                clientName={viewedClients.find(client => client.id === selectedClientId)?.name}
            />
        </div>
    );
}
