"use client"

import { DriveDebugger } from "@/app/components/drive-debugger";
import { Button } from "../components/ui/button";
import Link from "next/link";
import { Home, Logs } from "lucide-react";
import { Badge } from "../components/ui/badge";
import { ThemeToggle } from "../components/ui/theme-toggle";
import { Footer } from "../components/footer";

export default function DriveDebugPage() {
    return (
        <div className="min-h-screen flex flex-col">
            <div className="container mx-auto p-8 flex-grow">
                <div className="flex flex-col-reverse lg:flex-row justify-between gap-4 lg:gap-0 mb-8 lg:mb-16">
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                        <div className="flex items-center gap-2 group">
                            <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                <Logs className="w-5 h-5 sm:w-6 sm:h-6" color="#db5743" />
                            </div>
                            <h1 className="text-sm sm:text-base lg:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                Debug de URLs do Google Drive
                            </h1>
                        </div>
                        <Badge variant="outline" className="bg-green-100 border-green-200 dark:bg-green-950 dark:border-green-300">
                            Página exclusiva para desenvolvedores
                        </Badge>
                    </div>
                    <div className="flex items-center gap-2 self-start lg:self-center">
                        <ThemeToggle />
                        <Button variant="outline" asChild>
                            <Link href="/">
                                <Home size={16} />
                                <span className="hidden sm:inline">Início</span>
                            </Link>
                        </Button>
                    </div>
                </div>
                <DriveDebugger />

                <div className="mt-14 text-center">
                    <p className="text-sm text-muted-foreground">
                        Esta página é apenas para fins de debug e teste. Não use em produção.
                    </p>
                    <p className="text-sm text-muted-foreground">
                        Ela permite testar diferentes estratégias de acesso a arquivos do Google Drive e identificar problemas com URLs problemáticas.
                    </p>
                </div>
            </div>
            <Footer />
        </div>
    );
}
