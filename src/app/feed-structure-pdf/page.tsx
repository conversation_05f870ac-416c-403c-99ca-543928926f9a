"use client"

import { FeedStructuringContent } from "@/app/components/feed-structuring-content";
import { Client } from "@prisma/client";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import "./pdf-styles.css";
import Loading from "../components/ui/loading";
import { Ellipsis } from "lucide-react";


function FeedStructurePDFContent() {
    const searchParams = useSearchParams();
    const id = searchParams.get("id");
    const month = searchParams.get("month");

    const [client, setClient] = useState<Client>();
    const [isReady, setIsReady] = useState(false);
    const [filteredIds, setFilteredIds] = useState<string[] | null>(null);

    useEffect(() => {
        if (id) {
            try {
                const ids = localStorage.getItem(`pdf-contents-${id}`);
                if (ids) {
                    setFilteredIds(JSON.parse(ids));
                }
            } catch {}

            fetch(`/api/clients/${id}?include=resultsReport.results`)
                .then(res => res.json())
                .then(data => {
                    setClient(data);

                    const readyTimeout = setTimeout(() => {
                        const images = document.querySelectorAll('img[data-pdf-image="true"]');
                        let loadedImages = 0;

                        const checkImagesLoaded = () => {
                            if (loadedImages >= images.length || images.length === 0) {
                                setIsReady(true);
                                console.log("Página do PDF pronta para impressão");
                                document.title = "PDF_READY";

                                const readySignal = document.createElement('div');
                                readySignal.id = 'pdf-ready';
                                readySignal.dataset.ready = 'true';
                                readySignal.style.display = 'none';
                                document.body.appendChild(readySignal);
                            }
                        };

                        images.forEach((img) => {
                            const imgElement = img as HTMLImageElement;
                            if (imgElement.complete) {
                                loadedImages++;
                            } else {
                                imgElement.addEventListener('load', () => {
                                    loadedImages++;
                                    checkImagesLoaded();
                                });
                                imgElement.addEventListener('error', () => {
                                    loadedImages++;
                                    checkImagesLoaded();
                                });
                            }
                        });

                        setTimeout(checkImagesLoaded, 500);
                    }, 3000);

                    return () => clearTimeout(readyTimeout);
                })
                .catch(err => {
                    console.error("Erro ao carregar dados do cliente:", err);
                });
        }
    }, [id]);

    if (!client) return (
        <div className="pdf-container">
            <Ellipsis className="animate-pulse" />
        </div>
    )

    return (
        <div
            className={`pdf-container ${isReady ? 'ready' : ''}`}
            style={{
                padding: "10px",
                fontSize: "11px",
                maxWidth: "700px",
                width: "100%",
                margin: "0 auto"
            }}
        >
            <style>{`
                .pdf-grid {
                    display: grid !important;
                    grid-template-columns: 1fr 1fr 1fr !important;
                    gap: 5px !important;
                    padding: 1rem !important;
                }
                .pdf-card {
                    break-inside: avoid;
                    page-break-inside: avoid;
                    margin-bottom: 1rem;
                }
                .pdf-image-container img {
                    max-width: 100% !important;
                    height: auto !important;
                    object-fit: contain !important;
                    display: block;
                    margin: 0 auto;
                }
            `}</style>
            <FeedStructuringContent
                clientId={id || ""}
                client={client}
                isPdfMode={true}
                initialMonth={month || undefined}
                compactView={true}
                filteredIds={filteredIds || undefined}
            />
            <div id="pdf-ready" data-ready={isReady.toString()} style={{ display: 'none' }}></div>
        </div>
    );
}

export default function FeedStructurePDF() {
    return (
        <Suspense fallback={<Loading />}>
            <FeedStructurePDFContent />
        </Suspense>
    );
}
