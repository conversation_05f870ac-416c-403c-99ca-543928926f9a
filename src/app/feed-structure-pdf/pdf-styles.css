.pdf-container {
    font-size: 11px;
    line-height: 1.2;
    max-width: 1100px;
    margin: 0 auto;
    padding: 5mm;
}

.pdf-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px !important;
    margin-top: 10px;
}

.pdf-card {
    width: 100%;
    max-width: 280px;
    margin: 0 auto;
    break-inside: avoid;
    page-break-inside: avoid;
}

.pdf-image-container {
    width: 100%;
    height: 0;
    padding-bottom: 125%;
    position: relative;
    overflow: hidden;
    background-color: #f7f7f7;
    border-radius: 4px;
}

.pdf-image-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    max-width: 100% !important;
    max-height: 100% !important;
    transform: none !important;
    transition: none !important;
}

.pdf-container img {
    image-rendering: auto;
    image-rendering: crisp-edges;
    image-rendering: -webkit-optimize-contrast;
}

.pdf-container [data-pdf-image="true"] {
    width: auto !important;
    height: auto !important;
    max-width: 100% !important;
    max-height: 100% !important;
    object-fit: contain !important;
    transform: none !important;
    transition: none !important;
}

@media print {
    body {
        margin: 0;
        padding: 0;
    }

    .pdf-container {
        padding: 3mm !important;
    }

    .pdf-grid {
        gap: 6px !important;
    }

    .pdf-card {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    h1,
    h2,
    h3,
    h4 {
        page-break-after: avoid;
    }

    h1,
    h2,
    h3 {
        margin: 0 0 5px 0 !important;
        padding: 0 !important;
    }

    .pdf-grid>*:nth-child(-n+6) {
        page-break-after: avoid;
        break-after: avoid;
    }

    .no-print {
        display: none !important;
    }

    .pdf-grid:first-of-type {
        page-break-before: avoid;
        break-before: avoid;
    }
}