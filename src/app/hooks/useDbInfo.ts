"use client"

import { useState, useEffect } from "react";

interface DbInfo {
    environment: string;
    host: string;
    database: string;
    isConnected: boolean;
}

export function useDbInfo() {
    const [dbInfo, setDbInfo] = useState<DbInfo | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchDbInfo = async () => {
            try {
                const response = await fetch("/api/db-info");

                if (!response.ok) {
                    throw new Error("Falha ao buscar informações do banco");
                }

                const data = await response.json();
                setDbInfo(data);
            } catch (err) {
                setError(err instanceof Error ? err.message : "Erro desconhecido");
                console.error("Erro ao buscar informações do banco:", err);
            } finally {
                setIsLoading(false);
            }
        };

        fetchDbInfo();
    }, []);

    return { dbInfo, isLoading, error };
}