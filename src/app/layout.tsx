import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import AuthProvider from "./providers/auth";
import ClientSelectionProvider from "./providers/client-selection";
import { Toaster } from "./components/ui/sonner";
import { ThemeProvider } from "./providers/theme";
import DynamicFeedbackButton from "./components/feedback/dynamic-feedback-button";
import InstallPromptButton from "./components/pwa/InstallPromptButton";
import ConditionalMain from "./components/conditional-main";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "B4Desk",
  manifest: "/manifest.json",
};

export function generateViewport() {
  return {
    width: "device-width",
    initialScale: 1,
    themeColor: "#000000",
  };
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="pt-br"
      className={`${geistSans.variable} ${geistMono.variable}`}
      suppressHydrationWarning
    >
      <body className="antialiased max-w-[90rem] mx-auto">
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <AuthProvider>
            <ClientSelectionProvider>
              <ConditionalMain>
                {children}
              </ConditionalMain>
              <DynamicFeedbackButton />
              <InstallPromptButton />
            </ClientSelectionProvider>
          </AuthProvider>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
