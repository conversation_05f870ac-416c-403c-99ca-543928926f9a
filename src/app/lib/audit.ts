import prisma from "./prisma";

export async function createAuditLog(
    pointRecordId: string,
    action: string,
    editedBy: string,
    field?: string,
    oldValue?: string,
    newValue?: string
) {
    await prisma.pointRecordAudit.create({
        data: {
            pointRecordId,
            action,
            field,
            oldValue,
            newValue,
            editedBy
        }
    });
}