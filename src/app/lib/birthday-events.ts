import prisma from "@/app/lib/prisma";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

/**
 * Cria ou atualiza um evento de aniversário para um proprietário
 */
export async function createOrUpdateBirthdayEvent(
    clientId: string,
    ownerName: string,
    birthDate: Date | string
) {
    try {
        const birthdayDate = new Date(birthDate);
        if (isNaN(birthdayDate.getTime())) {
            console.error("Data de nascimento inválida:", birthDate);
            return null;
        }

        let birthdayCalendar = await prisma.calendar.findFirst({
            where: {
                clientId: clientId,
                name: "Aniversários"
            }
        });

        if (!birthdayCalendar) {
            birthdayCalendar = await prisma.calendar.create({
                data: {
                    name: "Aniversários",
                    description: "Calendário de aniversários",
                    color: "#FF4785",
                    clientId: clientId
                }
            });
        }

        const month = birthdayDate.getMonth() + 1;
        const day = birthdayDate.getDate();

        const existingHoliday = await prisma.holidayEvent.findFirst({
            where: {
                calendarId: birthdayCalendar.id,
                title: `Aniversário do proprietário ${ownerName}`,
            }
        });

        if (existingHoliday) {
            await prisma.holidayEvent.update({
                where: { id: existingHoliday.id },
                data: {
                    month: month,
                    day: day,
                    description: `Data de nascimento: ${format(birthdayDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}`,
                }
            });
            return existingHoliday.id;
        } else {
            const newHoliday = await prisma.holidayEvent.create({
                data: {
                    title: `Aniversário do proprietário ${ownerName}`,
                    description: `Data de nascimento: ${format(birthdayDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })}`,
                    month: month,
                    day: day,
                    color: "#FF4785",
                    allDay: true,
                    isRecurring: true,
                    calendarId: birthdayCalendar.id
                }
            });
            return newHoliday.id;
        }
    } catch (error) {
        console.error("Erro ao criar/atualizar evento de aniversário:", error);
        return null;
    }
}

/**
 * Cria ou atualiza um calendário de aniversários para um cliente, adicionando os aniversários dos proprietários
 * @param clientId
 * @param ownerId
 */
export async function createOrUpdateBirthdayCalendar(
    clientId: string,
    ownerId?: string
) {
    try {
        const client = await prisma.client.findUnique({
            where: { id: clientId },
            include: {
                owners: true
            }
        });

        if (!client) {
            console.error(`Cliente não encontrado: ${clientId}`);
            return false;
        }

        const ownersWithBirthdate = client.owners.filter(owner => owner.birthDate);
        
        if (ownersWithBirthdate.length === 0) {
            return false;
        }

        let birthdayCalendar = await prisma.calendar.findFirst({
            where: {
                clientId: clientId,
                name: "Aniversários"
            },
            include: {
                holidays: true
            }
        });

        if (!birthdayCalendar) {
            birthdayCalendar = await prisma.calendar.create({
                data: {
                    name: "Aniversários",
                    description: "Calendário de aniversários dos proprietários",
                    color: "#FFB6C1",
                    clientId: clientId
                },
                include: {
                    holidays: true
                }
            });
        }

        for (const owner of ownersWithBirthdate) {
            if (ownerId && owner.id !== ownerId) {
                continue;
            }

            const birthDate = new Date(owner.birthDate!);
            const month = birthDate.getMonth() + 1;
            const day = birthDate.getDate();
            
            const existingHoliday = birthdayCalendar.holidays.find(h => 
                h.title === `Aniversário de ${owner.name}`
            );

            if (existingHoliday) {
                await prisma.holidayEvent.update({
                    where: { id: existingHoliday.id },
                    data: {
                        month,
                        day,
                        description: `Aniversário de ${owner.name}`
                    }
                });
            } else {
                await prisma.holidayEvent.create({
                    data: {
                        title: `Aniversário de ${owner.name}`,
                        description: `Aniversário de ${owner.name}`,
                        month,
                        day,
                        color: "#FFB6C1",
                        allDay: true,
                        isRecurring: true,
                        calendarId: birthdayCalendar.id
                    }
                });
            }
        }

        return true;
    } catch (error) {
        console.error("Erro ao criar calendário de aniversários:", error);
        return false;
    }
}

/**
 * Cria ou atualiza um evento de aniversário para a marca
 */
export async function createOrUpdateBrandAnniversaryEvent(
    clientId: string,
    brandName: string,
    anniversaryDate: Date | string
) {
    try {
        const brandAnniversaryDate = new Date(anniversaryDate);
        if (isNaN(brandAnniversaryDate.getTime())) {
            console.error("Data de aniversário da marca inválida:", anniversaryDate);
            return null;
        }

        let birthdayCalendar = await prisma.calendar.findFirst({
            where: {
                clientId: clientId,
                name: "Aniversários"
            }
        });

        if (!birthdayCalendar) {
            birthdayCalendar = await prisma.calendar.create({
                data: {
                    name: "Aniversários",
                    description: "Calendário de aniversários",
                    color: "#FF4785",
                    clientId: clientId
                }
            });
            console.log("Calendário de aniversários criado:", birthdayCalendar.id);
        }

        const month = brandAnniversaryDate.getMonth() + 1;
        const day = brandAnniversaryDate.getDate();

        const existingHoliday = await prisma.holidayEvent.findFirst({
            where: {
                calendarId: birthdayCalendar.id,
                title: `Aniversário da marca ${brandName}`,
            }
        });

        const yearOfFoundation = brandAnniversaryDate.getFullYear();
        const currentYear = new Date().getFullYear();
        const yearsOfExistence = currentYear - yearOfFoundation;
        const description = `Fundação: ${format(brandAnniversaryDate, "dd 'de' MMMM 'de' yyyy", { locale: ptBR })} - ${yearsOfExistence} anos de existência`;

        if (existingHoliday) {
            await prisma.holidayEvent.update({
                where: { id: existingHoliday.id },
                data: {
                    month: month,
                    day: day,
                    description: description,
                }
            });
            console.log("Evento de aniversário da marca atualizado:", existingHoliday.id);
            return existingHoliday.id;
        } else {
            const newHoliday = await prisma.holidayEvent.create({
                data: {
                    title: `Aniversário da marca ${brandName}`,
                    description: description,
                    month: month,
                    day: day,
                    color: "#FF8C00",
                    allDay: true,
                    isRecurring: true,
                    calendarId: birthdayCalendar.id
                }
            });
            console.log("Evento de aniversário da marca criado:", newHoliday.id);
            return newHoliday.id;
        }
    } catch (error) {
        console.error("Erro ao criar/atualizar evento de aniversário da marca:", error);
        return null;
    }
}
