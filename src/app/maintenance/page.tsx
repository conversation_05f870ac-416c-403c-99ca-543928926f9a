"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/app/components/ui/card";
import { Shield } from "lucide-react";

export default function MaintenancePage() {

    return (
        <div className="min-h-screen flex flex-col">
            <div className="container p-4 xs:p-8 flex-grow">
                <h2 className="text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800 mt-6">
                    🚧 Estamos em manutenção
                </h2>
                <div className="space-y-4 mt-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-1">
                                <Shield className="h-4 w-4" />
                                Sistema
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm text-muted-foreground">
                                Estamos trabalhando para melhorar sua experiência. Em breve você poderá voltar para o sistema. Obrigado por sua compreensão!
                            </p>
                            <p className="text-sm text-muted-foreground mt-4">
                                Se você precisar de ajuda, entre em contato com o administrador do sistema.
                            </p>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
}