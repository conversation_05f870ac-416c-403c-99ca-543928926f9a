"use client"

import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Header } from "../components/header";
import { Footer } from "../components/footer";
import { format } from "date-fns";
import { AlertCircle, MessageSquarePlus, ClipboardList, Link as LinkIcon, CheckCircle2, Ellipsis, MoveLeft } from "lucide-react";
import { ActivityInfo } from "../components/activity-info";
import { Dialog, DialogContent, DialogDescription, DialogTitle, DialogTrigger } from "../components/ui/dialog";
import { Button } from "../components/ui/button";
import { Card, CardContent } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "../components/ui/tabs";
import Link from "next/link";
import Loading from "../components/ui/loading";

interface Notification {
    id: string;
    content: string;
    type: string;
    entityId: string | null;
    entityType: string | null;
    reference?: string | null;
    isRead: boolean;
    createdAt: string;
    importance?: string;
}

export default function NotificationsPage() {
    const { data: session, status } = useSession();
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const router = useRouter();

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        const fetchNotifications = async () => {
            try {
                const response = await fetch("/api/notifications");
                if (response.ok) {
                    const data = await response.json();
                    setNotifications(data);
                }
            } catch (error) {
                console.error("Error fetching notifications:", error);
            } finally {
                setIsLoading(false);
            }
        };

        if (status === "authenticated") {
            fetchNotifications();
        }
    }, [status, session]);

    const markAllAsRead = async () => {
        try {
            const unreadNotifications = notifications.filter(n => !n.isRead);

            if (unreadNotifications.length === 0) return;

            const promises = unreadNotifications.map(notification =>
                fetch("/api/notifications", {
                    method: "PATCH",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify({ id: notification.id, isRead: true }),
                })
            );

            await Promise.all(promises);

            setNotifications(notifications.map(notification => ({
                ...notification,
                isRead: true
            })));
        } catch (error) {
            console.error("Error marking all notifications as read:", error);
        }
    };

    const getNotificationIcon = (type: string) => {
        switch (type) {
            case "new_feedback":
                return <MessageSquarePlus className="h-4 w-4 text-blue-500" />;
            case "assigned_demand":
                return <ClipboardList className="h-4 w-4 text-purple-500" />;
            case "url_added":
                return <LinkIcon className="h-4 w-4 text-green-500" />;
            default:
                return <AlertCircle className="h-4 w-4 text-muted-foreground" />;
        }
    };

    const getNotificationTitle = (type: string) => {
        switch (type) {
            case "new_feedback":
                return "Novo feedback";
            case "assigned_demand":
                return "Demanda atribuída";
            case "url_added":
                return "URL adicionada";
            default:
                return "Notificação";
        }
    };

    const getNotificationLink = (notification: Notification) => {
        if (notification.type === "url_added") {
            return `/admin/demands`;
        }

        if (notification.reference) {
            return notification.reference;
        }

        if (notification.entityType === "feedback") {
            return `/admin/feedbacks`;
        } else if (notification.entityType === "content" || notification.entityType === "general_demand") {
            return `/my-demands`;
        }
        return '#';
    };

    if (isLoading) {
        return (
            <div className="min-h-screen flex flex-col">
                <Header />
                <div className="flex-grow flex justify-center items-center">
                    <Loading />
                </div>
                <Footer />
            </div>
        );
    }

    const unreadNotifications = notifications.filter(n => !n.isRead);
    const readNotifications = notifications.filter(n => n.isRead);

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="container p-4 xs:p-8 max-w-5xl flex-grow">
                <Button
                    variant="outline"
                    onClick={() => router.push('/panel')}
                >
                    <MoveLeft className="h-4 w-4" />
                    Voltar
                </Button>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 my-6">
                    <h2 className="text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800 pb-2">
                        Notificações
                    </h2>
                    {unreadNotifications.length > 0 && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={markAllAsRead}
                            className="flex items-center gap-1 self-start sm:self-auto"
                        >
                            <CheckCircle2 className="h-4 w-4" />
                            Marcar todas como lidas
                        </Button>
                    )}
                </div>

                <Tabs defaultValue="unread">
                    <TabsList className="mb-4 flex justify-between sm:justify-start sm:w-auto overflow-x-auto">
                        <TabsTrigger value="unread" className="relative flex-1 sm:flex-none">
                            Não lidas
                            {unreadNotifications.length > 0 && (
                                <Badge variant="secondary" className="ml-2 text-xs">
                                    {unreadNotifications.length}
                                </Badge>
                            )}
                        </TabsTrigger>
                        <TabsTrigger value="read" className="flex-1 sm:flex-none">
                            Lidas
                            <Badge variant="secondary" className="ml-2 text-xs">
                                {readNotifications.length}
                            </Badge>
                        </TabsTrigger>
                        <TabsTrigger value="all" className="flex-1 sm:flex-none">
                            Todas
                            <Badge variant="secondary" className="ml-2 text-xs">
                                {notifications.length}
                            </Badge>
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="unread" className="space-y-4">
                        {unreadNotifications.length === 0 ? (
                            <div className="text-center py-16 my-8 text-muted-foreground bg-zinc-50 dark:bg-zinc-900/20 rounded-lg">
                                <p className="text-sm">Não há notificações não lidas</p>
                            </div>
                        ) : (
                            unreadNotifications.map(notification => (
                                <NotificationCard
                                    key={notification.id}
                                    notification={notification}
                                    getNotificationIcon={getNotificationIcon}
                                    getNotificationTitle={getNotificationTitle}
                                    getNotificationLink={getNotificationLink}
                                />
                            ))
                        )}
                    </TabsContent>

                    <TabsContent value="read" className="space-y-4">
                        {readNotifications.length === 0 ? (
                            <div className="text-center py-16 my-8 text-muted-foreground bg-zinc-50 dark:bg-zinc-900/20 rounded-lg">
                                <p className="text-sm">Não há notificações lidas</p>
                            </div>
                        ) : (
                            readNotifications.map(notification => (
                                <NotificationCard
                                    key={notification.id}
                                    notification={notification}
                                    getNotificationIcon={getNotificationIcon}
                                    getNotificationTitle={getNotificationTitle}
                                    getNotificationLink={getNotificationLink}
                                />
                            ))
                        )}
                    </TabsContent>

                    <TabsContent value="all" className="space-y-4">
                        {notifications.length === 0 ? (
                            <div className="text-center py-16 my-8 text-muted-foreground bg-zinc-50 dark:bg-zinc-900/20 rounded-lg">
                                <p className="text-sm">Não há notificações</p>
                            </div>
                        ) : (
                            notifications.map(notification => (
                                <NotificationCard
                                    key={notification.id}
                                    notification={notification}
                                    getNotificationIcon={getNotificationIcon}
                                    getNotificationTitle={getNotificationTitle}
                                    getNotificationLink={getNotificationLink}
                                />
                            ))
                        )}
                    </TabsContent>
                </Tabs>
            </div>
            <Footer />
        </div>
    );
}

interface NotificationCardProps {
    notification: Notification;
    getNotificationIcon: (type: string) => React.ReactNode;
    getNotificationTitle: (type: string) => string;
    getNotificationLink: (notification: Notification) => string;
}

function NotificationCard({ notification, getNotificationIcon, getNotificationTitle, getNotificationLink }: NotificationCardProps) {
    const [markAsReadCalled, setMarkAsReadCalled] = useState(false);

    const markAsRead = async () => {
        if (markAsReadCalled) return;

        try {
            setMarkAsReadCalled(true);
            await fetch("/api/notifications", {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ id: notification.id, isRead: true }),
            });
        } catch (error) {
            console.error("Error marking notification as read:", error);
        }
    };

    const [demandData, setDemandData] = useState<{ url?: string, id?: string }>({});
    const [isLoadingDemand, setIsLoadingDemand] = useState(false);
    const fetchDemandData = async () => {
        if (!notification.entityId || isLoadingDemand) return;

        setIsLoadingDemand(true);
        try {
            const apiUrl = notification.entityType === "content"
                ? `/api/contents/${notification.entityId}`
                : `/api/general-demands/${notification.entityId}`;

            const response = await fetch(apiUrl);
            if (response.ok) {
                const data = await response.json();
                setDemandData({
                    url: data.urlStructuringFeed || '',
                    id: data.id,
                });
            }
        } catch (error) {
            console.error("Error fetching demand data:", error);
        } finally {
            setIsLoadingDemand(false);
        }
    };

    if (notification.type === "url_added") {
        return (
            <Dialog onOpenChange={(open) => {
                if (open) {
                    fetchDemandData();
                    markAsRead();
                }
            }}>
                <DialogTrigger asChild>
                    <Card
                        className={`mt-4 hover:bg-zinc-50 dark:hover:bg-zinc-800 transition-colors cursor-pointer ${!notification.isRead ? 'bg-sky-50 dark:bg-sky-900/20 border-sky-200 dark:border-sky-900' : ''}`}
                    >
                        <CardContent className="p-4">
                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-2">
                                <h6 className="text-sm font-semibold text-zinc-700 dark:text-zinc-200 flex items-center gap-1.5">
                                    {getNotificationIcon(notification.type)}
                                    {getNotificationTitle(notification.type)}
                                </h6>
                                <div className="flex items-center gap-2 self-start">
                                    <p className="text-xs text-zinc-500 dark:text-zinc-400">
                                        {format(new Date(notification.createdAt), "dd/MM/yyyy HH:mm")}
                                    </p>
                                    {!notification.isRead && (
                                        <Badge
                                            variant="outline"
                                            className={`flex-shrink-0 text-[.7rem] uppercase rounded-full ${notification.importance === 'high'
                                                ? 'border-red-300 text-red-800 bg-red-50 dark:bg-red-900/20 dark:border-red-900 dark:text-red-200'
                                                : 'border-sky-300 text-sky-800 bg-sky-50 dark:bg-sky-900/20 dark:border-sky-900 dark:text-sky-200'
                                                }`}
                                        >
                                            Nova
                                        </Badge>
                                    )}
                                </div>
                            </div>
                            <p className="text-sm text-zinc-700 dark:text-zinc-300">
                                {notification.content}
                            </p>
                        </CardContent>
                    </Card>
                </DialogTrigger>
                <DialogContent className="max-w-md">
                    <DialogTitle>Detalhes da demanda</DialogTitle>
                    <DialogDescription>
                        Clique no botão abaixo visualizar mais detalhes da demanda <span className="italic">{demandData.id?.substring(0, 8).toUpperCase()}</span>, incluindo a URL do Google Drive.
                    </DialogDescription>
                    {isLoadingDemand ? (
                        <Ellipsis />
                    ) : (
                        <ActivityInfo activity={{ url: Array.isArray(demandData.url) ? demandData.url : demandData.url ? [demandData.url] : undefined }} />
                    )}
                </DialogContent>
            </Dialog>
        );
    }

    return (
        <Link href={getNotificationLink(notification)} onClick={markAsRead}>
            <Card className={`mt-4 hover:bg-zinc-50 dark:hover:bg-zinc-800 transition-colors ${!notification.isRead ? 'bg-sky-50 dark:bg-sky-900/20 border-sky-200 dark:border-sky-900' : ''}`}>
                <CardContent className="p-4">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-2">
                        <h6 className="text-sm font-semibold text-zinc-700 dark:text-zinc-200 flex items-center gap-1.5">
                            {getNotificationIcon(notification.type)}
                            {getNotificationTitle(notification.type)}
                        </h6>
                        <div className="flex items-center gap-2 self-start">
                            <p className="text-xs text-zinc-500 dark:text-zinc-400">
                                {format(new Date(notification.createdAt), "dd/MM/yyyy HH:mm")}
                            </p>
                            {!notification.isRead && (
                                <Badge
                                    variant="outline"
                                    className={`flex-shrink-0 text-[.7rem] uppercase rounded-full ${notification.importance === 'high'
                                        ? 'border-red-300 text-red-800 bg-red-50 dark:bg-red-900/20 dark:border-red-900 dark:text-red-200'
                                        : 'border-sky-300 text-sky-800 bg-sky-50 dark:bg-sky-900/20 dark:border-sky-900 dark:text-sky-200'
                                        }`}
                                >
                                    Nova
                                </Badge>
                            )}
                        </div>
                    </div>
                    <p className="text-sm text-zinc-700 dark:text-zinc-300">
                        {notification.content}
                    </p>
                </CardContent>
            </Card>
        </Link>
    );
}
