"use client";

import React, { createContext, useContext, useState } from "react";

type Client = {
    id: string;
    name?: string | null;
};

type ClientSelectionContextValue = {
    selectedClient: Client | null;
    setSelectedClient: (c: Client | null) => void;
};

const ClientSelectionContext = createContext<ClientSelectionContextValue | undefined>(undefined);

export function ClientSelectionProvider({ children }: { children: React.ReactNode }) {
    const [selectedClient, setSelectedClient] = useState<Client | null>(null);

    return (
        <ClientSelectionContext.Provider value={{ selectedClient, setSelectedClient }}>
            {children}
        </ClientSelectionContext.Provider>
    );
}

export function useClientSelection() {
    const ctx = useContext(ClientSelectionContext);
    if (!ctx) throw new Error("useClientSelection must be used within ClientSelectionProvider");
    return ctx;
}

export default ClientSelectionProvider;
