"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Off, Mail, Trash, TriangleAlert } from "lucide-react";
import { Footer } from "../components/footer";
import { Header } from "../components/header";
import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card";
import { Separator } from "../components/ui/separator";
import { Badge } from "../components/ui/badge";
import { Button } from "../components/ui/button";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "../components/ui/alert-dialog";
import { toast } from "sonner";
import Loading from "../components/ui/loading";
import { Label } from "../components/ui/label";
import { Input } from "../components/ui/input";
import Image from "next/image";

type UserAccount = {
    id: string;
    name: string;
    email: string;
    role: string;
    createdAt: string;
    accounts: { userId: string; type: string; provider: string }[];
};

export default function SettingsPage() {
    const { data: session, status } = useSession();
    const [isLoading, setIsLoading] = useState(false);
    const [userAccount, setUserAccount] = useState<UserAccount | null>(null);
    const [isChangingPassword, setIsChangingPassword] = useState(false);
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [passwordData, setPasswordData] = useState({
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
    });
    const [hasPassword, setHasPassword] = useState(false);
    const router = useRouter();

    const provider = userAccount?.accounts[0]?.provider || "Desconhecido";
    const isGoogleAccount = provider === "google";
    const isCreatePassword = isGoogleAccount && !hasPassword;

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/")
        }
    }, [status, router])

    useEffect(() => {
        if (status === "loading") return;

        const fetchUserAccount = async () => {
            setIsLoading(true);
            try {
                if (session?.user.email) {
                    const email = session.user.email;

                    const response = await fetch(`/api/users/${email}`)
                    if (response.ok) {
                        const userAccount = await response.json();
                        setUserAccount(userAccount)
                        setHasPassword(!!userAccount.hasPassword)
                    } else {
                        console.error(`Failed to fetch user account. Status: ${response.status}`)
                    }
                } else {
                    console.error("No email found in session.")
                }
            } catch (error) {
                console.error("Error fetching user account:", error)
            } finally {
                setIsLoading(false)
            }
        }

        fetchUserAccount()
    }, [session, status])

    const handlePasswordChange = async (e: React.FormEvent) => {
        e.preventDefault();

        if (passwordData.newPassword !== passwordData.confirmPassword) {
            toast.error("A nova senha e confirmação não coincidem");
            return;
        }

        if (passwordData.newPassword.length < 6) {
            toast.error("A nova senha deve ter pelo menos 6 caracteres");
            return;
        }

        setIsChangingPassword(true);

        try {
            const payload = isCreatePassword
                ? { newPassword: passwordData.newPassword }
                : {
                    currentPassword: passwordData.currentPassword,
                    newPassword: passwordData.newPassword
                };

            const response = await fetch(`/api/users/${userAccount?.email}/password`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(payload),
            });

            if (response.ok) {
                toast.success(isCreatePassword ? "Senha cadastrada com sucesso!" : "Senha alterada com sucesso!");
                setPasswordData({ currentPassword: "", newPassword: "", confirmPassword: "" });
                if (isCreatePassword) {
                    setHasPassword(true);
                }
            } else {
                const errorData = await response.json().catch(() => null);
                const errorMessage = errorData?.error || "Erro ao alterar senha";
                toast.error(errorMessage);
            }
        } catch (error) {
            console.error("Erro ao alterar senha:", error);
            toast.error("Erro interno do servidor");
        } finally {
            setIsChangingPassword(false);
        }
    };

    const handleDeleteAccountClick = async () => {
        if (!userAccount) return;

        try {
            const response = await fetch(`/api/users/${userAccount.email}`, {
                method: "DELETE",
            });

            if (response.ok) {
                toast.success("Conta deletada com sucesso!");
                router.push("/");
            } else {
                const errorData = await response.json().catch(() => null);
                const errorMessage = errorData?.error || response.statusText;
                throw new Error(`Erro ao excluir conta: ${errorMessage} (${response.status})`);
            }
        } catch (error) {
            console.error("Erro ao excluir conta:", error);
            toast.error(`Erro ao excluir conta: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
        }
    };

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isLoading || !userAccount ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : (
                    <div className="flex flex-col gap-4">
                        <h2 className="mb-4 text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800">
                            Configurações
                        </h2>

                        <Card>
                            <CardHeader>
                                <CardTitle>Minha conta</CardTitle>
                                <CardDescription>Informações sobre sua conta</CardDescription>
                            </CardHeader>
                            <Separator />
                            <CardContent>
                                <div className="mt-4">
                                    <h4 className="text-sm font-semibold">{userAccount.name}</h4>
                                    <div className="flex items-center gap-2 my-2 text-sm">
                                        <p className="border-b">E-mail cadastrado:</p>
                                        <Badge variant="secondary" className="break-all">
                                            {userAccount.email}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-2 my-2 text-sm">
                                        <p className="border-b">Conta criada em:</p>
                                        <Badge variant="secondary">
                                            {new Date(userAccount.createdAt).toLocaleDateString()}
                                        </Badge>
                                    </div>
                                    <Separator className="my-4" />
                                    <div className="flex flex-col items-start gap-2 my-2 text-sm">
                                        <p className="border-b">Provedor da conta</p>
                                        <div>
                                            <Card className="bg-zinc-100 dark:bg-zinc-900 rounded-sm">
                                                <CardContent
                                                    className="flex items-start gap-4 p-4"
                                                >
                                                    {provider === "google" ? (
                                                        <>
                                                            <div
                                                                className="border rounded-sm p-2 bg-white dark:bg-zinc-950"
                                                            >
                                                                <Image
                                                                    src="/google7123025.svg"
                                                                    alt="Google"
                                                                    className="w-8 h-8"
                                                                    width={32}
                                                                    height={32} />
                                                            </div>
                                                            <div className="text-sm">
                                                                <p>Google</p>
                                                                <p className="text-muted-foreground">
                                                                    {userAccount.name}
                                                                </p>
                                                            </div>
                                                        </>
                                                    ) : (
                                                        <><div className="border rounded-sm p-2">
                                                            <Mail className="text-foreground" />
                                                        </div>
                                                            <div className="text-sm">
                                                                <p>E-mail</p>
                                                                <p className="text-muted-foreground break-all">
                                                                    {userAccount.email}
                                                                </p>
                                                            </div></>
                                                    )}
                                                </CardContent>
                                            </Card>
                                        </div>
                                    </div>
                                </div>
                                <Separator className="my-4" />
                                <div>
                                    <h5 className="text-sm font-semibold">
                                        {isCreatePassword ? "Cadastrar senha" : "Alterar senha"}
                                    </h5>
                                    <p className="text-xs text-muted-foreground mb-2">
                                        {isCreatePassword
                                            ? "Cadastre uma senha para poder fazer login com e-mail e senha também"
                                            : "Altere sua senha atual por uma nova"
                                        }
                                    </p>

                                    <form onSubmit={handlePasswordChange} className="space-y-4 max-w-md">
                                        {(!isGoogleAccount || hasPassword) && (
                                            <div className="!m-0">
                                                <Label htmlFor="currentPassword" className="text-xs">
                                                    Senha atual
                                                </Label>
                                                <div className="relative">
                                                    <Input
                                                        id="currentPassword"
                                                        type={showCurrentPassword ? "text" : "password"}
                                                        value={passwordData.currentPassword}
                                                        onChange={(e) => setPasswordData(prev => ({
                                                            ...prev,
                                                            currentPassword: e.target.value
                                                        }))}
                                                        required
                                                    />
                                                    <Button
                                                        type="button"
                                                        variant="ghost"
                                                        size="sm"
                                                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                                    >
                                                        {showCurrentPassword ? (
                                                            <EyeOff className="h-4 w-4" />
                                                        ) : (
                                                            <Eye className="h-4 w-4" />
                                                        )}
                                                    </Button>
                                                </div>
                                            </div>
                                        )}

                                        <div className="!m-0">
                                            <Label htmlFor="newPassword" className="text-xs">
                                                Nova senha
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="newPassword"
                                                    type={showNewPassword ? "text" : "password"}
                                                    value={passwordData.newPassword}
                                                    onChange={(e) => setPasswordData(prev => ({
                                                        ...prev,
                                                        newPassword: e.target.value
                                                    }))}
                                                    required
                                                    minLength={6}
                                                />
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                    onClick={() => setShowNewPassword(!showNewPassword)}
                                                >
                                                    {showNewPassword ? (
                                                        <EyeOff className="h-4 w-4" />
                                                    ) : (
                                                        <Eye className="h-4 w-4" />
                                                    )}
                                                </Button>
                                            </div>
                                        </div>

                                        <div className="!m-0">
                                            <Label htmlFor="confirmPassword" className="text-xs">
                                                Confirmar nova senha
                                            </Label>
                                            <div className="relative">
                                                <Input
                                                    id="confirmPassword"
                                                    type={showConfirmPassword ? "text" : "password"}
                                                    value={passwordData.confirmPassword}
                                                    onChange={(e) => setPasswordData(prev => ({
                                                        ...prev,
                                                        confirmPassword: e.target.value
                                                    }))}
                                                    required
                                                    minLength={6}
                                                />
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="sm"
                                                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                                >
                                                    {showConfirmPassword ? (
                                                        <EyeOff className="h-4 w-4" />
                                                    ) : (
                                                        <Eye className="h-4 w-4" />
                                                    )}
                                                </Button>
                                            </div>
                                        </div>

                                        <Button
                                            type="submit"
                                            disabled={isChangingPassword}
                                            className="w-full xs:w-auto"
                                        >
                                            {isChangingPassword ? (
                                                <Ellipsis />
                                            ) : (
                                                isCreatePassword ? "Cadastrar senha" : "Alterar senha"
                                            )}
                                        </Button>
                                    </form>
                                </div>
                            </CardContent>
                        </Card>

                        {userAccount.role !== "CLIENT" && (
                            <Card>
                                <CardHeader>
                                    <CardTitle className="text-red-600 flex items-center gap-2">
                                        <TriangleAlert size={18} />
                                        <span>Zona de perigo</span>
                                    </CardTitle>
                                    <CardDescription>
                                        As ações feitas não poderão ser desfeitas
                                    </CardDescription>
                                </CardHeader>
                                <Separator />
                                <CardContent>
                                    <div className="mt-4 flex flex-col text-sm">
                                        <h4 className="font-semibold">Excluir minha conta</h4>
                                        <p className="text-muted-foreground mb-2">Seus dados serão excluídos permanentemente</p>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button variant="outline" className="text-red-600 w-full xs:w-24 !hover:brightness-125">
                                                    <Trash />
                                                    Excluir
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Excluir minha conta</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        Deseja realmente excluir sua conta? Essa ação não poderá ser desfeita.
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeleteAccountClick()}>
                                                        Continuar
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                    </div>
                )}
            </div>
            <Footer />
        </div>
    );
}