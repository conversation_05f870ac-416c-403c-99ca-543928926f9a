"use client"

import { useState, useEffect } from "react";
import { Footer } from "../components/footer";
import { Header } from "../components/header";
import { format } from "date-fns";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "../components/ui/tabs";
import { Bell, FileText, ClipboardList, Clock, AlertCircle, CheckCircle, MoveLeft } from "lucide-react";
import { Button } from "../components/ui/button";
import Link from "next/link";
import Loading from "../components/ui/loading";
import { NotAllowed } from "../components/not-allowed";

interface Activity {
    id: string;
    type: 'notification' | 'content' | 'demand';
    title: string;
    description: string;
    timestamp: string;
    relatedId?: string | null;
    relatedType?: string | null;
    importance: string;
    isRead?: boolean;
    reference?: string | null;
    assignedTo?: string;
    dueDate?: string;
}

export default function UserActivityPage() {
    const { status, data: session } = useSession();
    const [activities, setActivities] = useState<Activity[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const router = useRouter();
    const [userRole, setUserRole] = useState('');

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        setUserRole(user?.role || '');
                    }
                }
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
            }
        };

        fetchUserRole();
    }, [session]);

    useEffect(() => {
        const fetchActivities = async () => {
            try {
                const response = await fetch("/api/user-activity");
                if (response.ok) {
                    const data = await response.json();
                    setActivities(data);
                }
            } catch (error) {
                console.error("Erro ao buscar atividades:", error);
            } finally {
                setIsLoading(false);
            }
        };

        if (status === "authenticated") {
            fetchActivities();
        }
    }, [status]);

    const getActivityIcon = (activity: Activity) => {
        switch (activity.type) {
            case "notification":
                return <Bell className="h-3.5 w-3.5 xs:h-4 xs:w-4 text-blue-500" />;
            case "content":
                return <FileText className="h-3.5 w-3.5 xs:h-4 xs:w-4 text-green-500" />;
            case "demand":
                return <ClipboardList className="h-3.5 w-3.5 xs:h-4 xs:w-4 text-purple-500" />;
            default:
                return <AlertCircle className="h-3.5 w-3.5 xs:h-4 xs:w-4 text-muted-foreground" />;
        }
    };

    const getActivityLink = (activity: Activity) => {
        if (activity.type === "notification" && activity.reference) {
            return activity.reference;
        }

        if (activity.type === "content" || activity.type === "demand") {
            return "/my-demands";
        }

        return '#';
    };

    const notifications = activities.filter(a => a.type === 'notification');
    const contents = activities.filter(a => a.type === 'content');
    const demands = activities.filter(a => a.type === 'demand');

    if (isLoading) {
        return (
            <div className="min-h-screen flex flex-col">
                <Header />
                <div className="flex-grow flex justify-center items-center">
                    <Loading />
                </div>
                <Footer />
            </div>
        );
    }

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="container p-4 xs:p-8 max-w-5xl flex-grow">
                {userRole !== 'VIEWER' ? (
                    <><Button
                        variant="outline"
                        onClick={() => router.push('/system')}
                    >
                        <MoveLeft className="h-3.5 w-3.5 xs:h-4 xs:w-4" />
                        Voltar
                    </Button>
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 my-6">
                            <h2 className="text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800 pb-2">
                                Atividades
                            </h2>
                        </div>
                        <Tabs defaultValue="all">
                            <TabsList className="mb-4 flex justify-between sm:justify-start sm:w-auto overflow-x-auto">
                                <TabsTrigger value="all" className="flex-1 sm:flex-none">
                                    Todas
                                    <Badge variant="secondary" className="ml-2 text-xs">
                                        {activities.length}
                                    </Badge>
                                </TabsTrigger>
                                <TabsTrigger value="notifications" className="relative flex-1 sm:flex-none">
                                    Notificações
                                    <Badge variant="secondary" className="ml-2 text-xs">
                                        {notifications.length}
                                    </Badge>
                                </TabsTrigger>
                                <TabsTrigger value="contents" className="flex-1 sm:flex-none">
                                    Conteúdos
                                    <Badge variant="secondary" className="ml-2 text-xs">
                                        {contents.length}
                                    </Badge>
                                </TabsTrigger>
                                <TabsTrigger value="demands" className="flex-1 sm:flex-none">
                                    Demandas
                                    <Badge variant="secondary" className="ml-2 text-xs">
                                        {demands.length}
                                    </Badge>
                                </TabsTrigger>
                            </TabsList>

                            <TabsContent value="all" className="space-y-4">
                                {activities.length === 0 ? (
                                    <div className="text-center py-16 my-8 text-muted-foreground bg-zinc-50 dark:bg-zinc-900/20 rounded-lg">
                                        <p className="text-sm">Nenhuma atividade encontrada</p>
                                    </div>
                                ) : (
                                    activities.map(activity => (
                                        <ActivityCard
                                            key={activity.id}
                                            activity={activity}
                                            getActivityIcon={getActivityIcon}
                                            getActivityLink={getActivityLink} />
                                    ))
                                )}
                            </TabsContent>

                            <TabsContent value="notifications" className="space-y-4">
                                {notifications.length === 0 ? (
                                    <div className="text-center py-16 my-8 text-muted-foreground bg-zinc-50 dark:bg-zinc-900/20 rounded-lg">
                                        <p className="text-sm">Nenhuma notificação encontrada</p>
                                    </div>
                                ) : (
                                    notifications.map(activity => (
                                        <ActivityCard
                                            key={activity.id}
                                            activity={activity}
                                            getActivityIcon={getActivityIcon}
                                            getActivityLink={getActivityLink} />
                                    ))
                                )}
                            </TabsContent>

                            <TabsContent value="contents" className="space-y-4">
                                {contents.length === 0 ? (
                                    <div className="text-center py-16 my-8 text-muted-foreground bg-zinc-50 dark:bg-zinc-900/20 rounded-lg">
                                        <p className="text-sm">Nenhum conteúdo encontrado</p>
                                    </div>
                                ) : (
                                    contents.map(activity => (
                                        <ActivityCard
                                            key={activity.id}
                                            activity={activity}
                                            getActivityIcon={getActivityIcon}
                                            getActivityLink={getActivityLink}
                                        />
                                    ))
                                )}
                            </TabsContent>

                            <TabsContent value="demands" className="space-y-4">
                                {demands.length === 0 ? (
                                    <div className="text-center py-16 my-8 text-muted-foreground bg-zinc-50 dark:bg-zinc-900/20 rounded-lg">
                                        <p className="text-sm">Nenhuma demanda encontrada</p>
                                    </div>
                                ) : (
                                    demands.map(activity => (
                                        <ActivityCard
                                            key={activity.id}
                                            activity={activity}
                                            getActivityIcon={getActivityIcon}
                                            getActivityLink={getActivityLink} />
                                    ))
                                )}
                            </TabsContent>
                        </Tabs></>
                ) : (
                    <NotAllowed
                        page='/'
                    />
                )}
            </div>
            <Footer />
        </div>
    );
}

interface ActivityCardProps {
    activity: Activity;
    getActivityIcon: (activity: Activity) => React.ReactNode;
    getActivityLink: (activity: Activity) => string;
}

function ActivityCard({ activity, getActivityIcon, getActivityLink }: ActivityCardProps) {
    const getStatusBadge = (activity: Activity) => {
        const statusMatch = activity.description.match(/Status: ([^|]+)(\||$)/);
        const status = statusMatch ? statusMatch[1].trim() : "";

        switch (status.toLowerCase()) {
            case "concluído":
            case "concluida":
            case "completo":
                return (
                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300">
                        <CheckCircle className="h-2.5 w-2.5 xs:h-3 xs:w-3 mr-1" />
                        {status}
                    </Badge>
                );
            case "pendente":
                return (
                    <Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300">
                        <Clock className="h-2.5 w-2.5 xs:h-3 xs:w-3 mr-1" />
                        {status}
                    </Badge>
                );
            default:
                return status ? (
                    <Badge className="bg-zinc-100 text-zinc-800 dark:bg-zinc-800 dark:text-zinc-300">
                        {status}
                    </Badge>
                ) : null;
        }
    };

    const getHighlightClass = () => {
        if (activity.type === 'notification' && activity.isRead === false) {
            return 'bg-sky-50 dark:bg-sky-900/20 border-sky-200 dark:border-sky-900';
        }

        if (activity.importance === 'high') {
            return 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-900';
        }

        return '';
    };

    const getTypeActivity = (activity: Activity) => {
        switch (activity.type) {
            case "notification":
                return "Notificação";
            case "content":
                return "Demanda de conteúdo";
            case "demand":
                return "Demanda pontual";
            default:
                return "Atividade";
        }
    };

    return (
        <Link href={getActivityLink(activity)}>
            <Card className={`mt-4 hover:bg-zinc-50 dark:hover:bg-zinc-800 transition-colors ${getHighlightClass()}`}>
                <CardContent className="p-4">
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-2">
                        <h6 className="text-sm font-semibold text-zinc-700 dark:text-zinc-200 flex items-start gap-2">
                            <span className="flex-shrink-0 mt-1">{getActivityIcon(activity)}</span>
                            <span className="break-words">
                                {activity.type === 'notification' ? (
                                    activity.title
                                ) : (
                                    `Demanda atribuída para ${activity.assignedTo}`
                                )}
                            </span>
                        </h6>
                        <div className="flex items-center gap-2 self-start flex-wrap">
                            {getStatusBadge(activity)}
                            <p className="text-[0.65rem] xs:text-xs text-zinc-500 dark:text-zinc-400">
                                {format(new Date(activity.timestamp), "dd/MM/yyyy HH:mm")}
                            </p>
                            {activity.type === 'notification' && !activity.isRead && (
                                <Badge
                                    variant="outline"
                                    className="flex-shrink-0 text-[.6rem] xs:text-[.7rem] uppercase rounded-full border-sky-300 text-sky-800 bg-sky-50 dark:bg-sky-900/20 dark:border-sky-900 dark:text-sky-200"
                                >
                                    Nova
                                </Badge>
                            )}
                            {activity.importance === 'high' && (
                                <Badge
                                    variant="outline"
                                    className="flex-shrink-0 text-[.6rem] xs:text-[.7rem] uppercase rounded-full border-red-300 text-red-800 bg-red-50 dark:bg-red-900/20 dark:border-red-900 dark:text-red-200"
                                >
                                    Importante
                                </Badge>
                            )}
                        </div>
                    </div>
                    <p className="text-xs xs:text-sm text-zinc-700 dark:text-zinc-300">
                        {activity.type === 'notification' ? (
                            null
                        ) : (
                            `${getTypeActivity(activity)} ${activity.id.substring(0, 8).toUpperCase()}`
                        )}
                    </p>
                    {activity.description && (
                        <p className="text-[0.65rem] xs:text-xs text-zinc-500 dark:text-zinc-400 mt-2">
                            {activity.type === 'notification' ? null : `Atribuído para ${activity.assignedTo}`}
                        </p>
                    )}
                </CardContent>
            </Card>
        </Link>
    );
}