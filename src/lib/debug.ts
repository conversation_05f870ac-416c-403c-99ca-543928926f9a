export const isDevelopment = process.env.NODE_ENV === 'development';

export const debugLog = (message: string, data?: unknown) => {
    if (isDevelopment) {
        if (data) {
            console.log(message, data);
        } else {
            console.log(message);
        }
    }
};

export const errorLog = (message: string, data?: unknown) => {
    if (isDevelopment) {
        if (data) {
            console.error(message, data);
        } else {
            console.error(message);
        }
    }
};
