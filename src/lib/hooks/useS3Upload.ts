/* Hook utilitário para obter presigned URL e fazer upload diretamente ao S3.
    Retorna uma função upload(file, options) que resolve com { key, bucket, publicUrl }.
*/
export type UploadResult = {
    key: string;
    bucket: string;
    publicUrl?: string;
};

export type UploadOptions = {
    folder?: string;
    onProgress?: (percent: number) => void;
};

export async function getPresignedUrl(filename: string, contentType?: string, folder?: string) {
    const res = await fetch('/api/uploads/presigned', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filename, contentType, folder }),
    });

    if (!res.ok) {
        const body = await res.json().catch(() => ({}));
        throw new Error(body?.error || `Failed to get presigned url (${res.status})`);
    }

    return res.json();
}

export async function uploadToS3(file: File, options?: UploadOptions): Promise<UploadResult & { presignedUrl?: string }> {
    const presign = await getPresignedUrl(file.name, file.type || undefined, options?.folder);

    const url: string = presign.url;
    const key: string = presign.key;
    const bucket: string = presign.bucket;

    await new Promise<void>((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('PUT', url);
        const contentType = file.type || 'application/octet-stream';
        try {
            xhr.setRequestHeader('Content-Type', contentType);
        } catch {
        }

        xhr.upload.onprogress = function (ev) {
            if (ev.lengthComputable && options?.onProgress) {
                const percent = Math.round((ev.loaded / ev.total) * 100);
                options.onProgress(percent);
            }
        };

        xhr.onload = function () {
            if (xhr.status >= 200 && xhr.status < 300) {
                resolve();
            } else {
                const respText = xhr.responseText || '';
                const headers = xhr.getAllResponseHeaders ? xhr.getAllResponseHeaders() : '';
                const msg = `Upload failed with status ${xhr.status}. Response: ${respText}`;
                type EnrichedError = Error & {
                    status?: number;
                    responseText?: string;
                    responseHeaders?: string;
                };

                const error = new Error(msg) as EnrichedError;
                error.status = xhr.status;
                error.responseText = respText;
                error.responseHeaders = headers;
                reject(error);
            }
        };

        xhr.onerror = function () {
            reject(new Error('Network error during upload'));
        };

        xhr.send(file);
    });

    const publicBase = process.env.NEXT_PUBLIC_S3_BASE_URL;
    const encodedKey = key.split('/').map(encodeURIComponent).join('/');
    const publicUrl = publicBase
        ? `${publicBase.replace(/\/$/, '')}/${encodedKey}`
        : `https://${bucket}.s3.amazonaws.com/${encodedKey}`;

    let presignedUrl: string | undefined = undefined
    try {
        const res = await fetch('/api/uploads/presigned-get', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ key }),
        })
        if (res.ok) {
            const data = await res.json().catch(() => null)
            presignedUrl = data?.url
        }
    } catch { }

    return { key, bucket, publicUrl, presignedUrl };
}
