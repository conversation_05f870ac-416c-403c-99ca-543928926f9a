import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Verifica se uma demanda é considerada pendente baseado em seu status
 * @param demand A demanda a ser verificada
 * @returns Verdadeiro se a demanda estiver em um estado pendente
 */
export function isPendingDemand(demand: { status?: string; archived?: boolean; id?: string }) {
  const pendingStatuses = [
    "pendente",
    "em andamento",
    "estruturação de feed",
    "feed estruturado",
    "repassado",
    "alteração",
  ];
  
  const status = demand.status || '';
  const isStatusPending = pendingStatuses.includes(status);
  const isNotArchived = !demand.archived;
  const result = isStatusPending && isNotArchived;
  
  return result;
}
