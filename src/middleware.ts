import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
    const isMaintenance = false;

    const pathname = request.nextUrl.pathname;

    if (
        isMaintenance &&
        pathname !== '/maintenance' &&
        !pathname.startsWith('/_next') &&
        !pathname.startsWith('/api') &&
        !pathname.includes('.') // Ignora arquivos como favicon.ico
    ) {
        return NextResponse.redirect(new URL('/maintenance', request.url));
    }

    return NextResponse.next();
}
