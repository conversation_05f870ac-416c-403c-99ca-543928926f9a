export interface Calendar {
    id: string;
    name: string;
    description?: string;
    color?: string;
    clientId: string;
    createdAt: Date | string;
    updatedAt: Date | string;
    archived: boolean;
    events?: Event[];
    holidays?: HolidayEvent[];
    client?: {
        id: string;
        name: string;
    };
}

export interface Event {
    id: string;
    title: string;
    description?: string;
    startDate: Date | string;
    endDate?: Date | string;
    allDay: boolean;
    location?: string;
    calendarId: string;
    createdAt: Date | string;
    updatedAt: Date | string;
    calendar?: {
        id: string;
        name: string;
        color?: string;
        client?: {
            id: string;
            name: string;
        };
    };
}

export interface HolidayEvent {
    id: string;
    title: string;
    description?: string;
    month: number;
    day?: number;
    dayNotFixed?: string;
    color?: string;
    allDay: boolean;
    isRecurring: boolean;
    calendarId: string;
    createdAt: Date | string;
    updatedAt: Date | string;
    calendar?: {
        id: string;
        name: string;
        color?: string;
        client?: {
            id: string;
            name: string;
        };
    };
}
