import "next-auth";

declare module "next-auth" {
    interface Session {
        user: {
            id?: string;
            name?: string | null;
            email?: string | null;
            image?: string | null;
            role?: string | null;
            accessLevel?: string | null;
            workArrangement?: "ON_SITE" | "REMOTE" | "HYBRID";
            clientId?: string | null;
        };
    }

    interface User {
        id: string;
        email: string;
        name?: string | null;
        image?: string | null;
        role?: string;
        accessLevel?: string;
        workArrangement?: "ON_SITE" | "REMOTE" | "HYBRID";
        clientId?: string;
    }
}

declare module "next-auth/jwt" {
    interface JWT {
        role?: string;
        accessLevel?: string;
        workArrangement?: "ON_SITE" | "REMOTE" | "HYBRID";
        clientId?: string;
    }
}
